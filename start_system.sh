#!/bin/bash

# Han AttendanceOS 系統啟動腳本
# 解決登錄問題的完整啟動方案

echo "🚀 Han AttendanceOS 系統啟動中..."
echo "========================================"

# 檢查端口是否被佔用
check_port() {
    local port=$1
    local service=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  端口 $port ($service) 已被佔用，正在終止..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# 清理端口
echo "🧹 清理端口..."
check_port 7072 "後端"
check_port 7075 "前端"

# 啟動後端服務
echo "🔧 啟動後端服務 (端口 7072)..."
cd backend
python app.py &
BACKEND_PID=$!
echo "後端 PID: $BACKEND_PID"

# 等待後端啟動
echo "⏳ 等待後端服務啟動..."
sleep 5

# 檢查後端是否正常
if curl -s http://localhost:7072/api/health > /dev/null; then
    echo "✅ 後端服務啟動成功"
else
    echo "❌ 後端服務啟動失敗"
    exit 1
fi

# 啟動前端服務
echo "🎨 啟動前端服務 (端口 7075)..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!
echo "前端 PID: $FRONTEND_PID"

# 等待前端啟動
echo "⏳ 等待前端服務啟動..."
sleep 10

# 檢查前端是否正常
if curl -s http://localhost:7075 > /dev/null; then
    echo "✅ 前端服務啟動成功"
else
    echo "❌ 前端服務啟動失敗"
    exit 1
fi

echo ""
echo "🎉 系統啟動完成！"
echo "========================================"
echo "📊 服務狀態："
echo "   後端服務: http://localhost:7072"
echo "   前端服務: http://localhost:7075"
echo ""
echo "🔑 管理員登錄："
echo "   URL: http://localhost:7075/admin/login"
echo "   帳號: admin"
echo "   密碼: admin123"
echo ""
echo "🧹 如果登錄有問題，請訪問："
echo "   http://localhost:7075/force-clear-cache.html"
echo ""
echo "⚠️  按 Ctrl+C 停止所有服務"
echo "========================================"

# 保存 PID 到文件
echo $BACKEND_PID > .backend.pid
echo $FRONTEND_PID > .frontend.pid

# 等待用戶中斷
trap 'echo ""; echo "🛑 正在停止服務..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; rm -f .backend.pid .frontend.pid; echo "✅ 所有服務已停止"; exit 0' INT

# 保持腳本運行
wait
