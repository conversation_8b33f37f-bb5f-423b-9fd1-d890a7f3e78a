#!/bin/bash

# 一鍵重置 Han AttendanceOS 系統
# 解決所有登錄和服務問題

echo "🔄 正在重置 Han AttendanceOS 系統..."
echo "========================================"

# 1. 停止所有相關進程
echo "🛑 停止所有服務..."
pkill -f "python app.py" 2>/dev/null
pkill -f "next dev" 2>/dev/null
pkill -f "npm run dev" 2>/dev/null

# 等待進程完全停止
sleep 3

# 2. 清理端口
echo "🧹 清理端口..."
lsof -ti:7072 | xargs kill -9 2>/dev/null || true
lsof -ti:7075 | xargs kill -9 2>/dev/null || true

# 等待端口釋放
sleep 2

# 3. 啟動後端
echo "🔧 啟動後端服務..."
cd /Users/<USER>/2024newdev/attend_next/backend
nohup python app.py > backend.log 2>&1 &
BACKEND_PID=$!

# 等待後端啟動
echo "⏳ 等待後端啟動..."
for i in {1..20}; do
    if curl -s http://localhost:7072/api/health > /dev/null 2>&1; then
        echo "✅ 後端服務啟動成功 (PID: $BACKEND_PID)"
        break
    fi
    sleep 1
done

# 4. 啟動前端
echo "🎨 啟動前端服務..."
cd /Users/<USER>/2024newdev/attend_next/frontend
nohup npm run dev > frontend.log 2>&1 &
FRONTEND_PID=$!

# 等待前端啟動
echo "⏳ 等待前端啟動..."
for i in {1..30}; do
    if curl -s http://localhost:7075 > /dev/null 2>&1; then
        echo "✅ 前端服務啟動成功 (PID: $FRONTEND_PID)"
        break
    fi
    sleep 1
done

# 5. 驗證服務
echo "🔍 驗證服務狀態..."
if curl -s http://localhost:7072/api/health > /dev/null 2>&1; then
    echo "✅ 後端服務正常"
else
    echo "❌ 後端服務異常"
fi

if curl -s http://localhost:7075 > /dev/null 2>&1; then
    echo "✅ 前端服務正常"
else
    echo "❌ 前端服務異常"
fi

echo ""
echo "🎉 系統重置完成！"
echo "========================================"
echo "📊 服務信息："
echo "   後端: http://localhost:7072"
echo "   前端: http://localhost:7075"
echo "   管理員登錄: http://localhost:7075/admin/login"
echo ""
echo "🔑 登錄信息："
echo "   帳號: admin"
echo "   密碼: admin123"
echo ""
echo "🧹 如果仍有登錄問題："
echo "   1. 訪問: http://localhost:7075/force-clear-cache.html"
echo "   2. 或按 Ctrl+Shift+R 強制刷新"
echo "========================================"
