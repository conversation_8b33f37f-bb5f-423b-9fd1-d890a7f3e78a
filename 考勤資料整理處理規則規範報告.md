# 考勤資料整理處理規則規範報告

## 📋 系統概述

**系統名稱**: Han AttendanceOS - 考勤資料整理模組  
**版本**: v1.0  
**建立日期**: 2025-06-16  
**負責人**: 系統管理員  

## 🎯 功能目標

考勤資料整理模組旨在自動化處理員工考勤記錄，包括計算遲到、早退、加班時間，整合請假資料，並更新考勤狀態，確保考勤數據的準確性和完整性。

## 📊 處理範圍

### 1. 日期範圍設定
- **支援範圍**: 任意日期區間
- **快速選擇**: 昨天、最近7天、最近30天、本月
- **限制**: 建議單次處理不超過3個月的資料
- **換日規則**: 採用6點換日制（凌晨6點為新一天開始）

### 2. 員工範圍設定
- **所有員工**: 處理系統中所有啟用狀態的員工
- **指定部門**: 可選擇一個或多個部門的員工
- **指定員工**: 可選擇特定的員工進行處理
- **狀態過濾**: 僅處理狀態為「啟用」的員工記錄

## ⚙️ 處理選項

### 1. 計算遲到時間 (calculate_late_early)
**功能描述**: 根據員工班表和實際打卡時間計算遲到分鐘數

**處理規則**:
- 比較實際上班打卡時間與班表規定上班時間
- 容許時間: 可設定容許遲到分鐘數（預設5分鐘）
- 計算公式: `遲到分鐘 = MAX(0, 實際打卡時間 - 班表上班時間 - 容許分鐘)`
- 特殊情況: 
  - 未打卡視為遲到（記錄為異常）
  - 請假期間不計算遲到
  - 週末和假日不計算遲到

**輸出結果**: 更新 `attendance` 表的 `late_minutes` 欄位

### 2. 計算早退時間 (calculate_late_early)
**功能描述**: 根據員工班表和實際打卡時間計算早退分鐘數

**處理規則**:
- 比較實際下班打卡時間與班表規定下班時間
- 容許時間: 可設定容許早退分鐘數（預設5分鐘）
- 計算公式: `早退分鐘 = MAX(0, 班表下班時間 - 實際打卡時間 - 容許分鐘)`
- 特殊情況:
  - 未打卡視為早退（記錄為異常）
  - 請假期間不計算早退
  - 週末和假日不計算早退

**輸出結果**: 更新 `attendance` 表的 `early_leave_minutes` 欄位

### 3. 計算加班時間 (calculate_overtime)
**功能描述**: 根據員工實際工作時間計算加班時數

**處理規則**:
- 基準工作時間: 根據班表計算標準工作時數
- 實際工作時間: 根據打卡記錄計算實際工作時數
- 計算公式: `加班分鐘 = MAX(0, 實際工作時間 - 標準工作時間)`
- 最小加班單位: 30分鐘（不足30分鐘不計加班）
- 特殊情況:
  - 請假期間不計算加班
  - 週末加班需特別標註
  - 國定假日加班按特殊費率計算

**輸出結果**: 更新 `attendance` 表的 `overtime_minutes` 欄位

### 4. 整合請假資料 (integrate_leaves)
**功能描述**: 將請假申請資料整合到考勤記錄中

**處理規則**:
- 資料來源: `leave_applications` 表中已核准的請假申請
- 整合邏輯:
  - 全日請假: 標記整天為請假狀態
  - 半日請假: 標記上午或下午為請假狀態
  - 小時請假: 記錄具體請假時數
- 優先順序: 請假資料優先於打卡資料
- 衝突處理: 請假期間的打卡記錄標記為無效

**輸出結果**: 
- 更新 `attendance` 表的 `leave_type` 和 `leave_hours` 欄位
- 更新考勤狀態為相應的請假類型

### 5. 覆蓋已存在的計算結果 (overwrite_existing)
**功能描述**: 決定是否覆蓋已經計算過的考勤資料

**處理規則**:
- **啟用時**: 重新計算所有選定範圍內的考勤資料
- **停用時**: 僅處理尚未計算的考勤記錄
- **建議使用**: 資料修正或重新計算時啟用

## 🔄 處理流程

### 階段1: 資料載入
1. 載入指定日期範圍和員工範圍的考勤記錄
2. 載入相關的班表資料
3. 載入請假申請資料
4. 驗證資料完整性

### 階段2: 時間計算
1. 遍歷每個員工的每一天記錄
2. 根據班表計算標準工作時間
3. 根據打卡記錄計算實際工作時間
4. 計算遲到、早退、加班時間

### 階段3: 資料整合
1. 整合請假資料到考勤記錄
2. 處理資料衝突和異常情況
3. 更新考勤狀態

### 階段4: 儲存結果
1. 更新考勤記錄到資料庫
2. 記錄處理日誌
3. 生成處理報告

## 📈 效能考量

### 處理能力
- **單次處理上限**: 建議不超過10,000條記錄
- **處理速度**: 約每秒處理10條記錄
- **記憶體使用**: 每1,000條記錄約佔用50MB記憶體

### 最佳化建議
- 分批處理大量資料
- 在非高峰時段執行
- 定期清理處理日誌

## ⚠️ 注意事項與限制

### 資料完整性要求
1. **班表資料**: 員工必須有有效的班表設定
2. **打卡資料**: 需要完整的上下班打卡記錄
3. **請假資料**: 請假申請必須已核准

### 處理限制
1. **時間範圍**: 不建議處理超過6個月的歷史資料
2. **併發處理**: 同一時間只能執行一個處理任務
3. **資料鎖定**: 處理期間相關資料會被鎖定

### 錯誤處理
1. **資料缺失**: 記錄警告並跳過該記錄
2. **計算錯誤**: 記錄錯誤詳情並繼續處理
3. **系統異常**: 回滾已處理的資料並記錄錯誤

## 📝 處理結果

### 成功指標
- **處理完成率**: 成功處理的記錄比例
- **資料準確性**: 計算結果的正確性
- **處理時間**: 實際處理時間與預估時間的比較

### 輸出報告
- 處理統計: 創建、更新、失敗的記錄數量
- 錯誤清單: 詳細的錯誤記錄和原因
- 處理日誌: 完整的處理過程記錄

## 🔧 系統設定

### 可調整參數
- **容許遲到時間**: 預設5分鐘
- **容許早退時間**: 預設5分鐘
- **最小加班單位**: 預設30分鐘
- **換日時間**: 預設凌晨6點

### 權限要求
- **執行權限**: 僅限系統管理員
- **資料存取**: 需要考勤、員工、班表、請假資料的讀寫權限

## 📞 技術支援

如有任何問題或需要調整處理規則，請聯繫系統管理員或技術支援團隊。

---

**文件版本**: v1.0  
**最後更新**: 2025-06-16  
**審核狀態**: 待審核
