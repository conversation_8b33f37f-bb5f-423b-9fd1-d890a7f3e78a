-- 創建文件上傳表
-- 用於記錄所有上傳的文件信息

CREATE TABLE IF NOT EXISTS file_uploads (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filename VARCHAR(255) NOT NULL,                    -- 存儲的文件名（UUID）
    original_filename VARCHAR(255) NOT NULL,           -- 原始文件名
    file_path TEXT NOT NULL,                          -- 文件存儲路徑
    file_url TEXT NOT NULL,                           -- 文件訪問URL
    file_size INTEGER NOT NULL,                       -- 文件大小（bytes）
    mime_type VARCHAR(100),                           -- MIME類型
    upload_type VARCHAR(50) DEFAULT 'general',        -- 上傳類型：leave_attachment, avatar, general
    uploaded_by INTEGER NOT NULL,                     -- 上傳者ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,    -- 創建時間
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,    -- 更新時間
    
    FOREIGN KEY (uploaded_by) REFERENCES employees(id) ON DELETE CASCADE
);

-- 創建索引
CREATE INDEX IF NOT EXISTS idx_file_uploads_uploaded_by ON file_uploads(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_file_uploads_upload_type ON file_uploads(upload_type);
CREATE INDEX IF NOT EXISTS idx_file_uploads_created_at ON file_uploads(created_at);

-- 為請假申請表添加附件字段
ALTER TABLE leave_requests ADD COLUMN attachments TEXT; -- JSON格式存儲附件URL列表

-- 創建更新時間觸發器
CREATE TRIGGER IF NOT EXISTS update_file_uploads_timestamp 
    AFTER UPDATE ON file_uploads
    FOR EACH ROW
BEGIN
    UPDATE file_uploads SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
