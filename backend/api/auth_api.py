"""
認證權限API模組

包含所有認證和權限管理相關的API端點：
- 用戶登入/登出
- 密碼管理
- 權限驗證
- 會話管理
- 審核流程
"""

import hashlib
import logging

from datetime import datetime
from flask import Blueprint, jsonify, request, session, make_response

# 創建藍圖
auth_bp = Blueprint("auth", __name__)

# 設置日誌
logger = logging.getLogger(__name__)

# 導入資料庫連接函數
from database import create_connection


def hash_password(password):
    """
    密碼雜湊函數

    參數：
    password (str): 原始密碼

    返回：
    str: 雜湊後的密碼
    """
    return hashlib.sha256(password.encode()).hexdigest()


def verify_password(password, hashed_password):
    """
    驗證密碼

    參數：
    password (str): 原始密碼
    hashed_password (str): 雜湊後的密碼

    返回：
    bool: 密碼是否正確
    """
    return hash_password(password) == hashed_password


@auth_bp.route("/api/login", methods=["POST"])
def login():
    """
    用戶登入（使用員工資料）

    請求體：
    - user_id/employee_id/username: 員工編號或員工姓名（必填）
    - password: 密碼（必填）
    - remember_me: 記住我（可選）

    返回：
    - 登入結果和用戶資訊
    """
    data = request.json

    # 支援多種參數名稱（向後兼容）
    user_id = None
    if data:
        user_id = data.get("user_id") or data.get("employee_id") or data.get("username")

    password = data.get("password") if data else None

    if not data or not user_id or not password:
        return jsonify({"success": False, "error": "請提供用戶ID和密碼"}), 400

    data.get("remember_me", False)

    conn = create_connection()
    try:
        cursor = conn.cursor()

        # 查詢員工資訊（支援員工編號或姓名登錄，員工編號不區分大小寫）
        cursor.execute(
            """
            SELECT e.id, e.name, e.employee_id, e.password, e.status, e.role_id,
                   e.department_id, d.name as department_name, e.email, e.phone
            FROM employees e
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE (UPPER(e.employee_id) = UPPER(?) OR e.name = ?) AND e.status = 'active'
        """,
            (user_id, user_id),
        )

        employee = cursor.fetchone()

        if not employee:
            logger.warning(f"登入失敗：員工不存在或已停用 - {user_id}")
            return jsonify({"success": False, "error": "員工編號/姓名或密碼錯誤"}), 401

        # 驗證密碼（如果員工沒有設定密碼，使用預設密碼邏輯）
        stored_password = employee[3]
        if not stored_password:
            # 如果沒有密碼，可以使用員工編號作為預設密碼
            stored_password = employee[2]  # employee_id

        if password != stored_password:
            logger.warning(f"登入失敗：密碼錯誤 - {user_id}")
            return jsonify({"success": False, "error": "員工編號/姓名或密碼錯誤"}), 401

        # 創建會話（簡化版，不依賴user_sessions表）
        session["employee_id"] = employee[0]
        session["employee_name"] = employee[1]
        session["employee_code"] = employee[2]
        session["department_id"] = employee[6]
        session["department_name"] = employee[7]
        session["role_id"] = employee[5] or 1
        session["logged_in"] = True

        user_info = {
            "employee_id": employee[0],
            "employee_name": employee[1],
            "employee_code": employee[2],
            "department_id": employee[6],
            "department_name": employee[7],
            "email": employee[8],
            "phone": employee[9],
            "role_id": employee[5] or 1,
            "permissions": ["read", "write"],  # 簡化權限
        }

        logger.info(f"員工登入成功: {employee[1]} ({employee[2]})")
        return jsonify(
            {"success": True, "message": "登入成功", "data": {"user": user_info}}
        )

    except Exception as e:
        logger.error(f"登入處理失敗: {e}")
        return jsonify({"success": False, "error": "登入處理失敗"}), 500
    finally:
        conn.close()


@auth_bp.route("/api/logout", methods=["POST"])
def logout():
    """
    用戶登出

    返回：
    - 登出結果
    """
    try:
        employee_name = session.get("employee_name")

        # 清除 Flask session
        session.clear()

        logger.info(f"員工登出成功: {employee_name}")

        # 設定回應標頭，確保不被快取
        response = make_response(jsonify({"message": "登出成功"}))
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        return response

    except Exception as e:
        logger.error(f"登出處理失敗: {e}")
        return jsonify({"error": "登出處理失敗"}), 500


@auth_bp.route("/api/clear-cache", methods=["POST", "GET"])
def clear_cache():
    """
    清除瀏覽器快取的 API 端點

    返回：
    - 清除快取指令
    """
    try:
        # 清除 Flask session
        session.clear()

        response = make_response(jsonify({
            "success": True,
            "message": "快取已清除，請重新整理頁面",
            "timestamp": datetime.now().isoformat(),
            "instructions": [
                "請按 Ctrl+Shift+R (Windows) 或 Cmd+Shift+R (Mac) 強制重新整理",
                "或關閉瀏覽器重新開啟"
            ]
        }))

        # 強制不快取此回應
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        response.headers['Last-Modified'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S GMT')

        return response

    except Exception as e:
        logger.error(f"清除快取失敗: {e}")
        return jsonify({"error": "清除快取失敗"}), 500


@auth_bp.route("/api/auth/verify", methods=["GET"])
def verify_auth():
    """
    驗證用戶認證狀態

    返回：
    - 認證狀態和用戶資訊
    """
    try:
        if not session.get("logged_in") or not session.get("employee_id"):
            return jsonify({"authenticated": False, "message": "未登入"}), 401

        user_info = {
            "employee_id": session.get("employee_id"),
            "employee_name": session.get("employee_name"),
            "employee_code": session.get("employee_code"),
            "department_id": session.get("department_id"),
            "department_name": session.get("department_name"),
            "role_id": session.get("role_id", 1),
            "permissions": ["read", "write"],
        }

        return jsonify({"authenticated": True, "user": user_info})

    except Exception as e:
        logger.error(f"認證驗證失敗: {e}")
        return jsonify({"authenticated": False, "error": str(e)}), 500


@auth_bp.route("/api/auth/clock-permission", methods=["GET"])
def check_clock_permission():
    """
    檢查員工線上打卡權限

    返回：
    - 線上打卡權限狀態
    """
    try:
        if not session.get("logged_in") or not session.get("employee_id"):
            return jsonify({"error": "未登入"}), 401

        employee_id = session.get("employee_id")

        conn = create_connection()
        try:
            cursor = conn.cursor()

            # 查詢員工的線上打卡權限
            cursor.execute(
                """
                SELECT allow_online_punch, name, employee_id
                FROM employees
                WHERE id = ?
            """,
                (employee_id,),
            )

            result = cursor.fetchone()

            if not result:
                return jsonify({"error": "員工資料不存在"}), 404

            allow_online_punch = result[0]
            employee_name = result[1]
            employee_code = result[2]

            logger.info(
                f"檢查線上打卡權限: {employee_name} ({employee_code}) - 權限: {'允許' if allow_online_punch else '禁止'}"
            )

            return jsonify(
                {
                    "allow_online_punch": bool(allow_online_punch),
                    "employee_name": employee_name,
                    "employee_code": employee_code,
                    "message": "允許線上打卡" if allow_online_punch else "不允許線上打卡",
                }
            )

        finally:
            conn.close()

    except Exception as e:
        logger.error(f"檢查線上打卡權限失敗: {e}")
        return jsonify({"error": "檢查權限失敗"}), 500


@auth_bp.route("/api/auth/change-password", methods=["POST"])
def change_password():
    """
    修改密碼

    請求體：
    - current_password: 當前密碼（必填）
    - new_password: 新密碼（必填）
    - confirm_password: 確認密碼（必填）

    返回：
    - 修改結果
    """
    data = request.json
    employee_id = session.get("employee_id")

    if not employee_id:
        return jsonify({"error": "請先登入"}), 401

    required_fields = ["current_password", "new_password", "confirm_password"]
    for field in required_fields:
        if not data or not data.get(field):
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400

    if data["new_password"] != data["confirm_password"]:
        return jsonify({"error": "新密碼與確認密碼不一致"}), 400

    if len(data["new_password"]) < 6:
        return jsonify({"error": "新密碼長度至少6個字元"}), 400

    conn = create_connection()
    try:
        cursor = conn.cursor()

        # 獲取當前密碼
        cursor.execute(
            "SELECT password, employee_id FROM employees WHERE id = ?", (employee_id,)
        )
        employee = cursor.fetchone()

        if not employee:
            return jsonify({"error": "員工不存在"}), 404

        # 驗證當前密碼
        current_stored_password = employee[0] or employee[1]  # 如果沒有密碼，使用員工編號
        if data["current_password"] != current_stored_password:
            return jsonify({"error": "當前密碼錯誤"}), 400

        # 更新密碼
        cursor.execute(
            """
            UPDATE employees
            SET password = ?
            WHERE id = ?
        """,
            (data["new_password"], employee_id),
        )

        conn.commit()

        logger.info(f"員工 {employee_id} 密碼修改成功")
        return jsonify({"message": "密碼修改成功"})

    except Exception as e:
        logger.error(f"修改密碼失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@auth_bp.route("/api/approval/leaves", methods=["GET"])
def get_approval_leaves():
    """
    獲取待審核的請假申請

    查詢參數：
    - status: 狀態篩選（pending/all，預設：pending）
    - page: 頁碼（預設：1）
    - limit: 每頁筆數（預設：20）

    返回：
    - 待審核的請假申請列表
    """
    conn = None
    try:
        # 簡化認證檢查 - 在開發環境下允許訪問
        session.get("employee_id")
        # 開發環境下暫時跳過認證檢查

        status = request.args.get("status", "pending")
        page = request.args.get("page", 1, type=int)
        limit = request.args.get("limit", 20, type=int)

        # 參數驗證
        if page < 1:
            return jsonify({"error": "頁碼必須大於 0"}), 400

        if limit < 1 or limit > 100:
            return jsonify({"error": "每頁筆數必須在 1-100 之間"}), 400

        offset = (page - 1) * limit

        conn = create_connection()
        cursor = conn.cursor()

        # 構建查詢條件
        where_conditions = []
        params = []

        if status == "pending":
            where_conditions.append("lr.status = 'pending'")

        where_clause = (
            " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        )

        # 查詢總記錄數
        count_query = f"""
            SELECT COUNT(*) as total
            FROM leaves lr
            JOIN employees e ON lr.employee_id = e.id
            {where_clause}
        """

        cursor.execute(count_query, params)
        total_records = cursor.fetchone()[0]

        # 查詢詳細記錄
        records_query = f"""
            SELECT
                lr.id, lr.employee_id, lr.leave_type, lr.start_date, lr.end_date,
                lr.leave_hours, lr.reason, lr.status, lr.created_at as applied_at,
                e.name as employee_name, e.employee_id as employee_code,
                COALESCE(lt.name, lr.leave_type) as leave_type_name, 1 as is_paid,
                d.name as department_name, e.position as employee_position,
                lr.substitute_id, sub.name as substitute_name, sub.employee_id as substitute_code,
                sub.position as substitute_position,
                lr.approver_id, approver.name as approver_name
            FROM leaves lr
            JOIN employees e ON lr.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN employees sub ON lr.substitute_id = sub.id
            LEFT JOIN employees approver ON lr.approver_id = approver.id
            LEFT JOIN leave_types lt ON lr.leave_type = lt.code
            {where_clause}
            ORDER BY lr.created_at DESC
            LIMIT ? OFFSET ?
        """

        cursor.execute(records_query, params + [limit, offset])

        # 轉換為字典格式
        columns = [description[0] for description in cursor.description]
        records = []
        for row in cursor.fetchall():
            record = dict(zip(columns, row))
            records.append(record)

        # 計算總頁數
        total_pages = (total_records + limit - 1) // limit

        return jsonify(
            {
                "success": True,
                "records": records,
                "pagination": {
                    "total": total_records,
                    "page": page,
                    "limit": limit,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1,
                },
            }
        )

    except Exception as e:
        logger.error(f"獲取待審核請假申請失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        if conn:
            conn.close()


@auth_bp.route("/api/approval/stats", methods=["GET"])
def get_approval_stats():
    """
    獲取審核統計資料

    返回：
    - 審核相關的統計資料
    """
    conn = None
    try:
        # 簡化認證檢查 - 在開發環境下允許訪問
        session.get("employee_id")
        # 開發環境下暫時跳過認證檢查

        conn = create_connection()
        cursor = conn.cursor()

        # 獲取待審核數量
        cursor.execute("SELECT COUNT(*) FROM leaves WHERE status = 'pending'")
        pending_count = cursor.fetchone()[0]

        # 獲取本月審核數量
        current_month = datetime.now().strftime("%Y-%m")
        cursor.execute(
            """
            SELECT COUNT(*)
            FROM leaves
            WHERE strftime('%Y-%m', approved_at) = ?
            AND approver_id IS NOT NULL
        """,
            (current_month,),
        )
        monthly_approved = cursor.fetchone()[0]

        # 獲取審核通過率
        cursor.execute(
            """
            SELECT
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected,
                COUNT(*) as total
            FROM leaves
            WHERE status IN ('approved', 'rejected')
            AND strftime('%Y-%m', approved_at) = ?
        """,
            (current_month,),
        )

        approval_stats = cursor.fetchone()
        approval_rate = 0
        if approval_stats[2] > 0:  # total > 0
            approval_rate = round((approval_stats[0] / approval_stats[2]) * 100, 1)

        # 獲取最近審核記錄
        cursor.execute(
            """
            SELECT
                lr.id, e.name as employee_name, lr.leave_type,
                lr.status, lr.approved_at, approver.name as approver_name
            FROM leaves lr
            JOIN employees e ON lr.employee_id = e.id
            LEFT JOIN employees approver ON lr.approver_id = approver.id
            WHERE lr.status IN ('approved', 'rejected')
            ORDER BY lr.approved_at DESC
            LIMIT 10
        """
        )

        recent_approvals = []
        for row in cursor.fetchall():
            recent_approvals.append(
                {
                    "id": row[0],
                    "employee_name": row[1],
                    "leave_type": row[2],
                    "status": row[3],
                    "approved_at": row[4],
                    "approver_name": row[5],
                }
            )

        stats = {
            "pending_count": pending_count,
            "monthly_approved": monthly_approved,
            "approval_rate": approval_rate,
            "monthly_stats": {
                "approved": approval_stats[0],
                "rejected": approval_stats[1],
                "total": approval_stats[2],
            },
            "recent_approvals": recent_approvals,
            "generated_at": datetime.now().isoformat(),
        }

        return jsonify(stats)

    except Exception as e:
        logger.error(f"獲取審核統計失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        if conn:
            conn.close()


@auth_bp.route("/api/approval/leaves/<int:leave_id>", methods=["POST"])
def approve_leave(leave_id):
    """
    審核請假申請

    參數：
    - leave_id: 請假申請ID

    請求體：
    - action: 'approve' 或 'reject'
    - comment: 審核備註
    - approver_id: 審核人ID

    返回：
    - 審核結果
    """
    conn = None
    try:
        # 獲取請求數據
        data = request.get_json()
        if not data:
            return jsonify({"error": "請求數據不能為空"}), 400

        action = data.get("action")
        comment = data.get("comment", "")
        approver_id = data.get("approver_id")

        # 驗證必要參數
        if not action or action not in ["approve", "reject"]:
            return jsonify({"error": "action 必須是 'approve' 或 'reject'"}), 400

        if not approver_id:
            return jsonify({"error": "審核人ID不能為空"}), 400

        conn = create_connection()
        cursor = conn.cursor()

        # 檢查請假申請是否存在且為待審核狀態
        cursor.execute(
            """
            SELECT id, employee_id, status, leave_type, start_date, end_date, reason
            FROM leaves
            WHERE id = ?
        """,
            (leave_id,),
        )

        leave_record = cursor.fetchone()
        if not leave_record:
            return jsonify({"error": "請假申請不存在"}), 404

        if leave_record[2] != "pending":
            return jsonify({"error": "該請假申請已經被審核過"}), 400

        # 更新請假申請狀態
        new_status = "approved" if action == "approve" else "rejected"
        approved_at = datetime.now().isoformat()

        cursor.execute(
            """
            UPDATE leaves
            SET status = ?, approver_id = ?, comment = ?, approved_at = ?
            WHERE id = ?
        """,
            (new_status, approver_id, comment, approved_at, leave_id),
        )

        conn.commit()

        # 獲取審核人資訊
        cursor.execute("SELECT name FROM employees WHERE id = ?", (approver_id,))
        approver_info = cursor.fetchone()
        approver_name = approver_info[0] if approver_info else "未知審核人"

        # 獲取申請人資訊
        cursor.execute("SELECT name FROM employees WHERE id = ?", (leave_record[1],))
        employee_info = cursor.fetchone()
        employee_name = employee_info[0] if employee_info else "未知申請人"

        action_text = "批准" if action == "approve" else "拒絕"
        logger.info(
            f"請假申請審核成功: 申請ID {leave_id}, 申請人 {employee_name}, 審核人 {approver_name}, 動作 {action_text}"
        )

        return jsonify(
            {
                "success": True,
                "message": f"請假申請已{action_text}",
                "data": {
                    "leave_id": leave_id,
                    "status": new_status,
                    "approver_name": approver_name,
                    "approved_at": approved_at,
                    "comment": comment,
                },
            }
        )

    except Exception as e:
        logger.error(f"審核請假申請失敗: {e}")
        if conn:
            conn.rollback()
        return jsonify({"error": str(e)}), 500
    finally:
        if conn:
            conn.close()


@auth_bp.route("/api/auth/sessions", methods=["GET"])
def get_user_sessions():
    """
    獲取用戶會話列表

    返回：
    - 當前用戶的所有會話
    """
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"error": "請先登入"}), 401

        conn = create_connection()
        cursor = conn.cursor()

        cursor.execute(
            """
            SELECT id, session_token, created_at, expires_at, ip_address,
                   user_agent, is_active, logged_out_at
            FROM user_sessions
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 20
        """,
            (user_id,),
        )

        sessions = []
        current_session_token = session.get("session_token")

        for row in cursor.fetchall():
            session_info = {
                "id": row[0],
                "session_token": row[1][:8] + "...",  # 只顯示前8個字元
                "created_at": row[2],
                "expires_at": row[3],
                "ip_address": row[4],
                "user_agent": row[5],
                "is_active": bool(row[6]),
                "logged_out_at": row[7],
                "is_current": row[1] == current_session_token,
            }
            sessions.append(session_info)

        return jsonify({"sessions": sessions})

    except Exception as e:
        logger.error(f"獲取用戶會話失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@auth_bp.route("/api/auth/sessions/<int:session_id>", methods=["DELETE"])
def revoke_session(session_id):
    """
    撤銷指定會話

    參數：
    session_id (int): 會話ID

    返回：
    - 撤銷結果
    """
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"error": "請先登入"}), 401

        conn = create_connection()
        cursor = conn.cursor()

        # 檢查會話是否屬於當前用戶
        cursor.execute(
            """
            SELECT session_token FROM user_sessions
            WHERE id = ? AND user_id = ?
        """,
            (session_id, user_id),
        )

        session_data = cursor.fetchone()
        if not session_data:
            return jsonify({"error": "會話不存在或無權限"}), 404

        # 撤銷會話
        cursor.execute(
            """
            UPDATE user_sessions
            SET is_active = 0, logged_out_at = CURRENT_TIMESTAMP
            WHERE id = ? AND user_id = ?
        """,
            (session_id, user_id),
        )

        conn.commit()

        # 如果撤銷的是當前會話，清除 Flask session
        current_session_token = session.get("session_token")
        if session_data[0] == current_session_token:
            session.clear()

        logger.info(f"會話 {session_id} 已被用戶 {user_id} 撤銷")
        return jsonify({"message": "會話已撤銷"})

    except Exception as e:
        logger.error(f"撤銷會話失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# 這裡之後會添加其他認證權限相關的API
# 例如：角色管理、權限檢查、API金鑰管理等
