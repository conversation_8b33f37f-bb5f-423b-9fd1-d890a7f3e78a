"""
考勤資料處理 API

此模組提供考勤資料整理的API端點，包括：
1. 執行考勤處理
2. 查詢處理狀態
3. 獲取處理結果
4. 預覽處理資料

功能特點：
- 自動計算遲到、早退、加班時間
- 整合請假資料
- 批量處理考勤記錄
- 實時處理進度追蹤
"""

import logging
import threading
import time
import uuid

import sqlite3
from datetime import datetime, timedelta
from flask import Blueprint, jsonify, request

from database import create_connection
from services.enhanced_attendance_processor import EnhancedAttendanceProcessor

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 創建藍圖
attendance_processing_bp = Blueprint("attendance_processing", __name__)

# 全局變量存儲處理狀態
processing_status = {}


@attendance_processing_bp.route("/api/attendance/processing/execute", methods=["POST"])
def execute_attendance_processing():
    """
    執行考勤整理處理 API

    功能說明：
    - 啟動考勤資料整理處理
    - 計算遲到、早退、加班時間
    - 整合請假資料
    - 更新考勤狀態

    請求參數：
    {
        "date_range": {
            "start_date": "2025-06-01",
            "end_date": "2025-06-02"
        },
        "employee_scope": {
            "type": "all",  // all, department, specific
            "department_ids": [1, 2],
            "employee_ids": [1, 2, 3]
        },
        "processing_options": {
            "calculate_late_early": true,
            "calculate_overtime": true,
            "integrate_leaves": true,
            "overwrite_existing": false
        }
    }

    返回格式：
    {
        "success": true,
        "message": "考勤整理處理已啟動",
        "processing_id": "proc_20250602_143000",
        "total_records": 150,
        "estimated_completion": "2025-06-02 14:35:00"
    }
    """
    try:
        data = request.get_json()

        # 驗證請求參數
        if not data:
            return jsonify({"success": False, "error": "請求參數不能為空"}), 400

        date_range = data.get("date_range", {})
        employee_scope = data.get("employee_scope", {})
        processing_options = data.get("processing_options", {})

        # 驗證日期範圍
        start_date = date_range.get("start_date")
        end_date = date_range.get("end_date")

        if not start_date or not end_date:
            return jsonify({"success": False, "error": "請提供開始日期和結束日期"}), 400

        # 生成處理ID
        processing_id = (
            f"proc_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        )

        # 計算預估記錄數量
        total_records = estimate_processing_records(
            start_date, end_date, employee_scope
        )

        # 計算預估完成時間（每條記錄約0.1秒）
        estimated_seconds = total_records * 0.1
        estimated_completion = datetime.now() + timedelta(seconds=estimated_seconds)

        # 初始化處理狀態
        processing_status[processing_id] = {
            "status": "running",
            "progress": 0,
            "total_records": total_records,
            "processed_records": 0,
            "start_time": datetime.now().isoformat(),
            "estimated_completion": estimated_completion.isoformat(),
            "current_step": "初始化",
            "errors": [],
            "results": {"created": 0, "updated": 0, "failed": 0},
        }

        # 在背景執行處理
        thread = threading.Thread(
            target=background_processing,
            args=(
                processing_id,
                start_date,
                end_date,
                employee_scope,
                processing_options,
            ),
        )
        thread.daemon = True
        thread.start()

        return jsonify(
            {
                "success": True,
                "message": "考勤整理處理已啟動",
                "processing_id": processing_id,
                "total_records": total_records,
                "estimated_completion": estimated_completion.isoformat(),
            }
        )

    except Exception as e:
        logger.error(f"執行考勤處理時發生錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@attendance_processing_bp.route(
    "/api/attendance/processing/status/<processing_id>", methods=["GET"]
)
def get_processing_status(processing_id):
    """
    查詢處理狀態

    返回格式：
    {
        "success": true,
        "data": {
            "status": "running",  // running, completed, failed
            "progress": 75,
            "total_records": 150,
            "processed_records": 112,
            "start_time": "2025-06-02T14:30:00",
            "estimated_completion": "2025-06-02T14:35:00",
            "current_step": "處理員工考勤記錄",
            "errors": [],
            "results": {
                "created": 45,
                "updated": 67,
                "failed": 0
            }
        }
    }
    """
    try:
        if processing_id not in processing_status:
            return jsonify({"success": False, "error": "處理ID不存在"}), 404

        status_data = processing_status[processing_id]

        return jsonify({"success": True, "data": status_data})

    except Exception as e:
        logger.error(f"查詢處理狀態時發生錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@attendance_processing_bp.route("/api/attendance/processing/preview", methods=["POST"])
def preview_processing_data():
    """
    預覽處理資料

    返回將要處理的考勤記錄預覽
    """
    try:
        data = request.get_json()

        date_range = data.get("date_range", {})
        employee_scope = data.get("employee_scope", {})

        start_date = date_range.get("start_date")
        end_date = date_range.get("end_date")

        if not start_date or not end_date:
            return jsonify({"success": False, "error": "請提供開始日期和結束日期"}), 400

        # 獲取預覽資料
        preview_data = get_preview_data(start_date, end_date, employee_scope)

        return jsonify({"success": True, "data": preview_data})

    except Exception as e:
        logger.error(f"預覽處理資料時發生錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


def estimate_processing_records(start_date, end_date, employee_scope):
    """估算處理記錄數量"""
    try:
        conn = create_connection()
        cursor = conn.cursor()

        # 計算日期範圍
        start = datetime.strptime(start_date, "%Y-%m-%d").date()
        end = datetime.strptime(end_date, "%Y-%m-%d").date()
        days = (end - start).days + 1

        # 根據員工範圍計算員工數量
        if employee_scope.get("type") == "all":
            cursor.execute("SELECT COUNT(*) FROM employees WHERE status = 'active'")
            employee_count = cursor.fetchone()[0]
        elif employee_scope.get("type") == "department":
            department_ids = employee_scope.get("department_ids", [])
            if department_ids:
                placeholders = ",".join(["?" for _ in department_ids])
                cursor.execute(
                    f"SELECT COUNT(*) FROM employees WHERE department_id IN ({placeholders}) AND status = 'active'",
                    department_ids,
                )
                employee_count = cursor.fetchone()[0]
            else:
                employee_count = 0
        elif employee_scope.get("type") == "specific":
            employee_ids = employee_scope.get("employee_ids", [])
            employee_count = len(employee_ids)
        else:
            employee_count = 0

        conn.close()

        # 估算總記錄數
        total_records = days * employee_count

        return total_records

    except Exception as e:
        logger.error(f"估算處理記錄數量時發生錯誤: {e}")
        return 0


def get_preview_data(start_date, end_date, employee_scope):
    """獲取預覽資料"""
    try:
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # 構建員工查詢條件
        employee_condition = ""
        params = []

        if employee_scope.get("type") == "department":
            department_ids = employee_scope.get("department_ids", [])
            if department_ids:
                placeholders = ",".join(["?" for _ in department_ids])
                employee_condition = f"AND e.department_id IN ({placeholders})"
                params.extend(department_ids)
        elif employee_scope.get("type") == "specific":
            employee_ids = employee_scope.get("employee_ids", [])
            if employee_ids:
                placeholders = ",".join(["?" for _ in employee_ids])
                employee_condition = f"AND e.id IN ({placeholders})"
                params.extend(employee_ids)

        # 查詢員工資料
        query = f"""
            SELECT e.id, e.employee_id, e.name, d.name as department_name
            FROM employees e
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE e.status = 'active' {employee_condition}
            ORDER BY e.employee_id
            LIMIT 10
        """

        cursor.execute(query, params)
        employees = [dict(row) for row in cursor.fetchall()]

        # 查詢現有考勤記錄數量
        params_with_dates = [start_date, end_date] + params
        existing_query = f"""
            SELECT COUNT(*) as count
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            WHERE a.work_date BETWEEN ? AND ?
            AND e.status = 'active' {employee_condition}
        """

        cursor.execute(existing_query, params_with_dates)
        existing_count = cursor.fetchone()["count"]

        conn.close()

        return {
            "employees": employees,
            "total_employees": len(employees),
            "date_range": {"start_date": start_date, "end_date": end_date},
            "existing_records": existing_count,
            "estimated_new_records": estimate_processing_records(
                start_date, end_date, employee_scope
            )
            - existing_count,
        }

    except Exception as e:
        logger.error(f"獲取預覽資料時發生錯誤: {e}")
        return {}


def background_processing(
    processing_id, start_date, end_date, employee_scope, processing_options
):
    """背景處理函數"""
    try:
        logger.info(f"開始背景處理: {processing_id}")

        # 更新狀態
        processing_status[processing_id]["current_step"] = "初始化處理器"
        processing_status[processing_id]["progress"] = 5

        # 創建處理器
        processor = EnhancedAttendanceProcessor()

        # 獲取員工列表
        processing_status[processing_id]["current_step"] = "獲取員工列表"
        processing_status[processing_id]["progress"] = 10

        employees = get_employees_for_processing(employee_scope)

        # 生成日期列表
        start = datetime.strptime(start_date, "%Y-%m-%d").date()
        end = datetime.strptime(end_date, "%Y-%m-%d").date()
        current_date = start

        total_tasks = len(employees) * ((end - start).days + 1)
        completed_tasks = 0

        # 處理每個員工的每一天
        while current_date <= end:
            date_str = current_date.strftime("%Y-%m-%d")

            processing_status[processing_id]["current_step"] = f"處理日期: {date_str}"

            for employee in employees:
                try:
                    # 處理單個員工的考勤記錄
                    result = processor._process_employee_attendance(
                        employee["id"], date_str
                    )

                    # 更新結果統計
                    if result["success"]:
                        if result["action"] == "created":
                            processing_status[processing_id]["results"]["created"] += 1
                        elif result["action"] == "updated":
                            processing_status[processing_id]["results"]["updated"] += 1
                    else:
                        processing_status[processing_id]["results"]["failed"] += 1
                        processing_status[processing_id]["errors"].append(
                            f"員工 {employee['employee_id']} 日期 {date_str}: {result.get('error', '未知錯誤')}"
                        )

                    completed_tasks += 1
                    progress = min(95, int((completed_tasks / total_tasks) * 85) + 10)
                    processing_status[processing_id]["progress"] = progress
                    processing_status[processing_id][
                        "processed_records"
                    ] = completed_tasks

                    # 短暫延遲，避免過度佔用資源
                    time.sleep(0.01)

                except Exception as e:
                    logger.error(
                        f"處理員工 {employee['employee_id']} 日期 {date_str} 時發生錯誤: {e}"
                    )
                    processing_status[processing_id]["results"]["failed"] += 1
                    processing_status[processing_id]["errors"].append(
                        f"員工 {employee['employee_id']} 日期 {date_str}: {str(e)}"
                    )

            current_date += timedelta(days=1)

        # 完成處理
        processing_status[processing_id]["status"] = "completed"
        processing_status[processing_id]["progress"] = 100
        processing_status[processing_id]["current_step"] = "處理完成"
        processing_status[processing_id]["end_time"] = datetime.now().isoformat()

        logger.info(f"背景處理完成: {processing_id}")

    except Exception as e:
        logger.error(f"背景處理時發生錯誤: {e}")
        processing_status[processing_id]["status"] = "failed"
        processing_status[processing_id]["current_step"] = f"處理失敗: {str(e)}"
        processing_status[processing_id]["errors"].append(str(e))


def get_employees_for_processing(employee_scope):
    """獲取需要處理的員工列表"""
    try:
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        if employee_scope.get("type") == "all":
            cursor.execute(
                """
                SELECT id, employee_id, name
                FROM employees
                WHERE status = 'active'
                ORDER BY employee_id
            """
            )
        elif employee_scope.get("type") == "department":
            department_ids = employee_scope.get("department_ids", [])
            if department_ids:
                placeholders = ",".join(["?" for _ in department_ids])
                cursor.execute(
                    f"""
                    SELECT id, employee_id, name
                    FROM employees
                    WHERE department_id IN ({placeholders}) AND status = 'active'
                    ORDER BY employee_id
                """,
                    department_ids,
                )
            else:
                return []
        elif employee_scope.get("type") == "specific":
            employee_ids = employee_scope.get("employee_ids", [])
            if employee_ids:
                placeholders = ",".join(["?" for _ in employee_ids])
                cursor.execute(
                    f"""
                    SELECT id, employee_id, name
                    FROM employees
                    WHERE id IN ({placeholders}) AND status = 'active'
                    ORDER BY employee_id
                """,
                    employee_ids,
                )
            else:
                return []
        else:
            return []

        employees = [dict(row) for row in cursor.fetchall()]
        conn.close()

        return employees

    except Exception as e:
        logger.error(f"獲取員工列表時發生錯誤: {e}")
        return []
