"""
請假管理相關API模組

包含所有請假管理相關的API端點：
- 請假申請CRUD操作
- 請假審核流程
- 請假統計分析
- 請假類型管理
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
import sqlite3
import logging

# 創建藍圖
leave_bp = Blueprint('leave', __name__)

# 設置日誌
logger = logging.getLogger(__name__)

# 導入資料庫連接函數
from database import create_connection

def calculate_partial_leave_hours(start_date, end_date, start_time, end_time):
    """
    計算部分工時請假的時數（支援跨天，自動扣除午休時間）
    
    參數：
    - start_date: 開始日期 (YYYY-MM-DD)
    - end_date: 結束日期 (YYYY-MM-DD)
    - start_time: 開始時間 (HH:MM)
    - end_time: 結束時間 (HH:MM)
    
    返回：
    - 請假時數（小時，已扣除午休時間）
    """
    try:
        start_datetime = datetime.strptime(f"{start_date} {start_time}", "%Y-%m-%d %H:%M")
        end_datetime = datetime.strptime(f"{end_date} {end_time}", "%Y-%m-%d %H:%M")
        
        if start_date == end_date:
            # 同一天的部分工時
            # 計算總分鐘數
            diff_minutes = (end_datetime - start_datetime).total_seconds() / 60
            
            # 扣除午休時間（12:00-13:00）
            lunch_start = datetime.strptime(f"{start_date} 12:00", "%Y-%m-%d %H:%M")
            lunch_end = datetime.strptime(f"{start_date} 13:00", "%Y-%m-%d %H:%M")
            
            lunch_break_minutes = 0
            if start_datetime < lunch_end and end_datetime > lunch_start:
                # 請假時間跨越午休時間
                overlap_start = max(start_datetime, lunch_start)
                overlap_end = min(end_datetime, lunch_end)
                lunch_break_minutes = (overlap_end - overlap_start).total_seconds() / 60
            
            actual_minutes = diff_minutes - lunch_break_minutes
            return round(actual_minutes / 60, 1)  # 轉換為小時並保留一位小數
            
        else:
            # 跨天請假
            total_hours = 0
            
            # 第一天：從開始時間到下班時間（17:00）
            first_day_end = datetime.strptime(f"{start_date} 17:00", "%Y-%m-%d %H:%M")
            if start_datetime < first_day_end:
                first_day_minutes = (first_day_end - start_datetime).total_seconds() / 60
                # 扣除第一天的午休時間
                lunch_start = datetime.strptime(f"{start_date} 12:00", "%Y-%m-%d %H:%M")
                lunch_end = datetime.strptime(f"{start_date} 13:00", "%Y-%m-%d %H:%M")
                first_day_lunch_minutes = 0
                if start_datetime < lunch_end and first_day_end > lunch_start:
                    overlap_start = max(start_datetime, lunch_start)
                    overlap_end = min(first_day_end, lunch_end)
                    first_day_lunch_minutes = (overlap_end - overlap_start).total_seconds() / 60
                total_hours += (first_day_minutes - first_day_lunch_minutes) / 60
            
            # 中間的完整天數
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
            days_diff = (end_date_obj - start_date_obj).days
            
            if days_diff > 1:
                total_hours += (days_diff - 1) * 8  # 中間每天8小時（已扣除午休）
            
            # 最後一天：從上班時間（08:00）到結束時間
            last_day_start = datetime.strptime(f"{end_date} 08:00", "%Y-%m-%d %H:%M")
            if end_datetime > last_day_start:
                last_day_minutes = (end_datetime - last_day_start).total_seconds() / 60
                # 扣除最後一天的午休時間
                lunch_start = datetime.strptime(f"{end_date} 12:00", "%Y-%m-%d %H:%M")
                lunch_end = datetime.strptime(f"{end_date} 13:00", "%Y-%m-%d %H:%M")
                last_day_lunch_minutes = 0
                if last_day_start < lunch_end and end_datetime > lunch_start:
                    overlap_start = max(last_day_start, lunch_start)
                    overlap_end = min(end_datetime, lunch_end)
                    last_day_lunch_minutes = (overlap_end - overlap_start).total_seconds() / 60
                total_hours += (last_day_minutes - last_day_lunch_minutes) / 60
            
            return round(total_hours, 1)
            
    except Exception as e:
        logger.error(f"計算請假時數失敗: {e}")
        # 如果計算失敗，回退到簡單計算
        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
        days = (end_date_obj - start_date_obj).days + 1
        return days * 8

@leave_bp.route("/api/leave-types", methods=["GET"])
def get_leave_types():
    """
    獲取請假類型列表
    
    返回：
    - 請假類型列表，包含所有可用的請假類型
    """
    conn = create_connection()
    conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, name, code, max_days_per_year, requires_approval, 
                   is_paid, description, is_active, created_at
            FROM leave_types 
            WHERE is_active = 1 
            ORDER BY name
        """)
        
        leave_types = []
        for row in cursor.fetchall():
            leave_type = {
                "id": row[0],
                "name": row[1],
                "code": row[2],
                "max_days_per_year": row[3],
                "requires_approval": bool(row[4]),
                "is_paid": bool(row[5]),
                "description": row[6],
                "is_active": bool(row[7]),
                "created_at": row[8]
            }
            leave_types.append(leave_type)
        
        return jsonify({"leave_types": leave_types})
        
    except Exception as e:
        logger.error(f"獲取請假類型列表失敗: {e}")
        return jsonify({"error": "獲取請假類型列表失敗"}), 500
    finally:
        conn.close()


@leave_bp.route("/api/leave-types", methods=["POST"])
def create_leave_type():
    """
    新增請假類型
    
    請求體：
    - name: 請假類型名稱（必填）
    - code: 請假類型代碼（必填）
    - max_days_per_year: 每年最大天數
    - requires_approval: 是否需要審核
    - is_paid: 是否為有薪假
    - description: 描述
    
    返回：
    - 新增的請假類型ID
    """
    data = request.json
    
    # 驗證必要欄位
    required_fields = ["name", "code"]
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查代碼是否已存在
        cursor.execute("SELECT id FROM leave_types WHERE code = ?", (data["code"],))
        if cursor.fetchone():
            return jsonify({"error": "請假類型代碼已存在"}), 400
        
        # 插入新請假類型
        cursor.execute("""
            INSERT INTO leave_types (
                name, code, max_days_per_year, requires_approval, 
                is_paid, description
            ) VALUES (?, ?, ?, ?, ?, ?)
        """, (
            data["name"],
            data["code"],
            data.get("max_days_per_year", 365),
            data.get("requires_approval", True),
            data.get("is_paid", True),
            data.get("description", "")
        ))
        
        leave_type_id = cursor.lastrowid
        conn.commit()
        
        logger.info(f"請假類型新增成功: {data['name']} (ID: {leave_type_id})")
        return jsonify({"message": "請假類型新增成功", "id": leave_type_id}), 201
        
    except Exception as e:
        logger.error(f"新增請假類型失敗: {e}")
        return jsonify({"error": "新增請假類型失敗"}), 500
    finally:
        conn.close()


@leave_bp.route("/api/leave-requests", methods=["GET"])
def get_leaves():
    """
    獲取請假申請列表
    
    查詢參數：
    - employee_id: 員工ID篩選
    - status: 狀態篩選（pending/approved/rejected）
    - start_date: 開始日期篩選
    - end_date: 結束日期篩選
    - page: 頁碼（預設：1）
    - limit: 每頁筆數（預設：20）
    
    返回：
    - 分頁的請假申請列表
    """
    try:
        # 獲取查詢參數
        employee_id_param = request.args.get('employee_id')  # 可能是字符串編號或數字ID
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        
        # 參數驗證
        if page < 1:
            return jsonify({"error": "頁碼必須大於 0"}), 400
        
        if limit < 1 or limit > 100:
            return jsonify({"error": "每頁筆數必須在 1-100 之間"}), 400
        
        # 計算偏移量
        offset = (page - 1) * limit
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 構建查詢條件
        where_conditions = []
        query_params = []
        employee_id = None
        
        if employee_id_param:
            # 如果是字符串編號（如E015），需要轉換為數字ID
            if isinstance(employee_id_param, str) and employee_id_param.startswith('E'):
                cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id_param,))
                result = cursor.fetchone()
                if result:
                    employee_id = result[0]
            else:
                # 如果是數字ID，直接使用
                try:
                    employee_id = int(employee_id_param)
                except ValueError:
                    pass
        
        if employee_id:
            where_conditions.append("lr.employee_id = ?")
            query_params.append(employee_id)
        
        if status:
            where_conditions.append("lr.status = ?")
            query_params.append(status)
        
        if start_date:
            where_conditions.append("lr.start_date >= ?")
            query_params.append(start_date)
        
        if end_date:
            where_conditions.append("lr.end_date <= ?")
            query_params.append(end_date)
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        # 查詢總記錄數
        count_query = f"""
            SELECT COUNT(*) as total
            FROM leaves lr
            LEFT JOIN employees e ON lr.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN leave_types lt ON lr.leave_type = lt.code
            {where_clause}
        """
        
        cursor.execute(count_query, query_params)
        total_records = cursor.fetchone()[0]
        
        # 查詢詳細記錄
        records_query = f"""
            SELECT
                lr.id, lr.employee_id, lr.leave_type, lr.start_date, lr.end_date,
                lr.leave_hours, lr.reason, lr.status, lr.created_at, lr.approved_at,
                lr.approver_id, lr.comment, lr.created_at, lr.created_at as updated_at,
                lr.attachments, lr.substitute_id, lr.emergency_contact,
                e.name as employee_name, e.employee_id as employee_code,
                d.name as department_name,
                lt.name as leave_type_name, lt.is_paid,
                approver.name as approver_name,
                substitute.name as substitute_name
            FROM leaves lr
            LEFT JOIN employees e ON lr.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN leave_types lt ON lr.leave_type = lt.code
            LEFT JOIN employees approver ON lr.approver_id = approver.id
            LEFT JOIN employees substitute ON lr.substitute_id = substitute.id
            {where_clause}
            ORDER BY lr.created_at DESC
            LIMIT ? OFFSET ?
        """
        
        cursor.execute(records_query, query_params + [limit, offset])

        # 轉換為字典格式
        columns = [description[0] for description in cursor.description]
        records = []
        for row in cursor.fetchall():
            record = dict(zip(columns, row))

            # 解析附件JSON
            if record.get('attachments'):
                import json
                try:
                    record['attachments'] = json.loads(record['attachments'])
                except (json.JSONDecodeError, TypeError):
                    record['attachments'] = []
            else:
                record['attachments'] = []

            records.append(record)
        
        # 計算總頁數
        total_pages = (total_records + limit - 1) // limit
        
        return jsonify({
            "success": True,
            "records": records,
            "pagination": {
                "total": total_records,
                "page": page,
                "limit": limit,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        })
        
    except Exception as e:
        logger.error(f"獲取請假申請列表失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@leave_bp.route("/api/leave-requests", methods=["POST"])
def create_leave_request():
    """
    新增請假申請
    
    請求體：
    - employee_id: 員工ID（必填）
    - leave_type: 請假類型（必填）
    - start_date: 開始日期（必填，格式：YYYY-MM-DD）
    - end_date: 結束日期（必填，格式：YYYY-MM-DD）
    - reason: 請假原因（必填）
    - leave_hours: 請假天數（可選，會自動計算）
    
    返回：
    - 新增的請假申請ID
    """
    data = request.json
    
    # 驗證必要欄位
    required_fields = ["employee_id", "leave_type", "start_date", "end_date", "reason"]
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400
    
    # 驗證日期格式
    try:
        start_date = datetime.strptime(data["start_date"], "%Y-%m-%d").date()
        end_date = datetime.strptime(data["end_date"], "%Y-%m-%d").date()
    except ValueError:
        return jsonify({"error": "日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
    
    # 驗證日期邏輯
    if start_date > end_date:
        return jsonify({"error": "開始日期不能晚於結束日期"}), 400
    
    if start_date < datetime.now().date():
        return jsonify({"error": "開始日期不能早於今天"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 驗證員工是否存在
        cursor.execute("SELECT id FROM employees WHERE id = ?", (data["employee_id"],))
        if not cursor.fetchone():
            return jsonify({"error": "員工不存在"}), 404
        
        # 驗證請假類型是否存在
        cursor.execute("""
            SELECT id, max_days_per_year, requires_approval 
            FROM leave_types 
            WHERE code = ? AND is_active = 1
        """, (data["leave_type"],))
        leave_type = cursor.fetchone()
        if not leave_type:
            return jsonify({"error": "請假類型不存在或已停用"}), 404
        
        # 計算請假天數
        leave_hours = (end_date - start_date).days + 1
        
        # 檢查是否超過年度限制
        max_days_per_year = leave_type[1] if leave_type[1] is not None else 0
        if max_days_per_year > 0:  # max_days_per_year > 0
            current_year = datetime.now().year
            cursor.execute("""
                SELECT COALESCE(SUM(leave_hours), 0) as used_days
                FROM leaves
                WHERE employee_id = ? AND leave_type = ? 
                AND strftime('%Y', start_date) = ? 
                AND status = 'approved'
            """, (data["employee_id"], data["leave_type"], str(current_year)))
            
            used_days = cursor.fetchone()[0]
            if used_days + leave_hours > max_days_per_year:
                return jsonify({
                    "error": f"超過年度請假限制。已使用 {used_days} 天，本次申請 {leave_hours} 天，年度限制 {max_days_per_year} 天"
                }), 400
        
        # 檢查日期衝突
        cursor.execute("""
            SELECT id FROM leaves
            WHERE employee_id = ? 
            AND status IN ('pending', 'approved')
            AND (
                (start_date <= ? AND end_date >= ?) OR
                (start_date <= ? AND end_date >= ?) OR
                (start_date >= ? AND end_date <= ?)
            )
        """, (
            data["employee_id"],
            data["start_date"], data["start_date"],
            data["end_date"], data["end_date"],
            data["start_date"], data["end_date"]
        ))
        
        if cursor.fetchone():
            return jsonify({"error": "請假日期與現有申請衝突"}), 400
        
        # 插入請假申請
        status = "pending" if leave_type[2] else "approved"  # requires_approval

        # 處理附件
        attachments_json = None
        if data.get("attachments"):
            import json
            attachments_json = json.dumps(data["attachments"])

        cursor.execute("""
            INSERT INTO leaves (
                employee_id, leave_type, start_date, end_date,
                leave_hours, reason, status, substitute_id, emergency_contact,
                attachments, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
        """, (
            data["employee_id"],
            data["leave_type"],
            data["start_date"],
            data["end_date"],
            leave_hours,
            data["reason"],
            status,
            data.get("substitute_id"),
            data.get("emergency_contact", ""),
            attachments_json
        ))
        
        request_id = cursor.lastrowid
        conn.commit()
        
        logger.info(f"請假申請新增成功: 員工ID {data['employee_id']}, 申請ID {request_id}")
        return jsonify({
            "message": "請假申請新增成功",
            "id": request_id,
            "status": status,
            "leave_hours": leave_hours
        }), 201
        
    except Exception as e:
        logger.error(f"新增請假申請失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@leave_bp.route("/api/leave-requests/<int:request_id>/approve", methods=["POST"])
def approve_leave_request(request_id):
    """
    審核請假申請（核准）
    
    參數：
    request_id (int): 請假申請ID
    
    請求體：
    - approver_id: 審核人員ID（必填）
    - comments: 審核意見（可選）
    
    返回：
    - 審核結果
    """
    data = request.json
    
    if not data or not data.get("approver_id"):
        return jsonify({"error": "缺少審核人員ID"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查請假申請是否存在且為待審核狀態
        cursor.execute("""
            SELECT lr.id, lr.employee_id, lr.status, e.name as employee_name
            FROM leaves lr
            JOIN employees e ON lr.employee_id = e.id
            WHERE lr.id = ?
        """, (request_id,))
        
        leave_request = cursor.fetchone()
        if not leave_request:
            return jsonify({"error": "請假申請不存在"}), 404
        
        if leave_request[2] != "pending":
            return jsonify({"error": f"請假申請狀態為 {leave_request[2]}，無法審核"}), 400
        
        # 檢查審核人員是否存在
        cursor.execute("SELECT id, name FROM employees WHERE id = ?", (data["approver_id"],))
        approver = cursor.fetchone()
        if not approver:
            return jsonify({"error": "審核人員不存在"}), 404
        
        # 更新請假申請狀態
        cursor.execute("""
            UPDATE leaves 
            SET status = 'approved', 
                approved_by = ?, 
                approved_at = datetime('now'),
                comments = ?,
                updated_at = datetime('now')
            WHERE id = ?
        """, (data["approver_id"], data.get("comments", ""), request_id))
        
        conn.commit()
        
        logger.info(f"請假申請 {request_id} 已核准，審核人：{approver[1]}")
        return jsonify({
            "message": "請假申請已核准",
            "request_id": request_id,
            "approver_name": approver[1],
            "employee_name": leave_request[3]
        })
        
    except Exception as e:
        logger.error(f"審核請假申請失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@leave_bp.route("/api/leave-requests/<int:request_id>/reject", methods=["POST"])
def reject_leave_request(request_id):
    """
    審核請假申請（拒絕）
    
    參數：
    request_id (int): 請假申請ID
    
    請求體：
    - approver_id: 審核人員ID（必填）
    - rejection_reason: 拒絕原因（必填）
    
    返回：
    - 審核結果
    """
    data = request.json
    
    if not data or not data.get("approver_id"):
        return jsonify({"error": "缺少審核人員ID"}), 400
    
    if not data.get("rejection_reason"):
        return jsonify({"error": "缺少拒絕原因"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查請假申請是否存在且為待審核狀態
        cursor.execute("""
            SELECT lr.id, lr.employee_id, lr.status, e.name as employee_name
            FROM leaves lr
            JOIN employees e ON lr.employee_id = e.id
            WHERE lr.id = ?
        """, (request_id,))
        
        leave_request = cursor.fetchone()
        if not leave_request:
            return jsonify({"error": "請假申請不存在"}), 404
        
        if leave_request[2] != "pending":
            return jsonify({"error": f"請假申請狀態為 {leave_request[2]}，無法審核"}), 400
        
        # 檢查審核人員是否存在
        cursor.execute("SELECT id, name FROM employees WHERE id = ?", (data["approver_id"],))
        approver = cursor.fetchone()
        if not approver:
            return jsonify({"error": "審核人員不存在"}), 404
        
        # 更新請假申請狀態
        cursor.execute("""
            UPDATE leaves 
            SET status = 'rejected', 
                approved_by = ?, 
                approved_at = datetime('now'),
                rejection_reason = ?,
                updated_at = datetime('now')
            WHERE id = ?
        """, (data["approver_id"], data["rejection_reason"], request_id))
        
        conn.commit()
        
        logger.info(f"請假申請 {request_id} 已拒絕，審核人：{approver[1]}")
        return jsonify({
            "message": "請假申請已拒絕",
            "request_id": request_id,
            "approver_name": approver[1],
            "employee_name": leave_request[3],
            "rejection_reason": data["rejection_reason"]
        })
        
    except Exception as e:
        logger.error(f"審核請假申請失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@leave_bp.route("/api/leave-requests/<int:request_id>", methods=["GET"])
def get_leave_request_detail(request_id):
    """
    獲取請假申請詳情
    
    參數：
    request_id (int): 請假申請ID
    
    返回：
    - 請假申請詳細資訊
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT
                lr.id, lr.employee_id, lr.leave_type, lr.start_date, lr.end_date,
                lr.leave_hours, lr.reason, lr.status, lr.created_at, lr.approved_at,
                lr.approved_by, lr.rejection_reason, lr.comments, lr.created_at, lr.updated_at,
                lr.attachments, lr.substitute_id, lr.emergency_contact,
                e.name as employee_name, e.employee_id as employee_code,
                lt.name as leave_type_name, lt.is_paid, lt.max_days_per_year,
                approver.name as approver_name,
                substitute.name as substitute_name
            FROM leaves lr
            LEFT JOIN employees e ON lr.employee_id = e.id
            LEFT JOIN leave_types lt ON lr.leave_type_id = lt.id
            LEFT JOIN employees approver ON lr.approved_by = approver.id
            LEFT JOIN employees substitute ON lr.substitute_id = substitute.id
            WHERE lr.id = ?
        """, (request_id,))
        
        row = cursor.fetchone()
        if not row:
            return jsonify({"error": "請假申請不存在"}), 404
        
        columns = [description[0] for description in cursor.description]
        record = dict(zip(columns, row))

        # 解析附件JSON
        if record.get('attachments'):
            import json
            try:
                record['attachments'] = json.loads(record['attachments'])
            except (json.JSONDecodeError, TypeError):
                record['attachments'] = []
        else:
            record['attachments'] = []

        return jsonify({
            "success": True,
            "record": record
        })
        
    except Exception as e:
        logger.error(f"獲取請假申請詳情失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@leave_bp.route("/api/leave-requests/statistics", methods=["GET"])
def get_leave_statistics():
    """
    獲取請假統計資料
    
    查詢參數：
    - year: 年份（預設：當前年份）
    - employee_id: 員工ID（可選）
    - department_id: 部門ID（可選）
    
    返回：
    - 請假統計資料
    """
    try:
        year = request.args.get('year', datetime.now().year, type=int)
        employee_id_param = request.args.get('employee_id')  # 可能是字符串編號或數字ID
        department_id = request.args.get('department_id', type=int)
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 構建查詢條件
        where_conditions = [f"strftime('%Y', lr.start_date) = '{year}'"]
        query_params = []
        employee_id = None
        
        if employee_id_param:
            # 如果是字符串編號（如E015），需要轉換為數字ID
            if isinstance(employee_id_param, str) and employee_id_param.startswith('E'):
                cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id_param,))
                result = cursor.fetchone()
                if result:
                    employee_id = result[0]
            else:
                # 如果是數字ID，直接使用
                try:
                    employee_id = int(employee_id_param)
                except ValueError:
                    pass
        
        if employee_id:
            where_conditions.append("lr.employee_id = ?")
            query_params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            query_params.append(department_id)
        
        where_clause = " AND ".join(where_conditions)
        
        # 按狀態統計
        cursor.execute(f"""
            SELECT 
                lr.status,
                COUNT(*) as count,
                SUM(lr.leave_hours) as total_days
            FROM leaves lr
            LEFT JOIN employees e ON lr.employee_id = e.id
            WHERE {where_clause}
            GROUP BY lr.status
        """, query_params)
        
        status_stats = {}
        for row in cursor.fetchall():
            status_stats[row[0]] = {
                "count": row[1],
                "total_days": row[2] or 0
            }
        
        # 按請假類型統計
        cursor.execute(f"""
            SELECT 
                lt.name as leave_type_name,
                COUNT(*) as count,
                SUM(lr.leave_hours) as total_days
            FROM leaves lr
            LEFT JOIN employees e ON lr.employee_id = e.id
            LEFT JOIN leave_types lt ON lr.leave_type_id = lt.id
            WHERE {where_clause} AND lr.status = 'approved'
            GROUP BY lt.id, lt.name
            ORDER BY total_days DESC
        """, query_params)
        
        type_stats = []
        for row in cursor.fetchall():
            type_stats.append({
                "leave_type_name": row[0],
                "count": row[1],
                "total_days": row[2] or 0
            })
        
        # 按月份統計
        cursor.execute(f"""
            SELECT 
                strftime('%m', lr.start_date) as month,
                COUNT(*) as count,
                SUM(lr.leave_hours) as total_days
            FROM leaves lr
            LEFT JOIN employees e ON lr.employee_id = e.id
            WHERE {where_clause} AND lr.status = 'approved'
            GROUP BY strftime('%m', lr.start_date)
            ORDER BY month
        """, query_params)
        
        monthly_stats = []
        for row in cursor.fetchall():
            monthly_stats.append({
                "month": int(row[0]),
                "count": row[1],
                "total_days": row[2] or 0
            })
        
        return jsonify({
            "success": True,
            "year": year,
            "status_statistics": status_stats,
            "type_statistics": type_stats,
            "monthly_statistics": monthly_stats
        })
        
    except Exception as e:
        logger.error(f"獲取請假統計失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# 為了向後相容，添加 /api/leaves 路由
@leave_bp.route("/api/leaves", methods=["GET"])
def get_leaves_compat():
    """
    獲取請假申請列表（相容路由）
    
    這是為了向後相容前端調用 /api/leaves 的路由
    實際功能與 /api/leave-requests 相同
    """
    try:
        # 獲取查詢參數
        employee_id_param = request.args.get('employee_id')
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        
        # 參數驗證
        if page < 1:
            return jsonify({"error": "頁碼必須大於 0"}), 400
        
        if limit < 1 or limit > 100:
            return jsonify({"error": "每頁筆數必須在 1-100 之間"}), 400
        
        # 計算偏移量
        offset = (page - 1) * limit
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        cursor = conn.cursor()
        
        # 構建查詢條件
        where_conditions = []
        query_params = []
        employee_id = None
        
        if employee_id_param:
            # 智能員工ID轉換
            if isinstance(employee_id_param, str) and employee_id_param.startswith('E'):
                cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id_param,))
                result = cursor.fetchone()
                if result:
                    employee_id = result[0]
            else:
                try:
                    employee_id = int(employee_id_param)
                except ValueError:
                    pass
        
        if employee_id:
            where_conditions.append("lr.employee_id = ?")
            query_params.append(employee_id)
        
        if status:
            where_conditions.append("lr.status = ?")
            query_params.append(status)
        
        if start_date:
            where_conditions.append("lr.start_date >= ?")
            query_params.append(start_date)
        
        if end_date:
            where_conditions.append("lr.end_date <= ?")
            query_params.append(end_date)
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        # 修復後的查詢 - 使用正確的欄位名稱，包含新的時間欄位
        records_query = f"""
            SELECT 
                lr.id, lr.employee_id, lr.leave_type, lr.start_date, lr.end_date,
                lr.leave_hours, lr.reason, lr.status, lr.created_at,
                lr.time_type, lr.start_time, lr.end_time,
                e.name as employee_name, e.employee_id as employee_code,
                lt.name as leave_type_name, lt.is_paid
            FROM leaves lr
            LEFT JOIN employees e ON lr.employee_id = e.id
            LEFT JOIN leave_types lt ON lr.leave_type = lt.code
            {where_clause}
            ORDER BY lr.created_at DESC
            LIMIT ? OFFSET ?
        """
        
        cursor.execute(records_query, query_params + [limit, offset])
        
        # 轉換為字典格式
        columns = [description[0] for description in cursor.description]
        records = []
        for row in cursor.fetchall():
            record = dict(zip(columns, row))
            records.append(record)
        
        # 查詢總記錄數
        count_query = f"""
            SELECT COUNT(*) as total
            FROM leaves lr
            LEFT JOIN employees e ON lr.employee_id = e.id
            LEFT JOIN leave_types lt ON lr.leave_type = lt.code
            {where_clause}
        """
        
        cursor.execute(count_query, query_params)
        total_records = cursor.fetchone()[0]
        
        # 計算總頁數
        total_pages = (total_records + limit - 1) // limit if limit > 0 else 1
        
        return jsonify({
            "success": True,
            "records": records,
            "pagination": {
                "total": total_records,
                "page": page,
                "limit": limit,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        })
        
    except Exception as e:
        logger.error(f"獲取請假申請列表失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@leave_bp.route("/api/leaves", methods=["POST"])
def create_leave_compat():
    """
    新增請假申請（相容路由）
    
    這是為了向後相容前端調用 /api/leaves 的路由
    """
    data = request.json
    
    # 驗證必要欄位
    required_fields = ["employee_id", "leave_type", "start_date", "end_date", "reason"]
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400
    
    # 驗證日期格式
    try:
        start_date = datetime.strptime(data["start_date"], "%Y-%m-%d").date()
        end_date = datetime.strptime(data["end_date"], "%Y-%m-%d").date()
    except ValueError:
        return jsonify({"error": "日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
    
    # 驗證日期邏輯
    if start_date > end_date:
        return jsonify({"error": "開始日期不能晚於結束日期"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 驗證員工是否存在
        cursor.execute("SELECT id FROM employees WHERE id = ?", (data["employee_id"],))
        if not cursor.fetchone():
            return jsonify({"error": "員工不存在"}), 404
        
        # 驗證請假類型是否存在
        cursor.execute("""
            SELECT code, name, requires_approval 
            FROM leave_types 
            WHERE code = ? AND is_active = 1
        """, (data["leave_type"],))
        
        leave_type_info = cursor.fetchone()
        if not leave_type_info:
            return jsonify({"error": "請假類型不存在"}), 404
        
        # 獲取時間類型和時間
        time_type = data.get("time_type", "full_day")
        start_time = data.get("start_time")
        end_time = data.get("end_time")
        
        # 計算請假時數
        if time_type == "partial_day" and start_time and end_time:
            # 部分工時：根據具體時間計算
            leave_hours = calculate_partial_leave_hours(
                data["start_date"], data["end_date"], start_time, end_time
            )
        else:
            # 全天請假：天數 * 8小時
            leave_days = (end_date - start_date).days + 1
            leave_hours = data.get("leave_hours", leave_days * 8)
        
        # 插入請假申請
        cursor.execute("""
            INSERT INTO leaves (
                employee_id, leave_type, start_date, end_date, 
                leave_hours, reason, status, substitute_id, emergency_contact, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
        """, (
            data["employee_id"],
            data["leave_type"],
            data["start_date"],
            data["end_date"],
            leave_hours,
            data["reason"],
            "pending",  # 預設狀態為待審核
            data.get("substitute_id"),
            data.get("emergency_contact", "")
        ))
        
        leave_id = cursor.lastrowid
        conn.commit()
        
        logger.info(f"請假申請新增成功: 員工ID {data['employee_id']}, 請假ID {leave_id}")
        return jsonify({
            "success": True,
            "message": "請假申請提交成功",
            "id": leave_id,
            "status": "pending"
        }), 201
        
    except Exception as e:
        logger.error(f"新增請假申請失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@leave_bp.route("/api/leaves/<int:leave_id>", methods=["GET"])
def get_leave_detail_compat(leave_id):
    """
    獲取請假申請詳情（相容路由）
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT 
                lr.id, lr.employee_id, lr.leave_type, lr.start_date, lr.end_date,
                lr.leave_hours, lr.reason, lr.status, lr.created_at,
                e.name as employee_name, e.employee_id as employee_code,
                lt.name as leave_type_name, lt.is_paid
            FROM leaves lr
            LEFT JOIN employees e ON lr.employee_id = e.id
            LEFT JOIN leave_types lt ON lr.leave_type = lt.code
            WHERE lr.id = ?
        """, (leave_id,))
        
        row = cursor.fetchone()
        if not row:
            return jsonify({"error": "請假申請不存在"}), 404
        
        # 轉換為字典格式
        columns = [description[0] for description in cursor.description]
        record = dict(zip(columns, row))
        
        return jsonify({
            "success": True,
            "record": record
        })
        
    except Exception as e:
        logger.error(f"獲取請假申請詳情失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@leave_bp.route("/api/leaves/<int:leave_id>", methods=["PUT"])
def update_leave_compat(leave_id):
    """
    更新請假申請（相容路由）
    """
    data = request.json
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查請假申請是否存在
        cursor.execute("SELECT status FROM leaves WHERE id = ?", (leave_id,))
        result = cursor.fetchone()
        if not result:
            return jsonify({"error": "請假申請不存在"}), 404
        
        # 只有待審核的申請才能修改
        if result[0] != "pending":
            return jsonify({"error": "只有待審核的申請才能修改"}), 400
        
        # 構建更新語句
        update_fields = []
        update_values = []
        
        allowed_fields = ["leave_type", "start_date", "end_date", "leave_hours", "reason"]
        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = ?")
                update_values.append(data[field])
        
        if not update_fields:
            return jsonify({"error": "沒有提供要更新的欄位"}), 400
        
        # 添加更新時間
        update_fields.append("updated_at = ?")
        update_values.append(datetime.now().isoformat())
        update_values.append(leave_id)
        
        cursor.execute(f"""
            UPDATE leaves 
            SET {', '.join(update_fields)}
            WHERE id = ?
        """, update_values)
        
        conn.commit()
        
        logger.info(f"請假申請更新成功: ID {leave_id}")
        return jsonify({
            "success": True,
            "message": "請假申請更新成功"
        })
        
    except Exception as e:
        logger.error(f"更新請假申請失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@leave_bp.route("/api/leaves/<int:leave_id>", methods=["DELETE"])
def delete_leave_compat(leave_id):
    """
    刪除請假申請（相容路由）
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查請假申請是否存在
        cursor.execute("SELECT status FROM leaves WHERE id = ?", (leave_id,))
        result = cursor.fetchone()
        if not result:
            return jsonify({"error": "請假申請不存在"}), 404
        
        # 只有待審核和已拒絕的申請才能刪除
        if result[0] not in ["pending", "rejected"]:
            return jsonify({"error": "只有待審核和已拒絕的申請才能刪除"}), 400
        
        cursor.execute("DELETE FROM leaves WHERE id = ?", (leave_id,))
        conn.commit()
        
        logger.info(f"請假申請刪除成功: ID {leave_id}")
        return jsonify({
            "success": True,
            "message": "請假申請刪除成功"
        })
        
    except Exception as e:
        logger.error(f"刪除請假申請失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# 這裡之後會添加其他請假管理相關的API
# 例如：請假餘額查詢、請假日曆等 