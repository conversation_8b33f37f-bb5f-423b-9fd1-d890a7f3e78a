#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考勤規則管理 API
提供考勤規則的查詢、配置和管理功能
"""

import logging
import sys
import os
from datetime import datetime, date
from flask import Blueprint, request, jsonify, session
from typing import Dict, List, Any, Optional

# 添加父目錄到路徑以便導入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 導入安全工具
try:
    from utils.security import InputValidator, SecurityAudit, sanitize_input
except ImportError:
    # 簡單的替代實作
    class InputValidator:
        @staticmethod
        def sanitize_string(s, max_len=255):
            return str(s)[:max_len]

    class SecurityAudit:
        @staticmethod
        def log_security_event(event_type, details):
            print(f"Security Event: {event_type} - {details}")

    def sanitize_input(s, max_len=255):
        return str(s)[:max_len]

# 導入進階考勤規則
try:
    from services.advanced_attendance_rules import (
        advanced_rules, AttendanceRuleType, WorkPattern, AttendanceContext
    )
    from services.performance_optimizer import db_optimizer, performance_monitor
    ADVANCED_FEATURES_AVAILABLE = True
except ImportError:
    ADVANCED_FEATURES_AVAILABLE = False

# 創建藍圖
attendance_rules_bp = Blueprint("attendance_rules", __name__)

# 設置日誌
logger = logging.getLogger(__name__)

@attendance_rules_bp.route("/api/attendance-rules", methods=["GET"])
@performance_monitor("get_attendance_rules") if ADVANCED_FEATURES_AVAILABLE else lambda x: x
def get_attendance_rules():
    """
    獲取考勤規則列表
    
    返回：
    - 考勤規則列表和統計資訊
    """
    try:
        if not ADVANCED_FEATURES_AVAILABLE:
            return jsonify({
                "success": False,
                "error": "進階考勤規則功能不可用"
            }), 503
        
        # 獲取規則列表
        rules_data = []
        for rule in advanced_rules.rules:
            rules_data.append({
                "rule_id": rule.rule_id,
                "rule_type": rule.rule_type.value,
                "name": rule.name,
                "description": rule.description,
                "priority": rule.priority,
                "is_active": rule.is_active,
                "effective_date": rule.effective_date.isoformat() if rule.effective_date else None,
                "expiry_date": rule.expiry_date.isoformat() if rule.expiry_date else None,
                "conditions": rule.conditions,
                "actions": rule.actions
            })
        
        # 按類型分組統計
        rule_stats = {}
        for rule_type in AttendanceRuleType:
            rule_stats[rule_type.value] = len([r for r in advanced_rules.rules if r.rule_type == rule_type])
        
        return jsonify({
            "success": True,
            "data": {
                "rules": rules_data,
                "total_count": len(rules_data),
                "active_count": len([r for r in advanced_rules.rules if r.is_active]),
                "rule_type_stats": rule_stats
            }
        })
        
    except Exception as e:
        logger.error(f"獲取考勤規則失敗: {e}")
        return jsonify({
            "success": False,
            "error": "獲取考勤規則失敗"
        }), 500

@attendance_rules_bp.route("/api/attendance-rules/test", methods=["POST"])
@performance_monitor("test_attendance_rules") if ADVANCED_FEATURES_AVAILABLE else lambda x: x
def test_attendance_rules():
    """
    測試考勤規則應用
    
    請求參數：
    - employee_id: 員工ID
    - work_date: 工作日期
    - check_in: 上班時間
    - check_out: 下班時間
    - weather_condition: 天氣狀況（可選）
    - traffic_condition: 交通狀況（可選）
    
    返回：
    - 規則應用結果
    """
    try:
        if not ADVANCED_FEATURES_AVAILABLE:
            return jsonify({
                "success": False,
                "error": "進階考勤規則功能不可用"
            }), 503
        
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "請提供測試資料"
            }), 400
        
        # 驗證必要參數
        required_fields = ["employee_id", "work_date"]
        for field in required_fields:
            if field not in data:
                return jsonify({
                    "success": False,
                    "error": f"缺少必要參數: {field}"
                }), 400
        
        # 清理輸入
        employee_id = int(data["employee_id"])
        work_date = datetime.strptime(data["work_date"], "%Y-%m-%d").date()
        
        # 解析時間
        check_in = None
        check_out = None
        
        if data.get("check_in"):
            check_in = datetime.strptime(f"{data['work_date']} {data['check_in']}", "%Y-%m-%d %H:%M")
        
        if data.get("check_out"):
            check_out = datetime.strptime(f"{data['work_date']} {data['check_out']}", "%Y-%m-%d %H:%M")
        
        # 創建測試上下文
        context = AttendanceContext(
            employee_id=employee_id,
            work_date=work_date,
            department_id=data.get("department_id", 1),
            position=data.get("position", "測試職位"),
            work_pattern=WorkPattern(data.get("work_pattern", "standard")),
            shift_info={
                "start_time": "09:00",
                "end_time": "18:00"
            },
            weather_condition=data.get("weather_condition"),
            traffic_condition=data.get("traffic_condition"),
            special_events=data.get("special_events", [])
        )
        
        # 應用規則
        result = advanced_rules.apply_rules(context, check_in, check_out)
        
        return jsonify({
            "success": True,
            "data": {
                "test_context": {
                    "employee_id": employee_id,
                    "work_date": work_date.isoformat(),
                    "check_in": check_in.isoformat() if check_in else None,
                    "check_out": check_out.isoformat() if check_out else None,
                    "weather_condition": context.weather_condition,
                    "traffic_condition": context.traffic_condition
                },
                "rule_result": result
            }
        })
        
    except ValueError as e:
        return jsonify({
            "success": False,
            "error": f"參數格式錯誤: {str(e)}"
        }), 400
    except Exception as e:
        logger.error(f"測試考勤規則失敗: {e}")
        return jsonify({
            "success": False,
            "error": "測試考勤規則失敗"
        }), 500

@attendance_rules_bp.route("/api/attendance-rules/performance", methods=["GET"])
def get_performance_stats():
    """
    獲取考勤規則效能統計
    
    返回：
    - 效能統計資訊
    """
    try:
        if not ADVANCED_FEATURES_AVAILABLE:
            return jsonify({
                "success": False,
                "error": "效能監控功能不可用"
            }), 503
        
        # 獲取效能統計
        performance_stats = db_optimizer.get_performance_stats()
        
        return jsonify({
            "success": True,
            "data": {
                "performance_stats": performance_stats,
                "timestamp": datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"獲取效能統計失敗: {e}")
        return jsonify({
            "success": False,
            "error": "獲取效能統計失敗"
        }), 500

@attendance_rules_bp.route("/api/attendance-rules/cache/clear", methods=["POST"])
def clear_cache():
    """
    清空考勤規則快取
    
    返回：
    - 清空結果
    """
    try:
        if not ADVANCED_FEATURES_AVAILABLE:
            return jsonify({
                "success": False,
                "error": "快取管理功能不可用"
            }), 503
        
        # 檢查權限
        employee_id = session.get("employee_id")
        if not employee_id:
            return jsonify({
                "success": False,
                "error": "請先登入"
            }), 401
        
        # 清空快取
        db_optimizer.query_cache.clear()
        
        SecurityAudit.log_security_event("cache_cleared", {
            "employee_id": employee_id,
            "timestamp": datetime.now().isoformat()
        })
        
        return jsonify({
            "success": True,
            "message": "快取已清空"
        })
        
    except Exception as e:
        logger.error(f"清空快取失敗: {e}")
        return jsonify({
            "success": False,
            "error": "清空快取失敗"
        }), 500

@attendance_rules_bp.route("/api/attendance-rules/work-patterns", methods=["GET"])
def get_work_patterns():
    """
    獲取工作模式列表
    
    返回：
    - 工作模式列表
    """
    try:
        if not ADVANCED_FEATURES_AVAILABLE:
            return jsonify({
                "success": False,
                "error": "進階功能不可用"
            }), 503
        
        patterns = []
        for pattern in WorkPattern:
            patterns.append({
                "value": pattern.value,
                "name": pattern.name,
                "description": {
                    "standard": "標準工時",
                    "flexible": "彈性工時",
                    "shift": "輪班制",
                    "remote": "遠程工作",
                    "hybrid": "混合模式"
                }.get(pattern.value, pattern.value)
            })
        
        return jsonify({
            "success": True,
            "data": {
                "work_patterns": patterns
            }
        })
        
    except Exception as e:
        logger.error(f"獲取工作模式失敗: {e}")
        return jsonify({
            "success": False,
            "error": "獲取工作模式失敗"
        }), 500

@attendance_rules_bp.route("/api/attendance-rules/rule-types", methods=["GET"])
def get_rule_types():
    """
    獲取規則類型列表
    
    返回：
    - 規則類型列表
    """
    try:
        if not ADVANCED_FEATURES_AVAILABLE:
            return jsonify({
                "success": False,
                "error": "進階功能不可用"
            }), 503
        
        rule_types = []
        for rule_type in AttendanceRuleType:
            rule_types.append({
                "value": rule_type.value,
                "name": rule_type.name,
                "description": {
                    "basic": "基本規則",
                    "flexible": "彈性規則",
                    "smart": "智慧規則",
                    "compliance": "合規規則",
                    "seasonal": "季節性規則"
                }.get(rule_type.value, rule_type.value)
            })
        
        return jsonify({
            "success": True,
            "data": {
                "rule_types": rule_types
            }
        })
        
    except Exception as e:
        logger.error(f"獲取規則類型失敗: {e}")
        return jsonify({
            "success": False,
            "error": "獲取規則類型失敗"
        }), 500


if __name__ == "__main__":
    # 測試程式碼
    print("🧪 測試考勤規則管理 API")
    print("=" * 50)
    
    if ADVANCED_FEATURES_AVAILABLE:
        print("✅ 進階考勤規則功能可用")
        print(f"✅ 載入規則數量: {len(advanced_rules.rules)}")
    else:
        print("❌ 進階考勤規則功能不可用")
    
    print("\n✅ 考勤規則管理 API 測試完成")
