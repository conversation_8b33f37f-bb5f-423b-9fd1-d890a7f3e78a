#!/usr/bin/env python3
"""
直接測試API功能
"""


from flask import Flask

from api.attendance_processing_api import attendance_processing_bp


def test_api():
    # 創建測試應用
    app = Flask(__name__)
    app.register_blueprint(attendance_processing_bp)

    with app.test_client() as client:
        # 測試預覽API
        print("🧪 測試預覽API...")
        response = client.post(
            "/api/attendance/processing/preview",
            json={
                "date_range": {"start_date": "2025-06-15", "end_date": "2025-06-15"},
                "employee_scope": {"type": "all"},
            },
        )

        print(f"狀態碼: {response.status_code}")
        print(f"響應: {response.get_json()}")

        # 測試執行API
        print("\n🧪 測試執行API...")
        response = client.post(
            "/api/attendance/processing/execute",
            json={
                "date_range": {"start_date": "2025-06-15", "end_date": "2025-06-15"},
                "employee_scope": {"type": "all"},
                "processing_options": {
                    "calculate_late_early": True,
                    "calculate_overtime": True,
                    "integrate_leaves": True,
                    "overwrite_existing": False,
                },
            },
        )

        print(f"狀態碼: {response.status_code}")
        result = response.get_json()
        print(f"響應: {result}")

        if result and result.get("success"):
            processing_id = result.get("processing_id")
            print(f"\n🧪 測試狀態查詢API...")

            # 等待一下讓處理開始
            import time

            time.sleep(1)

            response = client.get(f"/api/attendance/processing/status/{processing_id}")
            print(f"狀態碼: {response.status_code}")
            print(f"響應: {response.get_json()}")


if __name__ == "__main__":
    test_api()
