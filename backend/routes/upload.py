"""
文件上傳路由
處理各種文件上傳需求，包括請假文件、頭像等
"""

import os
import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage
import mimetypes
from database import create_connection

upload_bp = Blueprint('upload', __name__)

# 允許的文件類型
ALLOWED_EXTENSIONS = {
    'image': {'jpg', 'jpeg', 'png', 'gif', 'webp'},
    'document': {'pdf', 'doc', 'docx', 'txt'},
    'all': {'jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx', 'txt'}
}

# 文件大小限制 (bytes)
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

def allowed_file(filename, file_type='all'):
    """檢查文件是否允許上傳"""
    if '.' not in filename:
        return False
    
    extension = filename.rsplit('.', 1)[1].lower()
    return extension in ALLOWED_EXTENSIONS.get(file_type, ALLOWED_EXTENSIONS['all'])

def get_file_type(filename):
    """根據文件名獲取MIME類型"""
    mime_type, _ = mimetypes.guess_type(filename)
    return mime_type or 'application/octet-stream'

def create_upload_directory(upload_type):
    """創建上傳目錄"""
    base_dir = current_app.config.get('UPLOAD_FOLDER', 'uploads')
    upload_dir = os.path.join(base_dir, upload_type)
    
    if not os.path.exists(upload_dir):
        os.makedirs(upload_dir, exist_ok=True)
    
    return upload_dir

@upload_bp.route('/api/upload', methods=['POST'])
def upload_file():
    """
    文件上傳API
    支援多種文件類型和用途
    """
    try:
        # 檢查是否有文件
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '沒有選擇文件'
            }), 400

        file = request.files['file']
        upload_type = request.form.get('type', 'general')  # 上傳類型：leave_attachment, avatar, general

        # 檢查文件是否為空
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '沒有選擇文件'
            }), 400

        # 檢查文件大小
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > MAX_FILE_SIZE:
            return jsonify({
                'success': False,
                'error': f'文件大小超過限制 ({MAX_FILE_SIZE / 1024 / 1024:.1f}MB)'
            }), 400

        # 檢查文件類型
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': '不支援的文件格式'
            }), 400

        # 生成安全的文件名
        original_filename = secure_filename(file.filename)
        file_extension = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else ''
        unique_filename = f"{uuid.uuid4().hex}.{file_extension}"

        # 創建上傳目錄
        upload_dir = create_upload_directory(upload_type)
        file_path = os.path.join(upload_dir, unique_filename)

        # 保存文件
        file.save(file_path)

        # 生成文件URL
        file_url = f"/uploads/{upload_type}/{unique_filename}"

        # 記錄到資料庫
        conn = create_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO file_uploads (
                filename, original_filename, file_path, file_url,
                file_size, mime_type, upload_type, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            unique_filename,
            original_filename,
            file_path,
            file_url,
            file_size,
            get_file_type(original_filename),
            upload_type,
            datetime.now()
        ))
        
        file_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '文件上傳成功',
            'data': {
                'id': file_id,
                'filename': unique_filename,
                'original_filename': original_filename,
                'url': file_url,
                'size': file_size,
                'type': get_file_type(original_filename)
            }
        })

    except Exception as e:
        current_app.logger.error(f"文件上傳錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': '文件上傳失敗'
        }), 500

@upload_bp.route('/api/upload/<int:file_id>', methods=['DELETE'])
def delete_file(file_id):
    """刪除上傳的文件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 檢查文件是否存在
        cursor.execute("""
            SELECT file_path, uploaded_by FROM file_uploads
            WHERE id = ?
        """, (file_id,))
        
        file_record = cursor.fetchone()
        if not file_record:
            return jsonify({
                'success': False,
                'error': '文件不存在'
            }), 404

        file_path = file_record[0]

        # 刪除物理文件
        if os.path.exists(file_path):
            os.remove(file_path)

        # 刪除資料庫記錄
        cursor.execute("DELETE FROM file_uploads WHERE id = ?", (file_id,))
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '文件刪除成功'
        })

    except Exception as e:
        current_app.logger.error(f"文件刪除錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': '文件刪除失敗'
        }), 500
