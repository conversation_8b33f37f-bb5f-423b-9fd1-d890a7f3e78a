#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSRF 防護中間件
提供跨站請求偽造攻擊防護
"""

import secrets
import hashlib
import hmac
import logging
import sys
import os
from functools import wraps
from flask import request, session, jsonify, g

# 添加父目錄到路徑以便導入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils.security import SecurityAudit
except ImportError:
    # 如果無法導入，創建一個簡單的替代
    class SecurityAudit:
        @staticmethod
        def log_security_event(event_type, details):
            print(f"Security Event: {event_type} - {details}")

logger = logging.getLogger(__name__)

class CSRFProtection:
    """CSRF 防護類"""
    
    def __init__(self, app=None, secret_key=None):
        """
        初始化 CSRF 防護
        
        Args:
            app: Flask 應用實例
            secret_key: 用於生成 CSRF token 的密鑰
        """
        self.secret_key = secret_key or secrets.token_hex(32)
        self.exempt_views = set()
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """
        初始化 Flask 應用的 CSRF 防護
        
        Args:
            app: Flask 應用實例
        """
        app.before_request(self.protect)
        app.after_request(self.inject_csrf_token)
        
        # 將 CSRF 實例添加到應用配置
        if not hasattr(app, 'extensions'):
            app.extensions = {}
        app.extensions['csrf'] = self
    
    def generate_csrf_token(self, session_id=None):
        """
        生成 CSRF token
        
        Args:
            session_id: 會話 ID
            
        Returns:
            CSRF token 字串
        """
        if not session_id:
            session_id = session.get('csrf_session_id')
            if not session_id:
                session_id = secrets.token_hex(16)
                session['csrf_session_id'] = session_id
        
        # 使用 HMAC 生成安全的 token
        message = f"{session_id}:{secrets.token_hex(16)}"
        token = hmac.new(
            self.secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # 儲存 token 到會話
        session['csrf_token'] = token
        
        return token
    
    def validate_csrf_token(self, token):
        """
        驗證 CSRF token
        
        Args:
            token: 要驗證的 token
            
        Returns:
            是否有效
        """
        if not token:
            return False
        
        stored_token = session.get('csrf_token')
        if not stored_token:
            return False
        
        # 使用安全的字串比較
        return hmac.compare_digest(token, stored_token)
    
    def protect(self):
        """
        CSRF 防護檢查
        """
        # 跳過 GET、HEAD、OPTIONS 請求
        if request.method in ('GET', 'HEAD', 'OPTIONS'):
            return
        
        # 跳過豁免的視圖
        endpoint = request.endpoint
        if endpoint in self.exempt_views:
            return
        
        # 跳過某些 API 端點（可以根據需要調整）
        exempt_paths = [
            '/api/login',  # 登入端點
            '/api/health',  # 健康檢查
        ]
        
        if request.path in exempt_paths:
            return
        
        # 檢查 CSRF token
        token = None
        
        # 從 header 獲取 token
        token = request.headers.get('X-CSRF-Token')
        
        # 如果 header 中沒有，從表單數據獲取
        if not token and request.form:
            token = request.form.get('csrf_token')
        
        # 如果還是沒有，從 JSON 數據獲取
        if not token and request.is_json:
            data = request.get_json(silent=True)
            if data:
                token = data.get('csrf_token')
        
        # 驗證 token
        if not self.validate_csrf_token(token):
            SecurityAudit.log_security_event("csrf_token_invalid", {
                "path": request.path,
                "method": request.method,
                "ip": request.remote_addr,
                "user_agent": request.headers.get('User-Agent', ''),
                "token_provided": bool(token)
            })
            
            logger.warning(f"CSRF token 驗證失敗: {request.path}")
            return jsonify({
                "error": "CSRF token 無效或缺失",
                "code": "CSRF_TOKEN_INVALID"
            }), 403
    
    def inject_csrf_token(self, response):
        """
        在回應中注入 CSRF token
        
        Args:
            response: Flask 回應對象
            
        Returns:
            修改後的回應對象
        """
        # 只在成功的回應中注入 token
        if response.status_code == 200:
            # 生成新的 CSRF token
            token = self.generate_csrf_token()
            
            # 添加到 header
            response.headers['X-CSRF-Token'] = token
            
            # 如果是 JSON 回應，也可以添加到回應體中
            if response.is_json and hasattr(response, 'json'):
                try:
                    data = response.get_json()
                    if isinstance(data, dict):
                        data['csrf_token'] = token
                        response.data = response.json.dumps(data)
                except:
                    pass  # 如果無法修改 JSON，就只在 header 中提供
        
        return response
    
    def exempt(self, view):
        """
        豁免視圖的 CSRF 檢查
        
        Args:
            view: 視圖函數或端點名稱
            
        Returns:
            裝飾器函數
        """
        if isinstance(view, str):
            self.exempt_views.add(view)
            return view
        else:
            # 如果是函數，添加到豁免列表並返回裝飾器
            endpoint = f"{view.__module__}.{view.__name__}"
            self.exempt_views.add(endpoint)
            
            @wraps(view)
            def wrapped_view(*args, **kwargs):
                return view(*args, **kwargs)
            return wrapped_view


def csrf_protect(f):
    """
    CSRF 防護裝飾器
    
    Args:
        f: 要保護的視圖函數
        
    Returns:
        裝飾後的函數
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 檢查是否已經有 CSRF 防護
        if hasattr(g, 'csrf_protected'):
            return f(*args, **kwargs)
        
        # 標記為已保護
        g.csrf_protected = True
        
        # 執行 CSRF 檢查
        if request.method not in ('GET', 'HEAD', 'OPTIONS'):
            token = request.headers.get('X-CSRF-Token')
            if not token and request.form:
                token = request.form.get('csrf_token')
            if not token and request.is_json:
                data = request.get_json(silent=True)
                if data:
                    token = data.get('csrf_token')
            
            stored_token = session.get('csrf_token')
            if not token or not stored_token or not hmac.compare_digest(token, stored_token):
                SecurityAudit.log_security_event("csrf_decorator_failed", {
                    "function": f.__name__,
                    "path": request.path,
                    "method": request.method,
                    "ip": request.remote_addr
                })
                return jsonify({
                    "error": "CSRF token 無效",
                    "code": "CSRF_TOKEN_INVALID"
                }), 403
        
        return f(*args, **kwargs)
    
    return decorated_function


def get_csrf_token():
    """
    獲取當前的 CSRF token
    
    Returns:
        CSRF token 字串
    """
    csrf = None
    try:
        from flask import current_app
        csrf = current_app.extensions.get('csrf')
    except:
        pass
    
    if csrf:
        return csrf.generate_csrf_token()
    else:
        # 如果沒有 CSRF 實例，生成簡單的 token
        if 'csrf_token' not in session:
            session['csrf_token'] = secrets.token_hex(32)
        return session['csrf_token']


# 全域 CSRF 防護實例
csrf = CSRFProtection()


if __name__ == "__main__":
    # 測試程式碼
    print("🧪 測試 CSRF 防護模組")
    print("=" * 50)

    csrf_instance = CSRFProtection()

    # 測試基本功能（不需要 Flask 上下文）
    print("✅ CSRF 防護類初始化成功")
    print(f"✅ 密鑰長度: {len(csrf_instance.secret_key)} 字符")
    print(f"✅ 豁免視圖集合: {len(csrf_instance.exempt_views)} 項")

    # 測試 HMAC 功能
    test_message = "test_message"
    test_key = "test_key"
    hmac_result = hmac.new(test_key.encode(), test_message.encode(), hashlib.sha256).hexdigest()
    print(f"✅ HMAC 測試成功: {hmac_result[:20]}...")

    print("\n✅ CSRF 防護模組基本功能測試完成")
    print("注意：完整測試需要在 Flask 應用上下文中進行")
