#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL 安全中間件
提供安全的資料庫查詢包裝器，防止 SQL 注入攻擊
"""

import logging
import sqlite3
import re
from typing import Any, List, Optional, Tuple, Dict
from functools import wraps
from utils.security import SecurityAudit, InputValidator

logger = logging.getLogger(__name__)

class SecureDatabase:
    """安全資料庫包裝器"""
    
    # 允許的表名白名單
    ALLOWED_TABLES = {
        'employees', 'departments', 'attendance', 'attendance_records', 
        'punch_records', 'leaves', 'schedules', 'shifts', 'shift_templates',
        'leave_types', 'permissions', 'user_sessions', 'schedule_rules',
        'work_reports', 'overtime_requests', 'holidays', 'notifications'
    }
    
    # 允許的欄位名白名單（常用欄位）
    ALLOWED_COLUMNS = {
        'id', 'name', 'employee_id', 'department_id', 'status', 'created_at',
        'updated_at', 'check_in', 'check_out', 'work_date', 'start_date',
        'end_date', 'leave_type', 'reason', 'approver_id', 'approved_at'
    }
    
    # 允許的排序欄位
    ALLOWED_ORDER_BY = {
        'id', 'name', 'created_at', 'updated_at', 'check_in', 'work_date',
        'start_date', 'end_date', 'employee_id'
    }
    
    def __init__(self, connection):
        """
        初始化安全資料庫包裝器
        
        Args:
            connection: 資料庫連接
        """
        self.connection = connection
        self.cursor = connection.cursor()
    
    def validate_table_name(self, table_name: str) -> bool:
        """
        驗證表名是否在白名單中
        
        Args:
            table_name: 表名
            
        Returns:
            是否有效
        """
        return table_name in self.ALLOWED_TABLES
    
    def validate_column_name(self, column_name: str) -> bool:
        """
        驗證欄位名是否安全
        
        Args:
            column_name: 欄位名
            
        Returns:
            是否有效
        """
        # 檢查是否在白名單中或符合安全模式
        if column_name in self.ALLOWED_COLUMNS:
            return True
        
        # 允許簡單的欄位名（字母、數字、底線）
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*$'
        return bool(re.match(pattern, column_name))
    
    def validate_order_by(self, order_by: str) -> bool:
        """
        驗證 ORDER BY 子句是否安全
        
        Args:
            order_by: 排序子句
            
        Returns:
            是否有效
        """
        # 移除空白並轉為小寫
        order_by = order_by.strip().lower()
        
        # 檢查基本格式
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*(\s+(asc|desc))?$'
        if not re.match(pattern, order_by):
            return False
        
        # 提取欄位名
        field_name = order_by.split()[0]
        return field_name in self.ALLOWED_ORDER_BY
    
    def safe_execute(self, query: str, params: Optional[Tuple] = None, 
                    table_name: Optional[str] = None) -> sqlite3.Cursor:
        """
        安全執行 SQL 查詢
        
        Args:
            query: SQL 查詢
            params: 查詢參數
            table_name: 表名（用於驗證）
            
        Returns:
            查詢結果游標
            
        Raises:
            ValueError: 查詢不安全時拋出
        """
        try:
            # 檢查 SQL 注入風險
            risk_assessment = SecurityAudit.check_sql_injection_risk(query)
            
            if risk_assessment['risk_level'] == 'high':
                SecurityAudit.log_security_event("sql_injection_attempt", {
                    "query": query,
                    "risks": risk_assessment['issues']
                })
                raise ValueError(f"SQL 查詢存在安全風險: {', '.join(risk_assessment['issues'])}")
            
            # 驗證表名（如果提供）
            if table_name and not self.validate_table_name(table_name):
                SecurityAudit.log_security_event("invalid_table_access", {
                    "table_name": table_name,
                    "query": query
                })
                raise ValueError(f"不允許訪問表: {table_name}")
            
            # 記錄查詢（僅在開發模式）
            logger.debug(f"執行 SQL 查詢: {query[:100]}...")
            
            # 執行查詢
            if params:
                return self.cursor.execute(query, params)
            else:
                return self.cursor.execute(query)
                
        except Exception as e:
            logger.error(f"SQL 查詢執行失敗: {e}")
            SecurityAudit.log_security_event("sql_execution_error", {
                "query": query,
                "error": str(e)
            })
            raise
    
    def safe_select(self, table_name: str, columns: List[str] = None, 
                   where_clause: str = "", params: Tuple = None,
                   order_by: str = "", limit: int = None) -> List[Dict[str, Any]]:
        """
        安全的 SELECT 查詢
        
        Args:
            table_name: 表名
            columns: 要查詢的欄位列表
            where_clause: WHERE 子句
            params: 查詢參數
            order_by: 排序欄位
            limit: 限制筆數
            
        Returns:
            查詢結果列表
        """
        # 驗證表名
        if not self.validate_table_name(table_name):
            raise ValueError(f"不允許訪問表: {table_name}")
        
        # 驗證欄位名
        if columns:
            for col in columns:
                if not self.validate_column_name(col):
                    raise ValueError(f"不允許訪問欄位: {col}")
            columns_str = ", ".join(columns)
        else:
            columns_str = "*"
        
        # 構建查詢
        query = f"SELECT {columns_str} FROM {table_name}"
        
        # 添加 WHERE 子句
        if where_clause:
            query += f" WHERE {where_clause}"
        
        # 添加 ORDER BY
        if order_by:
            if not self.validate_order_by(order_by):
                raise ValueError(f"不安全的排序欄位: {order_by}")
            query += f" ORDER BY {order_by}"
        
        # 添加 LIMIT
        if limit:
            query += f" LIMIT {int(limit)}"
        
        # 執行查詢
        cursor = self.safe_execute(query, params, table_name)
        
        # 轉換為字典列表
        columns = [description[0] for description in cursor.description]
        results = []
        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))
        
        return results
    
    def safe_insert(self, table_name: str, data: Dict[str, Any]) -> int:
        """
        安全的 INSERT 操作
        
        Args:
            table_name: 表名
            data: 要插入的資料
            
        Returns:
            插入記錄的 ID
        """
        # 驗證表名
        if not self.validate_table_name(table_name):
            raise ValueError(f"不允許訪問表: {table_name}")
        
        # 驗證欄位名
        for column in data.keys():
            if not self.validate_column_name(column):
                raise ValueError(f"不允許訪問欄位: {column}")
        
        # 構建查詢
        columns = list(data.keys())
        placeholders = ", ".join(["?" for _ in columns])
        columns_str = ", ".join(columns)
        
        query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        params = tuple(data.values())
        
        # 執行查詢
        self.safe_execute(query, params, table_name)
        return self.cursor.lastrowid
    
    def safe_update(self, table_name: str, data: Dict[str, Any], 
                   where_clause: str, where_params: Tuple) -> int:
        """
        安全的 UPDATE 操作
        
        Args:
            table_name: 表名
            data: 要更新的資料
            where_clause: WHERE 子句
            where_params: WHERE 參數
            
        Returns:
            影響的記錄數
        """
        # 驗證表名
        if not self.validate_table_name(table_name):
            raise ValueError(f"不允許訪問表: {table_name}")
        
        # 驗證欄位名
        for column in data.keys():
            if not self.validate_column_name(column):
                raise ValueError(f"不允許訪問欄位: {column}")
        
        # 構建 SET 子句
        set_clauses = [f"{column} = ?" for column in data.keys()]
        set_clause = ", ".join(set_clauses)
        
        # 構建查詢
        query = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause}"
        params = tuple(data.values()) + where_params
        
        # 執行查詢
        self.safe_execute(query, params, table_name)
        return self.cursor.rowcount
    
    def safe_delete(self, table_name: str, where_clause: str, 
                   where_params: Tuple) -> int:
        """
        安全的 DELETE 操作
        
        Args:
            table_name: 表名
            where_clause: WHERE 子句
            where_params: WHERE 參數
            
        Returns:
            刪除的記錄數
        """
        # 驗證表名
        if not self.validate_table_name(table_name):
            raise ValueError(f"不允許訪問表: {table_name}")
        
        # 構建查詢
        query = f"DELETE FROM {table_name} WHERE {where_clause}"
        
        # 執行查詢
        self.safe_execute(query, where_params, table_name)
        return self.cursor.rowcount
    
    def commit(self):
        """提交事務"""
        self.connection.commit()
    
    def rollback(self):
        """回滾事務"""
        self.connection.rollback()
    
    def close(self):
        """關閉連接"""
        self.connection.close()


def secure_db_operation(func):
    """
    安全資料庫操作裝飾器
    
    Args:
        func: 要裝飾的函數
        
    Returns:
        裝飾後的函數
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # 記錄安全事件
            SecurityAudit.log_security_event("database_operation_error", {
                "function": func.__name__,
                "error": str(e),
                "args": str(args)[:200],  # 限制長度避免敏感資訊
                "kwargs": str(kwargs)[:200]
            })
            raise
    return wrapper


# 便利函數
def create_secure_db(connection) -> SecureDatabase:
    """
    創建安全資料庫包裝器
    
    Args:
        connection: 資料庫連接
        
    Returns:
        安全資料庫包裝器實例
    """
    return SecureDatabase(connection)
