#!/usr/bin/env python3
"""
模擬API服務器 - 用於測試前端功能
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
import time
import uuid
from datetime import datetime, timedelta

class MockAPIHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """處理CORS預檢請求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_POST(self):
        """處理POST請求"""
        # 設置CORS標頭
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        # 讀取請求體
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            request_data = json.loads(post_data.decode('utf-8'))
        except:
            request_data = {}
        
        # 路由處理
        if self.path == '/api/attendance/processing/preview':
            response = self.handle_preview(request_data)
        elif self.path == '/api/attendance/processing/execute':
            response = self.handle_execute(request_data)
        else:
            response = {'success': False, 'error': '未知的API端點'}
        
        # 發送響應
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def do_GET(self):
        """處理GET請求"""
        # 設置CORS標頭
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        # 處理狀態查詢
        if self.path.startswith('/api/attendance/processing/status/'):
            processing_id = self.path.split('/')[-1]
            response = self.handle_status(processing_id)
        else:
            response = {'message': '考勤處理API模擬服務器'}
        
        # 發送響應
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def handle_preview(self, request_data):
        """處理預覽請求"""
        return {
            'success': True,
            'data': {
                'employees': [
                    {'id': 1, 'employee_id': 'E001', 'name': '黎麗玲', 'department_name': '技術部'},
                    {'id': 2, 'employee_id': 'E002', 'name': '蔡秀娟', 'department_name': '技術部'},
                    {'id': 3, 'employee_id': 'E003', 'name': '劉志偉', 'department_name': '業務部'},
                ],
                'total_employees': 3,
                'date_range': request_data.get('date_range', {}),
                'existing_records': 1,
                'estimated_new_records': 2
            }
        }
    
    def handle_execute(self, request_data):
        """處理執行請求"""
        processing_id = f"proc_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        
        return {
            'success': True,
            'message': '考勤整理處理已啟動',
            'processing_id': processing_id,
            'total_records': 3,
            'estimated_completion': (datetime.now() + timedelta(seconds=10)).isoformat()
        }
    
    def handle_status(self, processing_id):
        """處理狀態查詢"""
        # 模擬處理進度
        return {
            'success': True,
            'data': {
                'status': 'completed',
                'progress': 100,
                'total_records': 3,
                'processed_records': 3,
                'start_time': datetime.now().isoformat(),
                'estimated_completion': datetime.now().isoformat(),
                'current_step': '處理完成',
                'errors': [],
                'results': {
                    'created': 2,
                    'updated': 1,
                    'failed': 0
                }
            }
        }
    
    def log_message(self, format, *args):
        """自定義日誌格式"""
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {format % args}")

def run_mock_server():
    server_address = ('', 7073)
    httpd = HTTPServer(server_address, MockAPIHandler)
    print(f"🚀 模擬API服務器啟動在 http://localhost:7073")
    print("📋 可用端點:")
    print("  - POST /api/attendance/processing/preview")
    print("  - POST /api/attendance/processing/execute")
    print("  - GET /api/attendance/processing/status/{id}")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服務器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_mock_server()
