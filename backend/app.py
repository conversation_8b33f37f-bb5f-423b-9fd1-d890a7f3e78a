"""
Flask 應用程式主模組 - 整合版本

此模組負責：
- 設定 Flask 應用程式
- 註冊所有 API 模組藍圖
- 處理靜態頁面路由
- 系統初始化
"""

import logging
import os
import time

from flask import (
    Flask,
    make_response,
    redirect,
    render_template,
    send_from_directory,
    url_for,
    request,
)
from flask_cors import CORS

# 導入所有API模組
from api.attendance_api import attendance_bp
from api.attendance_processing_api import attendance_processing_bp
from api.auth_api import auth_bp
from api.employee_api import employee_bp
from api.leave_api import leave_bp
from api.overtime_api import overtime_bp
from api.report_api import report_bp
from api.shift_api import shift_bp
from api.system_api import system_bp
from api.work_report_api import work_report_bp
from config import Config
from database import init_db
from middleware.csrf_protection import csrf

# 配置日誌
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.FileHandler(Config.LOG_FILE), logging.StreamHandler()],
)

# 創建logger實例
logger = logging.getLogger(__name__)


def create_app():
    """
    創建並配置Flask應用程式

    返回：
    Flask: 配置完成的Flask應用程式實例
    """
    app = Flask(__name__)
    # 實用的CORS配置 - 平衡安全性和可用性
    if Config.DEBUG or os.environ.get('FLASK_ENV') == 'development':
        # 開發模式：較寬鬆的設定，包含所有常見的本地開發地址
        allowed_origins = [
            "http://localhost:7075",
            "http://127.0.0.1:7075",
            "http://0.0.0.0:7075",
            "https://localhost:7075",
            "http://localhost:3000",  # Next.js 預設端口
            "http://127.0.0.1:3000",
        ]

        CORS(
            app,
            origins=allowed_origins,
            supports_credentials=True,
            allow_headers=["Content-Type", "Authorization", "X-Requested-With", "X-CSRF-Token", "Cache-Control", "Pragma", "Expires"],
            methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            expose_headers=["X-CSRF-Token"]
        )
    else:
        # 生產模式：限制來源但包含常見的部署情況
        allowed_origins = [
            "http://localhost:7075",
            "http://127.0.0.1:7075",
            "http://0.0.0.0:7075",
            "https://localhost:7075",  # HTTPS
        ]

        # 從環境變數讀取額外的允許來源
        extra_origins = os.environ.get("ALLOWED_ORIGINS", "").split(",")
        for origin in extra_origins:
            if origin.strip():
                allowed_origins.append(origin.strip())

        CORS(
            app,
            origins=allowed_origins,
            supports_credentials=True,
            allow_headers=["Content-Type", "Authorization", "X-Requested-With", "X-CSRF-Token", "Cache-Control", "Pragma", "Expires"],
            methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            expose_headers=["X-CSRF-Token"]
        )

    # 配置應用程式
    app.config["SECRET_KEY"] = Config.SECRET_KEY
    app.config["MAX_CONTENT_LENGTH"] = Config.MAX_CONTENT_LENGTH
    app.config["UPLOAD_FOLDER"] = Config.UPLOAD_FOLDER

    # 確保上傳目錄存在
    os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)

    # 初始化 CSRF 防護（開發模式下禁用）
    if not Config.DEBUG and os.environ.get('FLASK_ENV') != 'development':
        csrf.init_app(app)
        logger.info("CSRF 防護已啟用（生產模式）")
    else:
        logger.info("CSRF 防護已禁用（開發模式）")

    # 添加實用的安全標頭和快取控制
    @app.after_request
    def add_security_headers(response):
        """添加實用的安全標頭 - 不影響功能，並解決快取問題"""

        # 🔧 解決瀏覽器快取問題 - 強制不快取 API 回應
        try:
            if hasattr(request, 'path') and request.path.startswith('/api/'):
                response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
        except:
            # 如果無法訪問 request，就對所有回應都添加防快取標頭
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'

        # 只在生產模式添加嚴格的安全標頭
        if not Config.DEBUG and os.environ.get('FLASK_ENV') != 'development':
            # 防止點擊劫持（但允許同源）
            response.headers['X-Frame-Options'] = 'SAMEORIGIN'

            # 防止 MIME 類型嗅探
            response.headers['X-Content-Type-Options'] = 'nosniff'

            # XSS 防護
            response.headers['X-XSS-Protection'] = '1; mode=block'

            # 寬鬆的內容安全政策
            response.headers['Content-Security-Policy'] = (
                "default-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "img-src 'self' data: blob: *; "
                "font-src 'self' *; "
                "connect-src 'self' *; "
            )

        # 開發模式下的基本標頭
        response.headers['X-Content-Type-Options'] = 'nosniff'

        return response

    # 註冊所有API藍圖
    register_blueprints(app)

    # 註冊頁面路由
    register_page_routes(app)

    # 初始化資料庫
    init_db()

    logger.info("Flask應用程式初始化完成")
    logger.info(
        f"已註冊的API模組: attendance, employee, shift, leave, report, system, auth, overtime, work_report"
    )

    return app


def register_blueprints(app):
    """
    註冊所有API藍圖

    參數：
    app (Flask): Flask應用程式實例
    """
    # 註冊考勤相關API
    app.register_blueprint(attendance_bp)
    logger.info("已註冊考勤API模組 (attendance_api)")

    # 註冊考勤處理API
    app.register_blueprint(attendance_processing_bp)
    logger.info("已註冊考勤處理API模組 (attendance_processing_api)")

    # 註冊員工管理API
    app.register_blueprint(employee_bp)
    logger.info("已註冊員工管理API模組 (employee_api)")

    # 註冊班表管理API
    app.register_blueprint(shift_bp)
    logger.info("已註冊班表管理API模組 (shift_api)")

    # 註冊請假管理API
    app.register_blueprint(leave_bp)
    logger.info("已註冊請假管理API模組 (leave_api)")

    # 註冊報表分析API
    app.register_blueprint(report_bp)
    logger.info("已註冊報表分析API模組 (report_api)")

    # 註冊系統功能API
    app.register_blueprint(system_bp)
    logger.info("已註冊系統功能API模組 (system_api)")

    # 註冊認證權限API
    app.register_blueprint(auth_bp)
    logger.info("已註冊認證權限API模組 (auth_api)")

    # 註冊考勤編輯API
    from api.attendance_edit_api import attendance_edit_bp

    app.register_blueprint(attendance_edit_bp)
    logger.info("已註冊考勤編輯API模組 (attendance_edit_api)")

    # 註冊加班申請API
    app.register_blueprint(overtime_bp)
    logger.info("已註冊加班申請API模組 (overtime_api)")

    app.register_blueprint(work_report_bp)
    logger.info("已註冊工作回報API模組 (work_report_api)")

    # 註冊個人資料API
    from api.profile_api import profile_bp

    app.register_blueprint(profile_bp)
    logger.info("已註冊個人資料API模組 (profile_api)")

    # 註冊匯入API
    from api.import_api import import_bp

    app.register_blueprint(import_bp)
    logger.info("已註冊匯入API模組 (import_api)")

    # 註冊進階考勤規則API（暫時禁用）
    # try:
    #     from api.attendance_rules_api import attendance_rules_bp
    #     app.register_blueprint(attendance_rules_bp)
    #     logger.info("已註冊考勤規則管理API模組 (attendance_rules_api)")
    # except ImportError:
    #     logger.warning("考勤規則管理API模組不可用，跳過註冊")


def register_page_routes(app):
    """
    註冊頁面路由

    參數：
    app (Flask): Flask應用程式實例
    """

    @app.context_processor
    def inject_timestamp():
        return {"timestamp": int(time.time())}

    @app.route("/")
    def index():
        """主頁面 - 重定向到Elite版"""
        return redirect(url_for("elite_dashboard"))

    @app.route("/modern")
    def modern_index():
        """現代化主頁面 - 全新設計的智慧考勤系統"""
        return render_template("modern-index.html")

    @app.route("/professional")
    def professional_dashboard():
        """專業版儀表板 - 參考醫療儀表板設計風格"""
        return render_template("professional-dashboard.html")

    @app.route("/elite")
    def elite_dashboard():
        """Elite版儀表板 - 國際級企業設計標準"""
        return render_template("elite-dashboard.html")

    @app.route("/elite/attendance")
    def elite_attendance():
        """Elite版考勤打卡頁面"""
        return render_template("elite-attendance.html")

    @app.route("/elite/online-clock")
    def elite_online_clock():
        """Elite版線上打卡頁面"""
        return render_template("elite-online-clock.html")

    @app.route("/elite/schedule")
    def elite_schedule():
        """Elite版排班系統頁面"""
        return render_template("elite-schedule.html")

    @app.route("/elite/leaves")
    def elite_leaves():
        """Elite版請假管理頁面"""
        return render_template("elite-leaves.html")

    @app.route("/elite/overtime")
    def elite_overtime():
        """Elite版加班管理頁面"""
        return render_template("elite-overtime.html")

    @app.route("/elite/employees")
    def elite_employees():
        """Elite版員工管理頁面"""
        return render_template("elite-employees.html")

    @app.route("/elite/analytics")
    def elite_analytics():
        """Elite版數據分析頁面"""
        return render_template("elite-analytics.html")

    @app.route("/elite/settings")
    def elite_settings():
        """Elite版系統設定頁面"""
        return render_template("elite-settings.html")

    @app.route("/elite/approval")
    def elite_approval():
        """Elite版審核作業頁面"""
        return render_template("elite-approval.html")

    @app.route("/elite/masterdata")
    def elite_masterdata():
        """Elite版基本資料管理頁面"""
        return render_template("elite-masterdata.html")

    @app.route("/mobile")
    def mobile_dashboard():
        """移動端優化版儀表板 - 原生App體驗"""
        return render_template("mobile-dashboard.html")

    @app.route("/leaves")
    def leaves():
        """請假管理頁面"""
        return render_template("leaves.html")

    @app.route("/monitor")
    def system_monitor():
        """系統監控頁面"""
        return render_template("system-monitor.html")

    @app.route("/elite/shifts")
    def elite_shifts():
        """Elite 版本班別管理頁面"""
        return render_template("elite-shifts.html")

    @app.route("/elite/features")
    def elite_features():
        """Elite 版本功能總覽頁面"""
        return render_template("elite-features.html")

    @app.route("/elite/punch-records")
    def elite_punch_records():
        """打卡原始記錄查詢頁面"""
        return render_template("elite-punch-records.html")

    @app.route("/elite/attendance-management")
    def elite_attendance_management():
        """考勤作業管理頁面"""
        return render_template("elite-attendance-management.html")

    @app.route("/elite/attendance-processing")
    def elite_attendance_processing():
        """考勤整理頁面"""
        return render_template("elite-attendance-processing.html")

    @app.route("/elite/import-attendance")
    def elite_import_attendance():
        """匯入文字檔頁面"""
        return render_template("elite-import-attendance.html")

    @app.route("/m")
    def mobile_user_dashboard():
        """
        行動版員工自助服務系統

        檢查用戶登錄狀態：
        - 如果已登錄，顯示員工自助服務頁面
        - 如果未登錄，顯示登錄頁面
        """
        from flask import session

        # 檢查用戶是否已登錄
        if not session.get("logged_in") or not session.get("employee_id"):
            # 未登錄，顯示登錄頁面
            return render_template("user-login.html")

        # 已登錄，顯示員工自助服務頁面
        return render_template("user-dashboard.html")

    @app.route("/user")
    def user_dashboard():
        """舊的用戶路由 - 重定向到新的行動版路由"""
        return redirect(url_for("mobile_user_dashboard"))

    @app.route("/design-system-showcase")
    def design_system_showcase():
        """UI設計系統展示頁面"""
        return render_template("design-system-showcase.html")

    # 🖼️ 添加uploads目錄的靜態文件服務
    @app.route("/uploads/<path:filename>")
    def uploaded_file(filename):
        """提供上傳文件的靜態服務"""
        response = make_response(
            send_from_directory(app.config["UPLOAD_FOLDER"], filename)
        )
        # 添加CORS標頭，允許跨域訪問靜態檔案
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type"
        return response

    logger.info("已註冊所有頁面路由")


# 創建應用程式實例
app = create_app()

if __name__ == "__main__":
    """
    應用程式入口點
    """
    logger.info("啟動考勤管理系統...")
    logger.info("=" * 50)
    logger.info("🚀 Han AttendanceOS v2025.6.8.23 - 文檔結構優化版")
    logger.info("=" * 50)
    logger.info("📊 系統特色：")
    logger.info("   ✅ 模組化API架構")
    logger.info("   ✅ 完整的考勤管理功能")
    logger.info("   ✅ 智慧排班系統")
    logger.info("   ✅ 請假審核流程")
    logger.info("   ✅ 數據分析報表")
    logger.info("   ✅ 系統健康監控")
    logger.info("   ✅ 用戶認證權限")
    logger.info("=" * 50)

    # 在開發模式下運行
    app.run(host=Config.HOST, port=Config.PORT, debug=Config.DEBUG)
