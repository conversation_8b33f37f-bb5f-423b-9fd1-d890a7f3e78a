"""
考勤系統資料模型。

此模組負責：
- 考勤記錄的資料結構定義
- 考勤相關的業務邏輯
- 資料驗證和處理
"""

import logging
from typing import Dict, List, Optional

import sqlite3
from datetime import datetime, time

from database import create_connection
from utils.attendance_settings import attendance_settings

logger = logging.getLogger(__name__)


class AttendanceRecord:
    """考勤記錄資料模型"""

    def __init__(
        self,
        employee_id: int,
        check_in: Optional[datetime] = None,
        check_out: Optional[datetime] = None,
        status: str = "normal",
        device_id: Optional[str] = None,
        note: Optional[str] = None,
    ):
        self.employee_id = employee_id
        self.check_in = check_in
        self.check_out = check_out
        self.status = status  # normal, late, early_leave, absent, manual
        self.device_id = device_id
        self.note = note

    def to_dict(self) -> Dict:
        """轉換為字典格式"""
        return {
            "employee_id": self.employee_id,
            "check_in": self.check_in.isoformat() if self.check_in else None,
            "check_out": self.check_out.isoformat() if self.check_out else None,
            "status": self.status,
            "device_id": self.device_id,
            "note": self.note,
        }


class AttendanceManager:
    """考勤管理器"""

    @staticmethod
    def log_attendance(
        employee_id: int,
        timestamp: datetime,
        device_type: str = "manual",
        note: str = "",
    ) -> bool:
        """
        記錄考勤打卡。

        Args:
            employee_id: 員工ID
            timestamp: 打卡時間
            device_type: 設備類型
            note: 備註

        Returns:
            是否成功記錄
        """
        conn = create_connection()
        try:
            cursor = conn.cursor()

            # 檢查當日是否已有簽到記錄
            today = timestamp.strftime("%Y-%m-%d")
            cursor.execute(
                """
                SELECT id, check_in, check_out FROM attendance 
                WHERE employee_id = ? AND DATE(check_in) = ?
            """,
                (employee_id, today),
            )

            existing_record = cursor.fetchone()

            if existing_record:
                # 更新簽退時間
                cursor.execute(
                    """
                    UPDATE attendance 
                    SET check_out = ?, device_id = ?, note = ?
                    WHERE id = ?
                """,
                    (timestamp, device_type, note, existing_record[0]),
                )
                logger.info(f"員工 {employee_id} 簽退記錄已更新")
            else:
                # 新增簽到記錄
                status = AttendanceManager._calculate_status(timestamp)
                cursor.execute(
                    """
                    INSERT INTO attendance (employee_id, check_in, status, device_id, note)
                    VALUES (?, ?, ?, ?, ?)
                """,
                    (employee_id, timestamp, status, device_type, note),
                )
                logger.info(f"員工 {employee_id} 簽到記錄已建立")

            conn.commit()
            return True

        except sqlite3.Error as e:
            logger.error(f"記錄考勤失敗: {e}")
            return False
        finally:
            conn.close()

    @staticmethod
    def _calculate_status(check_in_time: datetime) -> str:
        """
        計算考勤狀態。

        Args:
            check_in_time: 簽到時間

        Returns:
            考勤狀態 (normal, late, early_leave, absent)
        """
        # 使用動態設定的容許遲到時間
        late_tolerance_minutes = attendance_settings.get_late_tolerance_minutes()

        # 標準上班時間 09:00 (這裡可以進一步改為從班表獲取)
        standard_time = time(9, 0)
        check_in_time_only = check_in_time.time()

        if check_in_time_only <= standard_time:
            return "normal"
        else:
            # 計算遲到分鐘數
            standard_datetime = datetime.combine(check_in_time.date(), standard_time)
            check_in_datetime = datetime.combine(check_in_time.date(), check_in_time_only)
            late_minutes = (check_in_datetime - standard_datetime).total_seconds() / 60

            if late_minutes <= late_tolerance_minutes:
                return "normal"  # 在容許範圍內
            else:
                return "late"  # 超過容許時間算遲到

    @staticmethod
    def get_employee_attendance(
        employee_id: int,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ) -> List[Dict]:
        """
        取得員工考勤記錄。

        Args:
            employee_id: 員工ID
            start_date: 開始日期 (YYYY-MM-DD)
            end_date: 結束日期 (YYYY-MM-DD)

        Returns:
            考勤記錄列表
        """
        conn = create_connection()
        try:
            cursor = conn.cursor()

            query = """
                SELECT a.id, a.employee_id, a.check_in, a.check_out, 
                       a.status, a.device_id, a.note, a.created_at
                FROM attendance a
                WHERE a.employee_id = ?
            """
            params = [employee_id]

            if start_date:
                query += " AND DATE(a.check_in) >= ?"
                params.append(start_date)

            if end_date:
                query += " AND DATE(a.check_in) <= ?"
                params.append(end_date)

            query += " ORDER BY a.check_in DESC"

            cursor.execute(query, params)
            columns = [col[0] for col in cursor.description]
            records = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return records

        except sqlite3.Error as e:
            logger.error(f"查詢員工考勤記錄失敗: {e}")
            return []
        finally:
            conn.close()

    @staticmethod
    def calculate_working_hours(check_in: datetime, check_out: datetime) -> float:
        """
        計算工作時數。

        Args:
            check_in: 簽到時間
            check_out: 簽退時間

        Returns:
            工作時數
        """
        if not check_out or check_out <= check_in:
            return 0.0

        work_duration = check_out - check_in
        hours = work_duration.total_seconds() / 3600

        # 扣除午休時間 (12:00-13:00)
        lunch_start = check_in.replace(hour=12, minute=0, second=0, microsecond=0)
        lunch_end = check_in.replace(hour=13, minute=0, second=0, microsecond=0)

        if check_in <= lunch_start and check_out >= lunch_end:
            hours -= 1  # 扣除1小時午休

        return round(hours, 2)

    @staticmethod
    def get_monthly_summary(employee_id: int, year: int, month: int) -> Dict:
        """
        取得員工月度考勤統計。

        Args:
            employee_id: 員工ID
            year: 年份
            month: 月份

        Returns:
            月度統計資料
        """
        conn = create_connection()
        try:
            cursor = conn.cursor()

            # 查詢月度考勤記錄
            cursor.execute(
                """
                SELECT 
                    COUNT(*) as total_days,
                    SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal_days,
                    SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_days,
                    SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave_days,
                    SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days
                FROM attendance
                WHERE employee_id = ? 
                AND strftime('%Y', check_in) = ? 
                AND strftime('%m', check_in) = ?
            """,
                (employee_id, str(year), f"{month:02d}"),
            )

            result = cursor.fetchone()

            return {
                "total_days": result[0] or 0,
                "normal_days": result[1] or 0,
                "late_days": result[2] or 0,
                "early_leave_days": result[3] or 0,
                "absent_days": result[4] or 0,
                "attendance_rate": round(
                    (result[1] or 0) / max(result[0] or 1, 1) * 100, 2
                ),
            }

        except sqlite3.Error as e:
            logger.error(f"查詢月度統計失敗: {e}")
            return {}
        finally:
            conn.close()
