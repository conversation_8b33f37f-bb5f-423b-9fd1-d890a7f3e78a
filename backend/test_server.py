#!/usr/bin/env python3
"""
測試服務器啟動腳本
"""

import sys
import traceback


def main():
    try:
        print("🚀 開始啟動測試服務器...")

        # 導入應用
        print("📦 導入應用模組...")
        import app

        print("✅ 應用模組導入成功")
        print(f"📊 Flask應用實例: {app.app}")

        # 檢查藍圖註冊
        print("🔍 檢查已註冊的藍圖:")
        for blueprint_name, blueprint in app.app.blueprints.items():
            print(f"  - {blueprint_name}: {blueprint}")

        # 檢查路由
        print("🛣️  檢查已註冊的路由:")
        for rule in app.app.url_map.iter_rules():
            if "attendance/processing" in rule.rule:
                print(f"  - {rule.rule} [{', '.join(rule.methods)}]")

        print("🌟 啟動服務器...")
        app.app.run(host="0.0.0.0", port=7072, debug=True, use_reloader=False)  # 避免重載問題

    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
