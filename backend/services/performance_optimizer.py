#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
效能優化模組
提供資料庫查詢優化、快取管理、批次處理等效能提升功能
"""

import logging
import time
import sqlite3
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from functools import wraps, lru_cache
from threading import Lock
import json
import hashlib

# 添加父目錄到路徑以便導入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database import create_connection
except ImportError:
    # 如果無法導入，創建簡單的替代
    def create_connection():
        return sqlite3.connect('../attendance.db')

logger = logging.getLogger(__name__)

class QueryCache:
    """查詢快取管理器"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 300):
        """
        初始化快取
        
        Args:
            max_size: 最大快取項目數
            ttl_seconds: 快取存活時間（秒）
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.lock = Lock()
    
    def _generate_key(self, query: str, params: tuple = None) -> str:
        """生成快取鍵值"""
        content = f"{query}:{params}" if params else query
        return hashlib.md5(content.encode()).hexdigest()
    
    def get(self, query: str, params: tuple = None) -> Optional[Any]:
        """獲取快取資料"""
        key = self._generate_key(query, params)
        
        with self.lock:
            if key in self.cache:
                cache_item = self.cache[key]
                
                # 檢查是否過期
                if time.time() - cache_item['timestamp'] < self.ttl_seconds:
                    cache_item['hits'] += 1
                    return cache_item['data']
                else:
                    # 過期，刪除
                    del self.cache[key]
        
        return None
    
    def set(self, query: str, data: Any, params: tuple = None):
        """設定快取資料"""
        key = self._generate_key(query, params)
        
        with self.lock:
            # 如果快取已滿，刪除最舊的項目
            if len(self.cache) >= self.max_size:
                oldest_key = min(self.cache.keys(), 
                               key=lambda k: self.cache[k]['timestamp'])
                del self.cache[oldest_key]
            
            self.cache[key] = {
                'data': data,
                'timestamp': time.time(),
                'hits': 0
            }
    
    def clear(self):
        """清空快取"""
        with self.lock:
            self.cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取快取統計"""
        with self.lock:
            total_hits = sum(item['hits'] for item in self.cache.values())
            return {
                'cache_size': len(self.cache),
                'max_size': self.max_size,
                'total_hits': total_hits,
                'ttl_seconds': self.ttl_seconds
            }

class DatabaseOptimizer:
    """資料庫優化器"""
    
    def __init__(self):
        """初始化優化器"""
        self.query_cache = QueryCache()
        self.slow_query_threshold = 1.0  # 慢查詢閾值（秒）
        self.slow_queries: List[Dict[str, Any]] = []
    
    def cached_query(self, query: str, params: tuple = None, 
                    use_cache: bool = True) -> List[Dict[str, Any]]:
        """
        執行快取查詢
        
        Args:
            query: SQL 查詢
            params: 查詢參數
            use_cache: 是否使用快取
            
        Returns:
            查詢結果
        """
        # 檢查快取
        if use_cache:
            cached_result = self.query_cache.get(query, params)
            if cached_result is not None:
                return cached_result
        
        # 執行查詢
        start_time = time.time()
        
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            # 轉換為字典列表
            columns = [description[0] for description in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            conn.close()
            
            # 記錄執行時間
            execution_time = time.time() - start_time
            
            # 記錄慢查詢
            if execution_time > self.slow_query_threshold:
                self.slow_queries.append({
                    'query': query[:200] + '...' if len(query) > 200 else query,
                    'params': str(params)[:100] if params else None,
                    'execution_time': execution_time,
                    'timestamp': datetime.now().isoformat()
                })
                logger.warning(f"慢查詢檢測: {execution_time:.2f}s - {query[:100]}...")
            
            # 儲存到快取
            if use_cache and execution_time < 5.0:  # 只快取執行時間合理的查詢
                self.query_cache.set(query, results, params)
            
            return results
            
        except Exception as e:
            logger.error(f"查詢執行失敗: {e}")
            return []
    
    def batch_insert(self, table: str, data_list: List[Dict[str, Any]], 
                    batch_size: int = 100) -> int:
        """
        批次插入資料
        
        Args:
            table: 表名
            data_list: 資料列表
            batch_size: 批次大小
            
        Returns:
            插入的記錄數
        """
        if not data_list:
            return 0
        
        total_inserted = 0
        
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 獲取欄位名稱
            columns = list(data_list[0].keys())
            placeholders = ', '.join(['?' for _ in columns])
            column_names = ', '.join(columns)
            
            insert_query = f"INSERT INTO {table} ({column_names}) VALUES ({placeholders})"
            
            # 分批處理
            for i in range(0, len(data_list), batch_size):
                batch = data_list[i:i + batch_size]
                batch_values = [tuple(item[col] for col in columns) for item in batch]
                
                cursor.executemany(insert_query, batch_values)
                total_inserted += len(batch)
                
                logger.debug(f"批次插入 {len(batch)} 筆資料到 {table}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"批次插入完成: {total_inserted} 筆資料到 {table}")
            return total_inserted
            
        except Exception as e:
            logger.error(f"批次插入失敗: {e}")
            return 0
    
    def optimize_database(self):
        """優化資料庫"""
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 分析資料庫
            cursor.execute("ANALYZE")
            
            # 重建索引
            cursor.execute("REINDEX")
            
            # 清理空間
            cursor.execute("VACUUM")
            
            conn.commit()
            conn.close()
            
            logger.info("資料庫優化完成")
            
        except Exception as e:
            logger.error(f"資料庫優化失敗: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """獲取效能統計"""
        cache_stats = self.query_cache.get_stats()
        
        return {
            'cache_stats': cache_stats,
            'slow_queries_count': len(self.slow_queries),
            'recent_slow_queries': self.slow_queries[-10:],  # 最近10個慢查詢
            'slow_query_threshold': self.slow_query_threshold
        }

class PerformanceMonitor:
    """效能監控器"""
    
    def __init__(self):
        """初始化監控器"""
        self.metrics: Dict[str, List[float]] = {}
        self.lock = Lock()
    
    def record_metric(self, name: str, value: float):
        """記錄效能指標"""
        with self.lock:
            if name not in self.metrics:
                self.metrics[name] = []
            
            self.metrics[name].append(value)
            
            # 保持最近1000個記錄
            if len(self.metrics[name]) > 1000:
                self.metrics[name] = self.metrics[name][-1000:]
    
    def get_metric_stats(self, name: str) -> Dict[str, float]:
        """獲取指標統計"""
        with self.lock:
            if name not in self.metrics or not self.metrics[name]:
                return {}
            
            values = self.metrics[name]
            return {
                'count': len(values),
                'avg': sum(values) / len(values),
                'min': min(values),
                'max': max(values),
                'recent': values[-10:]  # 最近10個值
            }
    
    def get_all_stats(self) -> Dict[str, Dict[str, float]]:
        """獲取所有指標統計"""
        return {name: self.get_metric_stats(name) for name in self.metrics.keys()}

def performance_monitor(metric_name: str = None):
    """效能監控裝飾器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 記錄效能指標
                name = metric_name or f"{func.__module__}.{func.__name__}"
                monitor.record_metric(name, execution_time)
                
                # 記錄慢函數
                if execution_time > 2.0:
                    logger.warning(f"慢函數檢測: {name} - {execution_time:.2f}s")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"函數執行失敗: {func.__name__} - {execution_time:.2f}s - {e}")
                raise
        
        return wrapper
    return decorator

class BatchProcessor:
    """批次處理器"""
    
    def __init__(self, batch_size: int = 100):
        """初始化批次處理器"""
        self.batch_size = batch_size
    
    @performance_monitor("batch_attendance_calculation")
    def process_attendance_batch(self, employee_ids: List[int], 
                                start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        批次處理考勤計算
        
        Args:
            employee_ids: 員工ID列表
            start_date: 開始日期
            end_date: 結束日期
            
        Returns:
            處理結果
        """
        results = {
            'processed_count': 0,
            'success_count': 0,
            'error_count': 0,
            'errors': []
        }
        
        try:
            # 分批處理員工
            for i in range(0, len(employee_ids), self.batch_size):
                batch_ids = employee_ids[i:i + self.batch_size]
                
                batch_result = self._process_employee_batch(batch_ids, start_date, end_date)
                
                results['processed_count'] += batch_result['processed']
                results['success_count'] += batch_result['success']
                results['error_count'] += batch_result['errors']
                
                if batch_result['error_details']:
                    results['errors'].extend(batch_result['error_details'])
                
                logger.info(f"批次處理進度: {i + len(batch_ids)}/{len(employee_ids)}")
            
            return results
            
        except Exception as e:
            logger.error(f"批次處理失敗: {e}")
            results['errors'].append(str(e))
            return results
    
    def _process_employee_batch(self, employee_ids: List[int], 
                               start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """處理員工批次"""
        result = {
            'processed': 0,
            'success': 0,
            'errors': 0,
            'error_details': []
        }
        
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 批次查詢員工考勤資料
            placeholders = ','.join(['?' for _ in employee_ids])
            query = f"""
                SELECT employee_id, work_date, check_in, check_out
                FROM attendance 
                WHERE employee_id IN ({placeholders})
                AND work_date BETWEEN ? AND ?
                ORDER BY employee_id, work_date
            """
            
            params = employee_ids + [start_date.date(), end_date.date()]
            cursor.execute(query, params)
            
            attendance_records = cursor.fetchall()
            
            # 處理每筆記錄
            for record in attendance_records:
                try:
                    # 這裡可以添加具體的考勤計算邏輯
                    result['success'] += 1
                except Exception as e:
                    result['errors'] += 1
                    result['error_details'].append(f"員工 {record[0]}: {str(e)}")
                
                result['processed'] += 1
            
            conn.close()
            return result
            
        except Exception as e:
            logger.error(f"處理員工批次失敗: {e}")
            result['errors'] += 1
            result['error_details'].append(str(e))
            return result

# 全域實例
db_optimizer = DatabaseOptimizer()
monitor = PerformanceMonitor()
batch_processor = BatchProcessor()

if __name__ == "__main__":
    # 測試程式碼
    print("🧪 測試效能優化模組")
    print("=" * 50)
    
    # 測試快取
    cache = QueryCache(max_size=10, ttl_seconds=60)
    
    # 測試查詢
    test_query = "SELECT * FROM employees LIMIT 5"
    result = db_optimizer.cached_query(test_query)
    print(f"查詢結果數量: {len(result)}")
    
    # 測試快取統計
    stats = db_optimizer.get_performance_stats()
    print(f"快取統計: {stats['cache_stats']}")
    
    # 測試效能監控
    @performance_monitor("test_function")
    def test_function():
        time.sleep(0.1)
        return "測試完成"
    
    test_function()
    metric_stats = monitor.get_metric_stats("test_function")
    print(f"函數效能: {metric_stats}")
    
    print("\n✅ 效能優化模組測試完成")
