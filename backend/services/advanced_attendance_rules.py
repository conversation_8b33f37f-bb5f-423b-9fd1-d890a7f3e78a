#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
進階考勤規則系統
提供智慧化、彈性化的考勤規則處理
"""

import logging
import sys
import os
from datetime import datetime, time, timedelta, date
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import sqlite3

# 添加父目錄到路徑以便導入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database import create_connection
    from utils.attendance_settings import attendance_settings
except ImportError:
    # 如果無法導入，創建簡單的替代
    def create_connection():
        return sqlite3.connect('../attendance.db')

    class attendance_settings:
        @staticmethod
        def get_late_tolerance_minutes():
            return 5

logger = logging.getLogger(__name__)

class AttendanceRuleType(Enum):
    """考勤規則類型"""
    BASIC = "basic"              # 基本規則
    FLEXIBLE = "flexible"        # 彈性規則
    SMART = "smart"             # 智慧規則
    COMPLIANCE = "compliance"    # 合規規則
    SEASONAL = "seasonal"        # 季節性規則

class WorkPattern(Enum):
    """工作模式"""
    STANDARD = "standard"        # 標準工時
    FLEXIBLE = "flexible"        # 彈性工時
    SHIFT = "shift"             # 輪班制
    REMOTE = "remote"           # 遠程工作
    HYBRID = "hybrid"           # 混合模式

@dataclass
class AttendanceRule:
    """考勤規則定義"""
    rule_id: str
    rule_type: AttendanceRuleType
    name: str
    description: str
    conditions: Dict[str, Any]
    actions: Dict[str, Any]
    priority: int = 100
    is_active: bool = True
    effective_date: Optional[date] = None
    expiry_date: Optional[date] = None

@dataclass
class AttendanceContext:
    """考勤上下文"""
    employee_id: int
    work_date: date
    department_id: int
    position: str
    work_pattern: WorkPattern
    shift_info: Dict[str, Any]
    weather_condition: Optional[str] = None
    traffic_condition: Optional[str] = None
    special_events: List[str] = None

class AdvancedAttendanceRules:
    """進階考勤規則引擎"""
    
    def __init__(self):
        """初始化規則引擎"""
        self.rules: List[AttendanceRule] = []
        self.load_rules()
    
    def load_rules(self):
        """載入考勤規則"""
        try:
            # 載入基本規則
            self._load_basic_rules()
            
            # 載入彈性規則
            self._load_flexible_rules()
            
            # 載入智慧規則
            self._load_smart_rules()
            
            # 載入合規規則
            self._load_compliance_rules()
            
            # 載入季節性規則
            self._load_seasonal_rules()
            
            # 按優先級排序
            self.rules.sort(key=lambda x: x.priority)
            
            logger.info(f"成功載入 {len(self.rules)} 條考勤規則")
            
        except Exception as e:
            logger.error(f"載入考勤規則失敗: {e}")
    
    def _load_basic_rules(self):
        """載入基本規則"""
        # 基本遲到規則
        self.rules.append(AttendanceRule(
            rule_id="basic_late",
            rule_type=AttendanceRuleType.BASIC,
            name="基本遲到規則",
            description="標準遲到時間計算",
            conditions={
                "check_in_after_shift_start": True,
                "tolerance_minutes": 5
            },
            actions={
                "mark_as_late": True,
                "calculate_late_minutes": True,
                "deduct_pay": False  # 基本規則不扣薪
            },
            priority=10
        ))
        
        # 基本早退規則
        self.rules.append(AttendanceRule(
            rule_id="basic_early_leave",
            rule_type=AttendanceRuleType.BASIC,
            name="基本早退規則",
            description="標準早退時間計算",
            conditions={
                "check_out_before_shift_end": True,
                "tolerance_minutes": 5
            },
            actions={
                "mark_as_early_leave": True,
                "calculate_early_minutes": True,
                "deduct_pay": False
            },
            priority=10
        ))
        
        # 基本加班規則
        self.rules.append(AttendanceRule(
            rule_id="basic_overtime",
            rule_type=AttendanceRuleType.BASIC,
            name="基本加班規則",
            description="標準加班時間計算",
            conditions={
                "work_hours_exceed_standard": True,
                "minimum_overtime_minutes": 30
            },
            actions={
                "calculate_overtime": True,
                "overtime_rate": 1.5,
                "require_approval": False
            },
            priority=10
        ))
    
    def _load_flexible_rules(self):
        """載入彈性規則"""
        # 彈性工時規則
        self.rules.append(AttendanceRule(
            rule_id="flexible_hours",
            rule_type=AttendanceRuleType.FLEXIBLE,
            name="彈性工時規則",
            description="允許在核心時間外彈性上下班",
            conditions={
                "work_pattern": "flexible",
                "core_hours": {"start": "10:00", "end": "15:00"},
                "flexible_range": {"start": "07:00", "end": "19:00"}
            },
            actions={
                "allow_flexible_start": True,
                "require_core_hours_presence": True,
                "calculate_total_hours": True
            },
            priority=20
        ))
        
        # 遠程工作規則
        self.rules.append(AttendanceRule(
            rule_id="remote_work",
            rule_type=AttendanceRuleType.FLEXIBLE,
            name="遠程工作規則",
            description="遠程工作考勤規則",
            conditions={
                "work_pattern": "remote",
                "require_online_checkin": True
            },
            actions={
                "skip_location_check": True,
                "require_activity_log": True,
                "flexible_break_time": True
            },
            priority=25
        ))
    
    def _load_smart_rules(self):
        """載入智慧規則"""
        # 天氣智慧規則
        self.rules.append(AttendanceRule(
            rule_id="weather_smart",
            rule_type=AttendanceRuleType.SMART,
            name="天氣智慧規則",
            description="根據天氣狀況調整考勤標準",
            conditions={
                "weather_conditions": ["heavy_rain", "typhoon", "snow"],
                "extend_tolerance": True
            },
            actions={
                "increase_late_tolerance": 15,  # 增加15分鐘容忍度
                "allow_remote_work": True,
                "notify_manager": True
            },
            priority=30
        ))
        
        # 交通智慧規則
        self.rules.append(AttendanceRule(
            rule_id="traffic_smart",
            rule_type=AttendanceRuleType.SMART,
            name="交通智慧規則",
            description="根據交通狀況調整考勤標準",
            conditions={
                "traffic_conditions": ["heavy_traffic", "accident", "construction"],
                "commute_distance": "> 10km"
            },
            actions={
                "increase_late_tolerance": 10,
                "suggest_early_departure": True,
                "track_pattern": True
            },
            priority=30
        ))
    
    def _load_compliance_rules(self):
        """載入合規規則"""
        # 勞基法合規規則
        self.rules.append(AttendanceRule(
            rule_id="labor_law_compliance",
            rule_type=AttendanceRuleType.COMPLIANCE,
            name="勞基法合規規則",
            description="確保符合勞動基準法規定",
            conditions={
                "max_daily_hours": 12,
                "max_weekly_hours": 46,
                "min_rest_hours": 11
            },
            actions={
                "block_excessive_overtime": True,
                "enforce_rest_period": True,
                "generate_compliance_report": True,
                "alert_hr": True
            },
            priority=5  # 最高優先級
        ))
        
        # 未成年員工規則
        self.rules.append(AttendanceRule(
            rule_id="minor_employee",
            rule_type=AttendanceRuleType.COMPLIANCE,
            name="未成年員工規則",
            description="未成年員工特殊考勤規則",
            conditions={
                "employee_age": "< 18",
                "restricted_hours": {"start": "06:00", "end": "22:00"}
            },
            actions={
                "block_night_shift": True,
                "limit_daily_hours": 8,
                "require_guardian_approval": True
            },
            priority=5
        ))
    
    def _load_seasonal_rules(self):
        """載入季節性規則"""
        # 夏季彈性規則
        self.rules.append(AttendanceRule(
            rule_id="summer_flexible",
            rule_type=AttendanceRuleType.SEASONAL,
            name="夏季彈性規則",
            description="夏季高溫期間的彈性考勤",
            conditions={
                "season": "summer",
                "temperature": "> 35°C",
                "months": [6, 7, 8]
            },
            actions={
                "allow_early_start": True,
                "extend_lunch_break": 30,
                "increase_late_tolerance": 10
            },
            priority=40,
            effective_date=date(2024, 6, 1),
            expiry_date=date(2024, 8, 31)
        ))
    
    def apply_rules(self, context: AttendanceContext, 
                   check_in: Optional[datetime], 
                   check_out: Optional[datetime]) -> Dict[str, Any]:
        """
        應用考勤規則
        
        Args:
            context: 考勤上下文
            check_in: 上班時間
            check_out: 下班時間
            
        Returns:
            處理結果
        """
        result = {
            "status": "normal",
            "late_minutes": 0,
            "early_leave_minutes": 0,
            "overtime_minutes": 0,
            "work_hours": 0,
            "applied_rules": [],
            "warnings": [],
            "adjustments": []
        }
        
        try:
            # 按優先級應用規則
            for rule in self.rules:
                if self._should_apply_rule(rule, context):
                    rule_result = self._apply_single_rule(rule, context, check_in, check_out, result)
                    if rule_result:
                        result.update(rule_result)
                        result["applied_rules"].append(rule.rule_id)
            
            # 最終狀態判斷
            result["status"] = self._determine_final_status(result)
            
            return result
            
        except Exception as e:
            logger.error(f"應用考勤規則失敗: {e}")
            return result
    
    def _should_apply_rule(self, rule: AttendanceRule, context: AttendanceContext) -> bool:
        """判斷是否應該應用規則"""
        # 檢查規則是否啟用
        if not rule.is_active:
            return False
        
        # 檢查有效期
        if rule.effective_date and context.work_date < rule.effective_date:
            return False
        
        if rule.expiry_date and context.work_date > rule.expiry_date:
            return False
        
        # 檢查條件
        conditions = rule.conditions
        
        # 檢查工作模式
        if "work_pattern" in conditions:
            if conditions["work_pattern"] != context.work_pattern.value:
                return False
        
        # 檢查季節性條件
        if "months" in conditions:
            if context.work_date.month not in conditions["months"]:
                return False
        
        # 檢查天氣條件
        if "weather_conditions" in conditions and context.weather_condition:
            if context.weather_condition not in conditions["weather_conditions"]:
                return False
        
        return True
    
    def _apply_single_rule(self, rule: AttendanceRule, context: AttendanceContext,
                          check_in: Optional[datetime], check_out: Optional[datetime],
                          current_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """應用單一規則"""
        try:
            actions = rule.actions
            adjustments = {}
            
            # 處理遲到容忍度調整
            if "increase_late_tolerance" in actions:
                base_tolerance = attendance_settings.get_late_tolerance_minutes()
                new_tolerance = base_tolerance + actions["increase_late_tolerance"]
                adjustments["late_tolerance_minutes"] = new_tolerance
                current_result["adjustments"].append(f"遲到容忍度調整為 {new_tolerance} 分鐘")
            
            # 處理彈性工時
            if "allow_flexible_start" in actions and actions["allow_flexible_start"]:
                adjustments["flexible_start_enabled"] = True
                current_result["adjustments"].append("啟用彈性上班時間")
            
            # 處理加班費率
            if "overtime_rate" in actions:
                adjustments["overtime_rate"] = actions["overtime_rate"]
            
            # 處理合規檢查
            if "block_excessive_overtime" in actions:
                max_hours = rule.conditions.get("max_daily_hours", 12)
                if current_result["work_hours"] > max_hours:
                    current_result["warnings"].append(f"工作時數超過法定上限 {max_hours} 小時")
            
            return adjustments
            
        except Exception as e:
            logger.error(f"應用規則 {rule.rule_id} 失敗: {e}")
            return None
    
    def _determine_final_status(self, result: Dict[str, Any]) -> str:
        """確定最終考勤狀態"""
        if result["late_minutes"] > 0 and result["early_leave_minutes"] > 0:
            return "late_and_early_leave"
        elif result["late_minutes"] > 0:
            return "late"
        elif result["early_leave_minutes"] > 0:
            return "early_leave"
        elif result["overtime_minutes"] > 0:
            return "overtime"
        else:
            return "normal"


# 全域規則引擎實例
advanced_rules = AdvancedAttendanceRules()


if __name__ == "__main__":
    # 測試程式碼
    print("🧪 測試進階考勤規則系統")
    print("=" * 50)
    
    # 創建測試上下文
    context = AttendanceContext(
        employee_id=1,
        work_date=date.today(),
        department_id=1,
        position="軟體工程師",
        work_pattern=WorkPattern.FLEXIBLE,
        shift_info={"start_time": "09:00", "end_time": "18:00"},
        weather_condition="heavy_rain"
    )
    
    # 測試規則應用
    check_in = datetime.now().replace(hour=9, minute=15)  # 遲到15分鐘
    check_out = datetime.now().replace(hour=18, minute=30)  # 加班30分鐘
    
    result = advanced_rules.apply_rules(context, check_in, check_out)
    
    print(f"應用的規則: {result['applied_rules']}")
    print(f"最終狀態: {result['status']}")
    print(f"調整項目: {result['adjustments']}")
    print(f"警告訊息: {result['warnings']}")
    
    print("\n✅ 進階考勤規則系統測試完成")
