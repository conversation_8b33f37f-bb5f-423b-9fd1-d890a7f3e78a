#!/usr/bin/env python3
"""
簡單的服務器啟動腳本
"""

import logging

from flask import Flask
from flask_cors import CORS

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 創建Flask應用
app = Flask(__name__)
CORS(app, origins=["*"], supports_credentials=True)

# 註冊API藍圖
from api.attendance_processing_api import attendance_processing_bp

app.register_blueprint(attendance_processing_bp)


# 簡單的健康檢查端點
@app.route("/health")
def health_check():
    return {"status": "ok", "message": "服務器運行正常"}


@app.route("/")
def index():
    return {
        "message": "考勤處理API服務器",
        "endpoints": [
            "/api/attendance/processing/preview",
            "/api/attendance/processing/execute",
        ],
    }


if __name__ == "__main__":
    logger.info("🚀 啟動考勤處理API服務器...")
    app.run(host="0.0.0.0", port=7073, debug=True, use_reloader=False)
