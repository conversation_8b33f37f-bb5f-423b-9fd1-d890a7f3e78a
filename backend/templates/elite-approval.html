<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>請假審核管理 - AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            500: '#22c55e',
                            600: '#16a34a',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            500: '#f59e0b',
                            600: '#d97706',
                        },
                        error: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            500: '#ef4444',
                            600: '#dc2626',
                        }
                    }
                }
            }
        }
    </script>

    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50 font-sans">
    <!-- 返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回儀表板</span>
        </a>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen p-6 pt-20">
        <!-- 頁面標題 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">請假審核管理</h1>
            <p class="text-gray-600">管理待審核的請假申請與其他審核事項</p>
        </div>

        <!-- 統計卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- 待審核總數 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between">
                    <div class="w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="clock" class="w-6 h-6 text-warning-600"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-2xl font-bold text-gray-900" id="pendingCount">-</p>
                        <p class="text-sm text-gray-500">待審核</p>
                    </div>
                </div>
            </div>

            <!-- 今日新增 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between">
                    <div class="w-12 h-12 bg-brand-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="plus-circle" class="w-6 h-6 text-brand-600"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-2xl font-bold text-gray-900" id="todayCount">-</p>
                        <p class="text-sm text-gray-500">今日新增</p>
                    </div>
                </div>
            </div>

            <!-- 已批准 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between">
                    <div class="w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="check-circle" class="w-6 h-6 text-success-600"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-2xl font-bold text-gray-900" id="approvedCount">-</p>
                        <p class="text-sm text-gray-500">已批准</p>
                    </div>
                </div>
            </div>

            <!-- 已拒絕 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between">
                    <div class="w-12 h-12 bg-error-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="x-circle" class="w-6 h-6 text-error-600"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-2xl font-bold text-gray-900" id="rejectedCount">-</p>
                        <p class="text-sm text-gray-500">已拒絕</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 篩選工具欄 -->
        <div class="bg-white rounded-2xl p-6 shadow-lg mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <select id="typeFilter" class="border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <option value="">所有類型</option>
                    </select>

                    <select id="urgencyFilter" class="border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <option value="">所有緊急程度</option>
                        <option value="urgent">緊急</option>
                        <option value="normal">一般</option>
                    </select>
                </div>

                <div class="flex items-center space-x-3">
                    <button id="refreshBtn" class="bg-brand-500 text-white px-4 py-2 rounded-lg hover:bg-brand-600 transition-colors flex items-center space-x-2">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        <span>重新整理</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 待審核列表 -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
            <!-- 表格標題 -->
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <div class="grid grid-cols-10 gap-2 text-sm font-medium text-gray-700">
                    <div class="col-span-2">申請人</div>
                    <div class="col-span-1">類型</div>
                    <div class="col-span-2">請假期間</div>
                    <div class="col-span-1">天數</div>
                    <div class="col-span-1">代理人</div>
                    <div class="col-span-1">審核主管</div>
                    <div class="col-span-1">請假原因</div>
                    <div class="col-span-1">操作</div>
                </div>
            </div>

            <!-- 載入指示器 -->
            <div id="loadingIndicator" class="flex items-center justify-center py-12">
                <div class="flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-500"></div>
                    <span class="text-gray-600">載入中...</span>
                </div>
            </div>

            <!-- 審核列表內容 -->
            <div id="approvalList" class="divide-y divide-gray-100">
                <!-- 動態載入審核數據 -->
            </div>

            <!-- 無資料提示 -->
            <div id="noDataMessage" class="hidden text-center py-12">
                <div class="text-gray-400 mb-2">
                    <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-3"></i>
                </div>
                <p class="text-gray-600">暫無待審核的申請</p>
                <p class="text-sm text-gray-400 mt-1">所有申請都已處理完成</p>
            </div>
        </div>
    </div>

    <!-- 審核詳情模態框 -->
    <div id="approvalModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden backdrop-blur-sm">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-3xl shadow-2xl max-w-5xl w-full h-[85vh] overflow-hidden border border-gray-100">
                <!-- 模態框頭部 -->
                <div class="bg-gradient-to-r from-brand-500 to-brand-600 px-6 py-4 text-white relative overflow-hidden">
                    <div class="absolute inset-0 bg-white opacity-10">
                        <div class="absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white opacity-20"></div>
                        <div class="absolute top-8 -left-8 w-16 h-16 rounded-full bg-white opacity-15"></div>
                    </div>

                    <div class="relative flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                <i data-lucide="file-check" class="w-5 h-5"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold">審核請假申請</h3>
                                <p class="text-brand-100 text-sm">詳細審核與批准作業</p>
                            </div>
                        </div>

                        <button type="button" id="closeModalBtn" class="bg-white bg-opacity-10 hover:bg-opacity-20 text-white px-3 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 backdrop-blur-sm">
                            <i data-lucide="x" class="w-4 h-4"></i>
                            <span>關閉</span>
                        </button>
                    </div>
                </div>

                <!-- 模態框內容 - 使用網格布局 -->
                <div class="p-6 h-[calc(85vh-80px)] grid grid-cols-3 gap-6">
                    <!-- 左側：申請人資訊 -->
                    <div class="col-span-1 space-y-4">
                        <!-- 申請人基本資訊 -->
                        <div class="bg-gradient-to-br from-gray-50 to-white rounded-2xl p-4 border border-gray-100 shadow-sm h-fit">
                            <div class="flex items-center space-x-2 mb-3">
                                <div class="w-8 h-8 bg-brand-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="user" class="w-4 h-4 text-brand-600"></i>
                                </div>
                                <h4 class="text-base font-semibold text-gray-900">申請人資訊</h4>
                            </div>

                            <div class="flex items-start space-x-3 mb-4">
                                <!-- 員工照片 -->
                                <div class="flex-shrink-0">
                                    <div id="employeeAvatar" class="w-12 h-12 rounded-xl overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center shadow-md border-2 border-white">
                                        <span class="text-white text-sm font-bold" id="avatarText">?</span>
                                    </div>
                                </div>

                                <!-- 員工基本資訊 -->
                                <div class="flex-1 space-y-2">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">申請人姓名</label>
                                        <p class="text-gray-900 font-medium text-base" id="modalEmployeeName">-</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">員工編號</label>
                                        <p class="text-gray-900 text-base" id="modalEmployeeId">-</p>
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">申請人職位</label>
                                    <p class="text-gray-900 text-base" id="modalEmployeePosition">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">所屬部門</label>
                                    <p class="text-gray-900 text-base" id="modalDepartment">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">職務代理人</label>
                                    <p class="text-gray-900 text-base" id="modalSubstitute">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">審核主管</label>
                                    <p class="text-gray-900 text-base" id="modalApprover">-</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 中間：請假詳情 -->
                    <div class="col-span-1 space-y-4">
                        <!-- 請假詳情 -->
                        <div class="bg-gradient-to-br from-blue-50 to-white rounded-2xl p-4 border border-blue-100 shadow-sm h-fit">
                            <div class="flex items-center space-x-2 mb-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="calendar" class="w-4 h-4 text-blue-600"></i>
                                </div>
                                <h4 class="text-base font-semibold text-gray-900">請假詳情</h4>
                            </div>

                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">請假類型</label>
                                    <p class="text-gray-900 font-medium text-base" id="modalLeaveType">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">請假天數</label>
                                    <p class="text-gray-900 font-medium text-base" id="modalDuration">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">開始日期</label>
                                    <p class="text-gray-900 text-base" id="modalStartDate">-</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">結束日期</label>
                                    <p class="text-gray-900 text-base" id="modalEndDate">-</p>
                                </div>
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-600 mb-2">請假原因</label>
                                <div class="bg-gray-50 rounded-lg p-3 max-h-24 overflow-y-auto">
                                    <p class="text-gray-900 text-base" id="modalReason">-</p>
                                </div>
                            </div>

                            <!-- 請假文件附件 -->
                            <div class="mt-4" id="attachmentsSection" style="display: none;">
                                <label class="block text-sm font-medium text-gray-600 mb-2">請假文件</label>
                                <div class="bg-gray-50 rounded-lg p-3 max-h-32 overflow-y-auto">
                                    <div id="attachmentsList" class="space-y-2">
                                        <!-- 動態載入附件列表 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右側：審核操作 -->
                    <div class="col-span-1 space-y-4">
                        <!-- 審核操作 -->
                        <div class="bg-gradient-to-br from-green-50 to-white rounded-2xl p-4 border border-green-100 shadow-sm h-fit">
                            <div class="flex items-center space-x-2 mb-3">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="check-square" class="w-4 h-4 text-green-600"></i>
                                </div>
                                <h4 class="text-base font-semibold text-gray-900">審核意見</h4>
                            </div>

                            <!-- 審核人資訊 -->
                            <div class="bg-blue-50 rounded-lg p-3 mb-4">
                                <div class="flex items-center space-x-2">
                                    <i data-lucide="user-check" class="w-4 h-4 text-blue-600"></i>
                                    <span class="text-sm font-medium text-blue-800">審核人：</span>
                                    <span class="text-sm text-blue-700" id="currentApproverName">載入中...</span>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-2">審核備註</label>
                                    <textarea id="approvalComment" rows="6" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm resize-none text-base" placeholder="請輸入審核意見或備註..."></textarea>
                                </div>

                                <!-- 審核按鈕 -->
                                <div class="space-y-3 pt-2">
                                    <button type="button" id="approveBtn" class="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg font-medium">
                                        <i data-lucide="check" class="w-4 h-4"></i>
                                        <span class="text-center">批准申請</span>
                                    </button>
                                    <button type="button" id="rejectBtn" class="w-full px-4 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg font-medium">
                                        <i data-lucide="x" class="w-4 h-4"></i>
                                        <span class="text-center">拒絕申請</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全域變數
        let pendingLeaves = [];
        let currentLeave = null;
        let leaveTypeMap = {}; // 動態假別類型對應表

        // 初始化頁面
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 精英審核頁面開始初始化...');

            // 立即隱藏載入指示器進行測試
            console.log('🧪 測試：立即隱藏載入指示器');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const approvalList = document.getElementById('approvalList');
            const currentApproverName = document.getElementById('currentApproverName');

            if (loadingIndicator) {
                loadingIndicator.classList.add('hidden');
                console.log('✅ 載入指示器已隱藏');
            } else {
                console.error('❌ 找不到載入指示器');
            }

            if (approvalList) {
                approvalList.style.display = 'block';
                approvalList.innerHTML = '<div class="px-6 py-4 text-blue-600">測試：JavaScript正常執行，正在載入數據...</div>';
                console.log('✅ 審核列表容器已顯示');
            } else {
                console.error('❌ 找不到審核列表容器');
            }

            if (currentApproverName) {
                currentApproverName.textContent = '系統管理員';
                console.log('✅ 審核人資訊已更新');
            } else {
                console.error('❌ 找不到審核人資訊元素');
            }

            try {
                // 等待工具函數庫載入完成
                if (window.UtilsLoader) {
                    console.log('⏳ 等待工具函數庫載入...');
                    await UtilsLoader.initPageUtils('elite-approval');
                    console.log('✅ 工具函數庫載入完成');
                } else {
                    console.log('⚠️ 工具函數庫不存在，跳過載入');
                }

                console.log('🎨 初始化圖標...');
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                } else {
                    console.warn('⚠️ Lucide圖標庫未載入');
                }

                console.log('📋 設定預設假別類型對應表...');
                useDefaultLeaveTypeMap();

                console.log('🔗 綁定事件...');
                bindEvents();

                console.log('📊 載入假別類型...');
                await loadLeaveTypes();

                console.log('📈 載入審核統計...');
                await loadApprovalStats();

                console.log('📝 載入待審核列表...');
                await loadPendingLeaves();

                // 確保載入指示器被隱藏
                setTimeout(() => {
                    const loadingIndicator = document.getElementById('loadingIndicator');
                    const approvalList = document.getElementById('approvalList');

                    if (loadingIndicator && !loadingIndicator.classList.contains('hidden')) {
                        loadingIndicator.classList.add('hidden');
                        console.log('🔧 強制隱藏載入指示器');
                    }

                    if (approvalList && approvalList.style.display === 'none') {
                        approvalList.style.display = 'block';
                        console.log('🔧 強制顯示審核列表');
                    }
                }, 1000);

                console.log('✅ 精英審核頁面初始化完成');
            } catch (error) {
                console.error('❌ 精英審核頁面初始化失敗:', error);
                console.error('❌ 錯誤堆疊:', error.stack);

                // 顯示錯誤信息
                if (approvalList) {
                    approvalList.innerHTML = `<div class="px-6 py-4 text-red-500">初始化失敗: ${error.message}</div>`;
                }

                // 即使工具函數庫載入失敗，也要繼續初始化基本功能
                console.log('🔄 嘗試基本功能初始化...');
                try {
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }
                    useDefaultLeaveTypeMap();
                    bindEvents();
                    await loadLeaveTypes();
                    await loadApprovalStats();
                    await loadPendingLeaves();
                    console.log('✅ 基本功能初始化完成');
                } catch (fallbackError) {
                    console.error('❌ 基本功能初始化也失敗:', fallbackError);
                    if (approvalList) {
                        approvalList.innerHTML = `<div class="px-6 py-4 text-red-500">所有初始化嘗試都失敗: ${fallbackError.message}</div>`;
                    }
                }
            }
        });

        // 綁定事件
        function bindEvents() {
            // 重新整理按鈕
            document.getElementById('refreshBtn').addEventListener('click', function() {
                loadApprovalStats();
                loadPendingLeaves();
            });

            // 篩選器
            document.getElementById('typeFilter').addEventListener('change', filterLeaves);
            document.getElementById('urgencyFilter').addEventListener('change', filterLeaves);

            // 模態框關閉
            document.getElementById('closeModalBtn').addEventListener('click', hideModal);

            // 審核按鈕
            document.getElementById('approveBtn').addEventListener('click', function() {
                approveLeave('approve');
            });
            document.getElementById('rejectBtn').addEventListener('click', function() {
                approveLeave('reject');
            });

            // 點擊模態框背景關閉
            document.getElementById('approvalModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideModal();
                }
            });
        }

        // 載入審核統計
        async function loadApprovalStats() {
            try {
                const response = await fetch('/api/approval/stats');
                const stats = await response.json();

                // 修正API欄位名稱對應
                document.getElementById('pendingCount').textContent = stats.pending_count || 0;
                document.getElementById('todayCount').textContent = stats.today_pending || 0;
                document.getElementById('approvedCount').textContent = (stats.monthly_stats && stats.monthly_stats.approved) || 0;
                document.getElementById('rejectedCount').textContent = (stats.monthly_stats && stats.monthly_stats.rejected) || 0;

                console.log('審核統計載入完成:', stats);

            } catch (error) {
                console.error('載入審核統計失敗:', error);
                showNotification('載入統計資料失敗', 'error');
            }
        }

        // 載入待審核列表
        async function loadPendingLeaves() {
            console.log('🔄 開始載入待審核列表...');

            // 先顯示載入指示器
            const loadingIndicator = document.getElementById('loadingIndicator');
            const approvalList = document.getElementById('approvalList');

            if (loadingIndicator) {
                loadingIndicator.classList.remove('hidden');
            }
            if (approvalList) {
                approvalList.style.display = 'none';
            }

            try {
                // 添加時間戳避免快取
                const timestamp = new Date().getTime();
                console.log('📡 發送API請求:', `/api/approval/leaves?t=${timestamp}`);

                const response = await fetch(`/api/approval/leaves?t=${timestamp}`);
                console.log('📥 API回應狀態:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error(`API請求失敗: ${response.status}`);
                }

                const data = await response.json();
                console.log('📊 API回應資料:', data);

                // 修正API回應結構：records 而不是 leaves
                pendingLeaves = data.records || [];
                console.log('✅ 解析到的待審核列表:', pendingLeaves.length, '筆記錄');

                console.log('🎨 開始渲染列表...');

                // 直接在這裡渲染，不調用其他函數
                const container = document.getElementById('approvalList');
                const noDataMessage = document.getElementById('noDataMessage');

                if (!container) {
                    console.error('❌ approvalList 容器不存在');
                    return;
                }

                if (pendingLeaves.length === 0) {
                    console.log('📭 沒有待審核記錄');
                    container.innerHTML = '';
                    if (noDataMessage) {
                        noDataMessage.classList.remove('hidden');
                    }
                } else {
                    console.log('📝 渲染', pendingLeaves.length, '筆記錄');
                    if (noDataMessage) {
                        noDataMessage.classList.add('hidden');
                    }

                    // 簡化的直接渲染
                    const html = pendingLeaves.map((leave, index) => {
                        console.log(`渲染記錄 ${index + 1}: ${leave.employee_name}`);
                        return `
                            <div class="px-6 py-4 border-b border-gray-100 hover:bg-gray-50">
                                <div class="flex justify-between items-center">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                                                ${(leave.employee_name || '?').charAt(0)}
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-900">${leave.employee_name || '未知申請人'}</p>
                                                <p class="text-sm text-gray-500">${leave.employee_code || 'N/A'}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-1 px-4">
                                        <p class="text-sm font-medium">${leave.leave_type_name || leave.leave_type || '未知假別'}</p>
                                        <p class="text-sm text-gray-500">${leave.start_date} ~ ${leave.end_date}</p>
                                    </div>
                                    <div class="flex-1 px-4">
                                        <p class="text-sm text-gray-600">${leave.reason || '無原因說明'}</p>
                                    </div>
                                    <div class="flex gap-2">
                                        <button onclick="quickApprove(${leave.id}, 'approve')" 
                                                class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">
                                            核准
                                        </button>
                                        <button onclick="quickApprove(${leave.id}, 'reject')" 
                                                class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm">
                                            拒絕
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('');

                    container.innerHTML = html;
                    console.log('✅ HTML渲染完成');
                }

                console.log('✅ 列表渲染完成');

            } catch (error) {
                console.error('❌ 載入待審核列表失敗:', error);
                console.error('❌ 錯誤堆疊:', error.stack);

                // 顯示錯誤信息
                const container = document.getElementById('approvalList');
                if (container) {
                    container.innerHTML = `<div class="px-6 py-4 text-red-500">載入失敗: ${error.message}</div>`;
                }

                if (window.NotificationSystem) {
                    NotificationSystem.error('載入待審核列表失敗');
                }
            } finally {
                console.log('🔚 隱藏載入指示器');

                // 隱藏載入指示器
                if (loadingIndicator) {
                    loadingIndicator.classList.add('hidden');
                }
                if (approvalList) {
                    approvalList.style.display = 'block';
                }
            }
        }

        // 顯示請假列表
        function displayLeaves(leaves) {
            console.log('🎨 displayLeaves 函數開始執行，記錄數:', leaves.length);

            try {
                const container = document.getElementById('approvalList');
                const noDataMessage = document.getElementById('noDataMessage');

                console.log('📋 DOM元素檢查:', {
                    container: container ? '✅' : '❌',
                    noDataMessage: noDataMessage ? '✅' : '❌'
                });

                if (!container) {
                    console.error('❌ approvalList 容器不存在');
                    return;
                }

                if (leaves.length === 0) {
                    console.log('📭 沒有待審核記錄，顯示空狀態');
                    container.innerHTML = '';
                    if (noDataMessage) {
                        noDataMessage.classList.remove('hidden');
                    }
                    return;
                }

                console.log('📝 有', leaves.length, '筆記錄，開始渲染');
                if (noDataMessage) {
                    noDataMessage.classList.add('hidden');
                }

                // 簡化版本：先嘗試渲染一個簡單的列表
                console.log('🔄 嘗試簡化渲染...');
                const simpleHtml = leaves.map((leave, index) => {
                    console.log(`渲染記錄 ${index + 1}/${leaves.length}: ID=${leave.id}`);
                    return `
                        <div class="px-6 py-4 border-b border-gray-100">
                            <div class="flex justify-between items-center">
                                <div>
                                    <p class="font-medium">${leave.employee_name || '未知申請人'}</p>
                                    <p class="text-sm text-gray-500">${leave.leave_type_name || leave.leave_type || '未知假別'}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm">${leave.start_date} ~ ${leave.end_date}</p>
                                    <div class="flex gap-2 mt-2">
                                        <button onclick="quickApprove(${leave.id}, 'approve')" class="bg-green-500 text-white px-3 py-1 rounded text-sm">核准</button>
                                        <button onclick="quickApprove(${leave.id}, 'reject')" class="bg-red-500 text-white px-3 py-1 rounded text-sm">拒絕</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                console.log('✅ HTML生成完成，長度:', simpleHtml.length);
                container.innerHTML = simpleHtml;
                console.log('✅ DOM更新完成');

            } catch (error) {
                console.error('❌ displayLeaves 函數執行失敗:', error);
                console.error('❌ 錯誤堆疊:', error.stack);

                // 嘗試顯示錯誤信息
                const container = document.getElementById('approvalList');
                if (container) {
                    container.innerHTML = `<div class="px-6 py-4 text-red-500">渲染失敗: ${error.message}</div>`;
                }
            }
        }

        // 備用的複雜渲染函數（暫時不使用）
        function displayLeavesComplex(leaves) {
            console.log('🎨 displayLeavesComplex 函數開始執行，記錄數:', leaves.length);

            const container = document.getElementById('approvalList');
            const noDataMessage = document.getElementById('noDataMessage');

            console.log('📋 DOM元素檢查:', {
                container: container ? '✅' : '❌',
                noDataMessage: noDataMessage ? '✅' : '❌'
            });

            if (leaves.length === 0) {
                console.log('📭 沒有待審核記錄，顯示空狀態');
                container.innerHTML = '';
                noDataMessage.classList.remove('hidden');
                return;
            }

            console.log('📝 有', leaves.length, '筆記錄，開始渲染');
            noDataMessage.classList.add('hidden');

            try {
                container.innerHTML = leaves.map(leave => {
                    try {
                        const avatarHtml = generateAvatarHtml(leave);
                        const leaveTypeText = getLeaveTypeText(leave.leave_type);
                        const urgencyClass = isUrgentLeave(leave) ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800';
                        const urgencyText = isUrgentLeave(leave) ? '緊急' : '一般';
                        const duration = calculateDuration(leave);

                        // 安全地處理可能為null的值
                        const employeeName = leave.employee_name || '未知申請人';
                        const employeeCode = leave.employee_code || 'N/A';
                        const substituteName = leave.substitute_name || '未設定';
                        const approverName = leave.approver_name || '未設定';
                        const reason = leave.reason || '無原因說明';
                        const startDate = formatDate(leave.start_date);
                        const endDate = formatDate(leave.end_date);

                        // 除錯：確保使用正確的欄位
                        console.log(`記錄 ${leave.id}:`, {
                            申請人: employeeName,
                            代理人: substituteName,
                            審核主管: approverName
                        });

                        return `
                            <div class="px-6 py-4 hover:bg-gray-50 transition-colors">
                                <div class="grid grid-cols-10 gap-2 items-center">
                                    <!-- 申請人 -->
                                    <div class="col-span-2 flex items-center space-x-3">
                                        ${avatarHtml}
                                        <div>
                                            <p class="font-medium text-gray-900 text-sm">${employeeName}</p>
                                            <p class="text-xs text-gray-500">${employeeCode}</p>
                                        </div>
                                    </div>
                                    <!-- 請假類型 -->
                                    <div class="col-span-1">
                                        <div class="flex flex-col items-start">
                                            <span class="text-sm font-medium text-gray-900">${leaveTypeText}</span>
                                            <span class="${urgencyClass} text-xs px-2 py-1 rounded-full mt-1">${urgencyText}</span>
                                        </div>
                                    </div>
                                    <!-- 請假期間 -->
                                    <div class="col-span-2">
                                        <div class="text-sm text-gray-900">
                                            ${startDate} ~ ${endDate}
                                        </div>
                                    </div>
                                    <!-- 天數 -->
                                    <div class="col-span-1">
                                        <div class="text-sm font-medium text-blue-600">
                                            ${duration} 天
                                        </div>
                                    </div>
                                    <!-- 代理人 -->
                                    <div class="col-span-1">
                                        <p class="text-sm text-gray-900 truncate">${substituteName}</p>
                                    </div>
                                    <!-- 審核主管 -->
                                    <div class="col-span-1">
                                        <p class="text-sm text-gray-900 truncate">${approverName}</p>
                                    </div>
                                    <!-- 請假原因 -->
                                    <div class="col-span-1">
                                        <p class="text-sm text-gray-900 truncate" title="${reason}">${reason}</p>
                                    </div>
                                    <!-- 操作 -->
                                    <div class="col-span-1 flex gap-0.5 w-full">
                                        <button onclick="quickApprove(${leave.id}, 'approve')" class="bg-emerald-500 hover:bg-emerald-600 text-white text-xs font-semibold px-2 py-1.5 rounded-l-md transition-colors duration-150 shadow-sm hover:shadow flex items-center justify-center gap-1 flex-1 min-w-[28px]">
                                            <i data-lucide="check" class="w-3 h-3"></i>
                                            <span class="hidden sm:inline">核准</span>
                                        </button>
                                        <button onclick="quickApprove(${leave.id}, 'reject')" class="bg-red-500 hover:bg-red-600 text-white text-xs font-semibold px-2 py-1.5 rounded-r-md transition-colors duration-150 shadow-sm hover:shadow flex items-center justify-center gap-1 flex-1 min-w-[28px]">
                                            <i data-lucide="x" class="w-3 h-3"></i>
                                            <span class="hidden sm:inline">拒絕</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    } catch (error) {
                        console.error('渲染單個請假記錄失敗:', error, leave);
                        return `<div class="px-6 py-4 text-red-500">記錄 ${leave.id} 渲染失敗</div>`;
                    }
                }).join('');
            } catch (error) {
                console.error('渲染請假列表失敗:', error);
                container.innerHTML = '<div class="px-6 py-4 text-red-500">載入請假列表失敗</div>';
            }
        }

        // 生成員工頭像HTML
        function generateAvatarHtml(leave) {
            const employeeName = leave.employee_name || '未知';
            const firstChar = employeeName.charAt(0) || '?';

            if (leave.photo_url && leave.photo_url.trim()) {
                const avatarColor = getAvatarColor(employeeName);
                return `
                    <div class="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        <img src="${leave.photo_url}" 
                             alt="${employeeName}" 
                             class="w-full h-full object-cover"
                             onerror="this.style.display='none'; this.parentElement.innerHTML='<div class=\\'w-full h-full ${avatarColor} rounded-full flex items-center justify-center text-white font-medium\\'>${firstChar}</div>'">
                    </div>
                `;
            } else {
                const avatarColor = getAvatarColor(employeeName);
                return `
                    <div class="w-10 h-10 ${avatarColor} rounded-full flex items-center justify-center text-white font-medium">
                        ${firstChar}
                    </div>
                `;
            }
        }

        // 根據姓名生成頭像背景顏色
        function getAvatarColor(name) {
            const colors = [
                'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
                'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
            ];
            if (!name || name.length === 0) {
                return colors[0]; // 預設顏色
            }
            const charCode = name.charCodeAt(0);
            return colors[charCode % colors.length];
        }

        // 獲取請假類型文字
        function getLeaveTypeText(type) {
            return leaveTypeMap[type] || type;
        }

        // 判斷是否為緊急請假
        function isUrgentLeave(leave) {
            const today = new Date();
            const startDate = new Date(leave.start_date);
            const diffDays = Math.ceil((startDate - today) / (1000 * 60 * 60 * 24));
            return diffDays <= 1; // 明天或今天開始的請假視為緊急
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }

        // 格式化日期時間
        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-TW', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 計算請假天數
        function calculateDuration(leave) {
            // 優先使用 duration_days
            if (leave.duration_days && leave.duration_days !== 'undefined') {
                return leave.duration_days;
            }

            // 如果有請假時數，則根據時數計算天數（每天8小時）
            if (leave.leave_hours && leave.leave_hours > 0) {
                const days = Math.round((leave.leave_hours / 8) * 100) / 100; // 保留兩位小數
                return days;
            }

            // 如果API沒有提供天數，則自行計算
            if (leave.start_date && leave.end_date) {
                const startDate = new Date(leave.start_date);
                const endDate = new Date(leave.end_date);
                const diffTime = Math.abs(endDate - startDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                return diffDays;
            }

            return 1; // 預設為1天
        }

        // 篩選請假列表
        function filterLeaves() {
            const typeFilter = document.getElementById('typeFilter').value;
            const urgencyFilter = document.getElementById('urgencyFilter').value;

            let filteredLeaves = pendingLeaves;

            if (typeFilter) {
                filteredLeaves = filteredLeaves.filter(leave => leave.leave_type === typeFilter);
            }

            if (urgencyFilter) {
                if (urgencyFilter === 'urgent') {
                    filteredLeaves = filteredLeaves.filter(leave => isUrgentLeave(leave));
                } else if (urgencyFilter === 'normal') {
                    filteredLeaves = filteredLeaves.filter(leave => !isUrgentLeave(leave));
                }
            }

            displayLeaves(filteredLeaves);
        }

        // 查看請假詳情
        function viewLeaveDetail(leaveId) {
            const leave = pendingLeaves.find(l => l.id === leaveId);
            if (!leave) return;

            currentLeave = leave;

            // 除錯：顯示完整的請假資料
            console.log('查看請假詳情:', leave);

            // 填充模態框資料 - 申請人資訊
            document.getElementById('modalEmployeeName').textContent = leave.employee_name || '未知申請人';
            document.getElementById('modalEmployeeId').textContent = leave.employee_code || 'N/A';
            document.getElementById('modalEmployeePosition').textContent = leave.employee_position || '未設定職位';
            document.getElementById('modalDepartment').textContent = leave.department_name || '未設定部門';

            // 顯示代理人資訊（包含職位和員工編號）
            let substituteText = '無';
            if (leave.substitute_name) {
                substituteText = leave.substitute_name;
                if (leave.substitute_code) {
                    substituteText += ` (${leave.substitute_code})`;
                }
                if (leave.substitute_position) {
                    substituteText += ` - ${leave.substitute_position}`;
                }
            }
            document.getElementById('modalSubstitute').textContent = substituteText;

            // 顯示審核主管資訊（包含職位）
            let approverText = '無';
            if (leave.approver_name) {
                approverText = leave.approver_name;
                if (leave.approver_position) {
                    approverText += ` (${leave.approver_position})`;
                }
            }
            document.getElementById('modalApprover').textContent = approverText;

            // 除錯：顯示三個角色的資訊
            console.log('模態框資料填充:', {
                申請人: leave.employee_name,
                申請人職位: leave.employee_position,
                代理人: leave.substitute_name,
                代理人職位: leave.substitute_position,
                審核主管: leave.approver_name,
                審核主管職位: leave.approver_position
            });

            document.getElementById('modalLeaveType').textContent = getLeaveTypeText(leave.leave_type);
            document.getElementById('modalDuration').textContent = `${calculateDuration(leave)} 天`;
            document.getElementById('modalStartDate').textContent = formatDate(leave.start_date);
            document.getElementById('modalEndDate').textContent = formatDate(leave.end_date);
            document.getElementById('modalReason').textContent = leave.reason || '無';

            // 顯示請假文件附件
            displayAttachments(leave.attachments);

            // 設定員工頭像
            const avatarContainer = document.getElementById('employeeAvatar');
            const avatarText = document.getElementById('avatarText');

            if (leave.photo_url && leave.photo_url.trim()) {
                avatarContainer.innerHTML = `<img src="${leave.photo_url}" alt="${leave.employee_name}" class="w-full h-full object-cover">`;
            } else {
                const avatarColor = getAvatarColor(leave.employee_name || '?');
                avatarContainer.className = `w-16 h-16 rounded-2xl overflow-hidden ${avatarColor} flex items-center justify-center shadow-lg border-4 border-white`;
                avatarText.textContent = (leave.employee_name || '?').charAt(0);
            }

            // 清空審核備註
            document.getElementById('approvalComment').value = '';

            // 載入當前審核人資訊
            loadCurrentApproverInfo();

            showModal();
        }

        // 獲取當前登錄用戶資訊
        async function getCurrentUserInfo() {
            try {
                const response = await fetch('/api/auth/verify');
                if (response.ok) {
                    const data = await response.json();
                    return {
                        id: data.employee_id || 1,
                        name: data.name || '系統管理員',
                        employee_code: data.employee_code || 'ADMIN'
                    };
                }
            } catch (error) {
                console.error('獲取當前用戶失敗:', error);
            }
            return {
                id: 1,
                name: '系統管理員',
                employee_code: 'ADMIN'
            };
        }

        // 獲取當前登錄用戶ID（向後兼容）
        async function getCurrentUserId() {
            const userInfo = await getCurrentUserInfo();
            return userInfo.id;
        }

        // 快速審核請假（直接從列表操作）
        async function quickApprove(leaveId, action) {
            const leave = pendingLeaves.find(l => l.id === leaveId);
            if (!leave) {
                showSlideNotification('找不到請假申請', 'error');
                return;
            }

            const actionText = action === 'approve' ? '批准' : '拒絕';

            try {
                // 獲取當前登錄用戶ID
                const currentUserId = await getCurrentUserId();

                console.log('快速審核請假:', {
                    leave_id: leaveId,
                    action: action,
                    approver_id: currentUserId
                });

                const response = await fetch(`/api/approval/leaves/${leaveId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: action,
                        comment: `快速${actionText}`,
                        approver_id: currentUserId
                    })
                });

                const result = await response.json();
                console.log('快速審核結果:', result);

                if (response.ok) {
                    showSlideNotification(`請假申請已${actionText}`, 'success');

                    // 重新載入數據
                    loadApprovalStats();
                    loadPendingLeaves();
                } else {
                    console.error('快速審核失敗:', result);
                    showSlideNotification(result.error || result.message || '審核失敗', 'error');
                }

            } catch (error) {
                console.error('快速審核請假失敗:', error);
                showSlideNotification('審核失敗，請稍後再試', 'error');
            }
        }

        // 審核請假（詳細模態框）
        async function approveLeave(action) {
            if (!currentLeave) return;

            const comment = document.getElementById('approvalComment').value.trim();

            try {
                // 獲取當前登錄用戶ID
                const currentUserId = await getCurrentUserId();

                console.log('審核請假:', {
                    leave_id: currentLeave.id,
                    action: action,
                    comment: comment,
                    approver_id: currentUserId
                });

                const response = await fetch(`/api/approval/leaves/${currentLeave.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: action,
                        comment: comment,
                        approver_id: currentUserId
                    })
                });

                const result = await response.json();
                console.log('審核結果:', result);

                if (response.ok) {
                    const actionText = action === 'approve' ? '批准' : '拒絕';
                    showSlideNotification(`請假申請已${actionText}`, 'success');
                    hideModal();

                    // 重新載入數據
                    loadApprovalStats();
                    loadPendingLeaves();
                } else {
                    console.error('審核失敗:', result);
                    showSlideNotification(result.error || result.message || '審核失敗', 'error');
                }

            } catch (error) {
                console.error('審核請假失敗:', error);
                showSlideNotification('審核失敗，請稍後再試', 'error');
            }
        }

        // 載入當前審核人資訊
        async function loadCurrentApproverInfo() {
            try {
                const userInfo = await getCurrentUserInfo();
                const approverElement = document.getElementById('currentApproverName');
                if (approverElement) {
                    approverElement.textContent = `${userInfo.name} (${userInfo.employee_code})`;
                }
                console.log('當前審核人:', userInfo);
            } catch (error) {
                console.error('載入審核人資訊失敗:', error);
                const approverElement = document.getElementById('currentApproverName');
                if (approverElement) {
                    approverElement.textContent = '系統管理員';
                }
            }
        }

        // 顯示模態框
        function showModal() {
            document.getElementById('approvalModal').classList.remove('hidden');
            // 重新創建圖標
            lucide.createIcons();
        }

        // 隱藏模態框
        function hideModal() {
            document.getElementById('approvalModal').classList.add('hidden');
            currentLeave = null;
        }

        // 顯示載入指示器
        function showLoading() {
            document.getElementById('loadingIndicator').classList.remove('hidden');
            document.getElementById('approvalList').style.display = 'none';
        }

        // 隱藏載入指示器
        function hideLoading() {
            document.getElementById('loadingIndicator').classList.add('hidden');
            document.getElementById('approvalList').style.display = 'block';
        }

        // 向後兼容的通知函數（使用新的工具函數庫）
        function showNotification(message, type = 'info') {
            if (window.NotificationSystem) {
                switch (type) {
                    case 'success':
                        NotificationSystem.success(message);
                        break;
                    case 'error':
                        NotificationSystem.error(message);
                        break;
                    case 'warning':
                        NotificationSystem.warning(message);
                        break;
                    default:
                        NotificationSystem.info(message);
                }
            } else {
                console.warn('通知系統未載入，使用控制台輸出:', message);
            }
        }

        // 向後兼容的滑動通知函數（使用新的工具函數庫）
        function showSlideNotification(message, type = 'success') {
            if (window.NotificationSystem) {
                NotificationSystem.showSlideNotification(message, type);
            } else {
                showNotification(message, type);
            }
        }

        // 載入假別類型
        async function loadLeaveTypes() {
            try {
                const response = await fetch('/api/masterdata/leave_types');
                const data = await response.json();

                if (data.items) {
                    // 建立假別代碼到假別名稱的對應表
                    leaveTypeMap = {};
                    data.items.forEach(item => {
                        leaveTypeMap[item.code] = item.name;
                    });

                    // 更新篩選器選項
                    populateFilterOptions(data.items);
                    console.log('假別類型載入成功:', leaveTypeMap);
                } else {
                    console.warn('假別類型載入失敗，使用預設對應表');
                    useDefaultLeaveTypeMap();
                }
            } catch (error) {
                console.error('載入假別類型失敗:', error);
                useDefaultLeaveTypeMap();
            }
        }

        // 使用預設假別類型對應表（容錯處理）
        function useDefaultLeaveTypeMap() {
            leaveTypeMap = {
                'annual': '年假',
                'sick': '病假',
                'personal': '事假',
                'maternity': '產假',
                'paternity': '陪產假',
                'bereavement': '喪假',
                'marriage': '婚假',
                'official': '公假',
                'compensatory': '補休'
            };
            console.log('使用預設假別類型對應表:', leaveTypeMap);
        }

        // 顯示請假文件附件
        function displayAttachments(attachments) {
            const attachmentsSection = document.getElementById('attachmentsSection');
            const attachmentsList = document.getElementById('attachmentsList');

            // 清空現有內容
            attachmentsList.innerHTML = '';

            // 檢查是否有附件
            if (!attachments || attachments.length === 0) {
                attachmentsSection.style.display = 'none';
                return;
            }

            // 顯示附件區域
            attachmentsSection.style.display = 'block';

            // 處理附件列表
            attachments.forEach((attachment, index) => {
                const attachmentItem = document.createElement('div');
                attachmentItem.className = 'flex items-center justify-between bg-white rounded-lg p-3 border border-gray-200 hover:border-blue-300 transition-colors';

                // 獲取文件名和類型
                const fileName = attachment.split('/').pop() || `附件${index + 1}`;
                const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';

                // 判斷文件類型圖標
                let fileIcon = '📄'; // 預設文件圖標
                if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExtension)) {
                    fileIcon = '🖼️';
                } else if (fileExtension === 'pdf') {
                    fileIcon = '📕';
                } else if (['doc', 'docx'].includes(fileExtension)) {
                    fileIcon = '📝';
                }

                attachmentItem.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <span class="text-2xl">${fileIcon}</span>
                        <div>
                            <p class="text-sm font-medium text-gray-900">${fileName}</p>
                            <p class="text-xs text-gray-500">請假證明文件</p>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="viewAttachment('${attachment}')"
                                class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors">
                            查看
                        </button>
                        <button onclick="downloadAttachment('${attachment}', '${fileName}')"
                                class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors">
                            下載
                        </button>
                    </div>
                `;

                attachmentsList.appendChild(attachmentItem);
            });
        }

        // 查看附件
        function viewAttachment(attachmentUrl) {
            // 在新視窗中打開附件
            window.open(attachmentUrl, '_blank');
        }

        // 下載附件
        function downloadAttachment(attachmentUrl, fileName) {
            const link = document.createElement('a');
            link.href = attachmentUrl;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 填充篩選器選項
        function populateFilterOptions(leaveTypes) {
            const typeFilter = document.getElementById('typeFilter');
            if (!typeFilter) return;

            // 清空現有選項（保留第一個預設選項）
            const firstOption = typeFilter.firstElementChild;
            typeFilter.innerHTML = '';
            if (firstOption) {
                typeFilter.appendChild(firstOption);
            }

            // 添加動態載入的假別類型
            leaveTypes.forEach(leaveType => {
                if (leaveType.is_active) {
                    const option = document.createElement('option');
                    option.value = leaveType.code;
                    option.textContent = leaveType.name;
                    typeFilter.appendChild(option);
                }
            });
        }
    </script>
</body>

</html>