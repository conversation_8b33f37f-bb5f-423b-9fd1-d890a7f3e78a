#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考勤設定工具模組
提供動態讀取考勤規則設定的功能
"""

import sqlite3
import logging
import sys
import os
from typing import Dict, Optional, Union
from datetime import time

# 添加父目錄到路徑以便導入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database import create_connection

logger = logging.getLogger(__name__)

class AttendanceSettings:
    """考勤設定管理類"""
    
    def __init__(self):
        """初始化設定管理器"""
        self._settings_cache = {}
        self._cache_valid = False
    
    def get_setting(self, setting_key: str, default_value: Union[str, int, float] = None) -> Optional[str]:
        """
        獲取單個設定值
        
        Args:
            setting_key: 設定鍵名
            default_value: 預設值
            
        Returns:
            設定值或預設值
        """
        try:
            if not self._cache_valid:
                self._load_settings()
            
            return self._settings_cache.get(setting_key, default_value)
            
        except Exception as e:
            logger.error(f"獲取設定 {setting_key} 失敗: {e}")
            return default_value
    
    def get_all_settings(self) -> Dict[str, str]:
        """
        獲取所有考勤設定
        
        Returns:
            所有設定的字典
        """
        try:
            if not self._cache_valid:
                self._load_settings()
            
            return self._settings_cache.copy()
            
        except Exception as e:
            logger.error(f"獲取所有設定失敗: {e}")
            return {}
    
    def _load_settings(self):
        """從資料庫載入設定"""
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 查詢考勤相關設定
            cursor.execute("""
                SELECT rule_type, rule_value 
                FROM schedule_rules 
                WHERE rule_type IN (
                    'late_tolerance_minutes',
                    'early_leave_tolerance_minutes', 
                    'overtime_minimum_hours',
                    'day_change_time',
                    'cross_day_attendance',
                    'first_punch_as_checkin',
                    'last_punch_as_checkout',
                    'auto_clock_out_hours',
                    'break_time_deduction',
                    'weekend_overtime_auto'
                )
                AND is_active = 1
            """)
            
            # 清空快取並重新載入
            self._settings_cache.clear()
            
            for rule_type, rule_value in cursor.fetchall():
                self._settings_cache[rule_type] = rule_value
            
            # 設定快取為有效
            self._cache_valid = True
            
            conn.close()
            logger.info(f"成功載入 {len(self._settings_cache)} 項考勤設定")
            
        except Exception as e:
            logger.error(f"載入考勤設定失敗: {e}")
            self._cache_valid = False
    
    def refresh_cache(self):
        """刷新設定快取"""
        self._cache_valid = False
        self._load_settings()
    
    def get_late_tolerance_minutes(self) -> int:
        """獲取容許遲到時間（分鐘）"""
        try:
            value = self.get_setting('late_tolerance_minutes', '5')
            return int(value)
        except (ValueError, TypeError):
            logger.warning("容許遲到時間設定無效，使用預設值 5 分鐘")
            return 5
    
    def get_early_leave_tolerance_minutes(self) -> int:
        """獲取容許早退時間（分鐘）"""
        try:
            value = self.get_setting('early_leave_tolerance_minutes', '5')
            return int(value)
        except (ValueError, TypeError):
            logger.warning("容許早退時間設定無效，使用預設值 5 分鐘")
            return 5
    
    def get_overtime_minimum_hours(self) -> float:
        """獲取最小加班時數"""
        try:
            value = self.get_setting('overtime_minimum_hours', '0.5')
            return float(value)
        except (ValueError, TypeError):
            logger.warning("最小加班時數設定無效，使用預設值 0.5 小時")
            return 0.5
    
    def get_day_change_time(self) -> time:
        """獲取換日時間"""
        try:
            time_str = self.get_setting('day_change_time', '06:00')
            hour, minute = map(int, time_str.split(':'))
            return time(hour, minute)
        except (ValueError, TypeError):
            logger.warning("換日時間設定無效，使用預設值 06:00")
            return time(6, 0)
    
    def is_cross_day_attendance_enabled(self) -> bool:
        """是否啟用跨日考勤"""
        try:
            value = self.get_setting('cross_day_attendance', '1')
            return value == '1'
        except (ValueError, TypeError):
            return True
    
    def is_first_punch_as_checkin(self) -> bool:
        """第一次打卡是否視為上班"""
        try:
            value = self.get_setting('first_punch_as_checkin', '1')
            return value == '1'
        except (ValueError, TypeError):
            return True
    
    def is_last_punch_as_checkout(self) -> bool:
        """最後一次打卡是否視為下班"""
        try:
            value = self.get_setting('last_punch_as_checkout', '1')
            return value == '1'
        except (ValueError, TypeError):
            return True
    
    def get_auto_clock_out_hours(self) -> int:
        """獲取自動下班打卡時間（小時後）"""
        try:
            value = self.get_setting('auto_clock_out_hours', '12')
            return int(value)
        except (ValueError, TypeError):
            logger.warning("自動下班打卡時間設定無效，使用預設值 12 小時")
            return 12
    
    def is_break_time_deduction_enabled(self) -> bool:
        """是否扣除休息時間"""
        try:
            value = self.get_setting('break_time_deduction', '1')
            return value == '1'
        except (ValueError, TypeError):
            return True
    
    def is_weekend_overtime_auto_enabled(self) -> bool:
        """週末是否自動計算加班"""
        try:
            value = self.get_setting('weekend_overtime_auto', '1')
            return value == '1'
        except (ValueError, TypeError):
            return True
    
    def get_settings_summary(self) -> Dict[str, Union[str, int, float, bool]]:
        """
        獲取設定摘要
        
        Returns:
            格式化的設定摘要
        """
        return {
            'late_tolerance_minutes': self.get_late_tolerance_minutes(),
            'early_leave_tolerance_minutes': self.get_early_leave_tolerance_minutes(),
            'overtime_minimum_hours': self.get_overtime_minimum_hours(),
            'day_change_time': self.get_day_change_time().strftime('%H:%M'),
            'cross_day_attendance': self.is_cross_day_attendance_enabled(),
            'first_punch_as_checkin': self.is_first_punch_as_checkin(),
            'last_punch_as_checkout': self.is_last_punch_as_checkout(),
            'auto_clock_out_hours': self.get_auto_clock_out_hours(),
            'break_time_deduction': self.is_break_time_deduction_enabled(),
            'weekend_overtime_auto': self.is_weekend_overtime_auto_enabled()
        }


# 全域設定實例
attendance_settings = AttendanceSettings()


def get_attendance_setting(setting_key: str, default_value: Union[str, int, float] = None) -> Optional[str]:
    """
    便利函數：獲取考勤設定值
    
    Args:
        setting_key: 設定鍵名
        default_value: 預設值
        
    Returns:
        設定值
    """
    return attendance_settings.get_setting(setting_key, default_value)


def refresh_attendance_settings():
    """便利函數：刷新考勤設定快取"""
    attendance_settings.refresh_cache()


def get_attendance_settings_summary() -> Dict[str, Union[str, int, float, bool]]:
    """便利函數：獲取考勤設定摘要"""
    return attendance_settings.get_settings_summary()


if __name__ == "__main__":
    # 測試程式碼
    print("🧪 測試考勤設定模組")
    print("=" * 50)
    
    settings = AttendanceSettings()
    summary = settings.get_settings_summary()
    
    print("📋 目前考勤設定：")
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    print("\n✅ 測試完成")
