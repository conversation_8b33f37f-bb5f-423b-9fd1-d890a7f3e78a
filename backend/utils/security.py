#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全工具模組
提供密碼雜湊、驗證、輸入驗證等安全功能
"""

import bcrypt
import re
import logging
import secrets
import string
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import sqlite3

logger = logging.getLogger(__name__)

class PasswordManager:
    """密碼管理器 - 提供安全的密碼雜湊和驗證功能"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """
        使用 bcrypt 雜湊密碼
        
        Args:
            password: 明文密碼
            
        Returns:
            雜湊後的密碼字串
        """
        try:
            # 生成鹽值並雜湊密碼
            salt = bcrypt.gensalt()
            hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
            return hashed.decode('utf-8')
        except Exception as e:
            logger.error(f"密碼雜湊失敗: {e}")
            raise
    
    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        """
        驗證密碼
        
        Args:
            password: 明文密碼
            hashed_password: 雜湊後的密碼
            
        Returns:
            密碼是否正確
        """
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
        except Exception as e:
            logger.error(f"密碼驗證失敗: {e}")
            return False
    
    @staticmethod
    def generate_secure_password(length: int = 12) -> str:
        """
        生成安全的隨機密碼
        
        Args:
            length: 密碼長度
            
        Returns:
            隨機生成的密碼
        """
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(alphabet) for _ in range(length))
        return password
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """
        驗證密碼強度
        
        Args:
            password: 要驗證的密碼
            
        Returns:
            包含驗證結果的字典
        """
        result = {
            'is_valid': True,
            'score': 0,
            'errors': [],
            'suggestions': []
        }
        
        # 檢查長度
        if len(password) < 8:
            result['errors'].append('密碼長度至少需要8個字符')
            result['is_valid'] = False
        else:
            result['score'] += 1
        
        # 檢查是否包含大寫字母
        if not re.search(r'[A-Z]', password):
            result['errors'].append('密碼需要包含至少一個大寫字母')
            result['is_valid'] = False
        else:
            result['score'] += 1
        
        # 檢查是否包含小寫字母
        if not re.search(r'[a-z]', password):
            result['errors'].append('密碼需要包含至少一個小寫字母')
            result['is_valid'] = False
        else:
            result['score'] += 1
        
        # 檢查是否包含數字
        if not re.search(r'\d', password):
            result['errors'].append('密碼需要包含至少一個數字')
            result['is_valid'] = False
        else:
            result['score'] += 1
        
        # 檢查是否包含特殊字符
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            result['suggestions'].append('建議包含特殊字符以提高安全性')
        else:
            result['score'] += 1
        
        # 檢查常見弱密碼
        weak_passwords = ['password', '123456', 'admin', 'user', 'guest']
        if password.lower() in weak_passwords:
            result['errors'].append('不能使用常見的弱密碼')
            result['is_valid'] = False
        
        return result


class InputValidator:
    """輸入驗證器 - 防止SQL注入和XSS攻擊"""
    
    @staticmethod
    def sanitize_string(input_str: str, max_length: int = 255) -> str:
        """
        清理字串輸入
        
        Args:
            input_str: 輸入字串
            max_length: 最大長度
            
        Returns:
            清理後的字串
        """
        if not isinstance(input_str, str):
            return ""
        
        # 移除危險字符
        sanitized = re.sub(r'[<>"\']', '', input_str)
        
        # 限制長度
        sanitized = sanitized[:max_length]
        
        # 移除前後空白
        sanitized = sanitized.strip()
        
        return sanitized
    
    @staticmethod
    def validate_employee_id(employee_id: str) -> bool:
        """
        驗證員工編號格式
        
        Args:
            employee_id: 員工編號
            
        Returns:
            是否有效
        """
        if not employee_id:
            return False
        
        # 員工編號只能包含字母、數字和連字符
        pattern = r'^[A-Za-z0-9\-_]{1,20}$'
        return bool(re.match(pattern, employee_id))
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """
        驗證電子郵件格式
        
        Args:
            email: 電子郵件地址
            
        Returns:
            是否有效
        """
        if not email:
            return True  # 電子郵件是可選的
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """
        驗證電話號碼格式
        
        Args:
            phone: 電話號碼
            
        Returns:
            是否有效
        """
        if not phone:
            return True  # 電話是可選的
        
        # 支援台灣手機和市話格式
        patterns = [
            r'^09\d{8}$',  # 手機
            r'^0\d{1,2}-?\d{6,8}$',  # 市話
            r'^\+886-?9\d{8}$',  # 國際格式手機
        ]
        
        return any(re.match(pattern, phone.replace('-', '').replace(' ', '')) for pattern in patterns)
    
    @staticmethod
    def validate_date_format(date_str: str) -> bool:
        """
        驗證日期格式 (YYYY-MM-DD)
        
        Args:
            date_str: 日期字串
            
        Returns:
            是否有效
        """
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_time_format(time_str: str) -> bool:
        """
        驗證時間格式 (HH:MM 或 HH:MM:SS)
        
        Args:
            time_str: 時間字串
            
        Returns:
            是否有效
        """
        patterns = [
            r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$',  # HH:MM
            r'^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$',  # HH:MM:SS
        ]
        
        return any(re.match(pattern, time_str) for pattern in patterns)


class SecurityAudit:
    """安全審計工具"""
    
    @staticmethod
    def check_sql_injection_risk(query: str) -> Dict[str, Any]:
        """
        檢查SQL查詢的注入風險
        
        Args:
            query: SQL查詢字串
            
        Returns:
            風險評估結果
        """
        result = {
            'risk_level': 'low',
            'issues': [],
            'recommendations': []
        }
        
        # 檢查危險關鍵字
        dangerous_keywords = [
            'DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE',
            'EXEC', 'EXECUTE', 'UNION', 'INSERT', 'UPDATE'
        ]
        
        query_upper = query.upper()
        found_keywords = [kw for kw in dangerous_keywords if kw in query_upper]
        
        if found_keywords:
            result['risk_level'] = 'high'
            result['issues'].append(f'包含危險關鍵字: {", ".join(found_keywords)}')
        
        # 檢查是否使用參數化查詢
        if "'" in query and '?' not in query:
            result['risk_level'] = 'medium'
            result['issues'].append('可能未使用參數化查詢')
            result['recommendations'].append('使用參數化查詢防止SQL注入')
        
        return result
    
    @staticmethod
    def log_security_event(event_type: str, details: Dict[str, Any], user_id: Optional[str] = None):
        """
        記錄安全事件
        
        Args:
            event_type: 事件類型
            details: 事件詳情
            user_id: 用戶ID
        """
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'event_type': event_type,
                'user_id': user_id,
                'details': details
            }
            
            logger.warning(f"安全事件: {log_entry}")
            
            # 這裡可以添加將安全事件寫入專門的安全日誌表的邏輯
            
        except Exception as e:
            logger.error(f"記錄安全事件失敗: {e}")


# 便利函數
def hash_password(password: str) -> str:
    """便利函數：雜湊密碼"""
    return PasswordManager.hash_password(password)

def verify_password(password: str, hashed_password: str) -> bool:
    """便利函數：驗證密碼"""
    return PasswordManager.verify_password(password, hashed_password)

def validate_password_strength(password: str) -> Dict[str, Any]:
    """便利函數：驗證密碼強度"""
    return PasswordManager.validate_password_strength(password)

def sanitize_input(input_str: str, max_length: int = 255) -> str:
    """便利函數：清理輸入"""
    return InputValidator.sanitize_string(input_str, max_length)


if __name__ == "__main__":
    # 測試程式碼
    print("🧪 測試安全工具模組")
    print("=" * 50)
    
    # 測試密碼雜湊
    test_password = "TestPassword123!"
    hashed = hash_password(test_password)
    print(f"原始密碼: {test_password}")
    print(f"雜湊密碼: {hashed}")
    print(f"驗證結果: {verify_password(test_password, hashed)}")
    
    # 測試密碼強度
    strength = validate_password_strength(test_password)
    print(f"密碼強度: {strength}")
    
    print("\n✅ 安全工具模組測試完成")
