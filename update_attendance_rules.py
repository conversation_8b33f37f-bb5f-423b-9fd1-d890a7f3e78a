#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考勤規則資料庫更新腳本
更新考勤規則預設值以符合規範要求
"""

import sqlite3
import os
import sys
from datetime import datetime

def get_database_path():
    """獲取資料庫路徑"""
    # 從 backend 目錄執行時，資料庫在上一層
    if os.path.basename(os.getcwd()) == 'backend':
        return '../attendance.db'
    else:
        return 'attendance.db'

def check_current_settings():
    """檢查目前的設定值"""
    db_path = get_database_path()
    
    if not os.path.exists(db_path):
        print(f"❌ 資料庫檔案不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查 schedule_rules 表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='schedule_rules'
        """)
        
        if not cursor.fetchone():
            print("❌ schedule_rules 表不存在")
            return False
        
        # 查詢目前的考勤規則設定
        cursor.execute("""
            SELECT rule_type, rule_value, description 
            FROM schedule_rules 
            WHERE rule_type IN (
                'late_tolerance_minutes', 
                'early_leave_tolerance_minutes', 
                'overtime_minimum_hours', 
                'day_change_time'
            ) 
            ORDER BY rule_type
        """)
        
        current_settings = cursor.fetchall()
        
        print("📊 目前資料庫中的考勤規則設定：")
        print("=" * 60)
        
        if current_settings:
            for rule_type, rule_value, description in current_settings:
                print(f"  {rule_type}: {rule_value} ({description})")
        else:
            print("  ⚠️  未找到相關的考勤規則設定")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 檢查設定時發生錯誤: {e}")
        return False

def update_attendance_rules():
    """更新考勤規則為符合規範的值"""
    db_path = get_database_path()
    
    # 新的規則設定（符合規範要求）
    new_rules = {
        'late_tolerance_minutes': ('5', '遲到容忍時間（分鐘）'),
        'early_leave_tolerance_minutes': ('5', '早退容忍時間（分鐘）'),
        'overtime_minimum_hours': ('0.5', '最小加班時數（30分鐘=0.5小時）'),
        'day_change_time': ('06:00', '每日換日時間（此時間前的打卡歸屬前一日）')
    }
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🔄 開始更新考勤規則...")
        print("=" * 60)
        
        updated_count = 0
        
        for rule_type, (rule_value, description) in new_rules.items():
            # 檢查規則是否已存在
            cursor.execute("""
                SELECT rule_value FROM schedule_rules 
                WHERE rule_type = ?
            """, (rule_type,))
            
            existing = cursor.fetchone()
            
            if existing:
                old_value = existing[0]
                if old_value != rule_value:
                    # 更新現有規則
                    cursor.execute("""
                        UPDATE schedule_rules 
                        SET rule_value = ?, description = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE rule_type = ?
                    """, (rule_value, description, rule_type))
                    
                    print(f"  ✅ 更新 {rule_type}: {old_value} → {rule_value}")
                    updated_count += 1
                else:
                    print(f"  ℹ️  {rule_type}: {rule_value} (無需更新)")
            else:
                # 新增規則
                cursor.execute("""
                    INSERT INTO schedule_rules (rule_type, rule_value, description, is_active)
                    VALUES (?, ?, ?, 1)
                """, (rule_type, rule_value, description))
                
                print(f"  ➕ 新增 {rule_type}: {rule_value}")
                updated_count += 1
        
        # 提交變更
        conn.commit()
        conn.close()
        
        print("=" * 60)
        print(f"🎉 考勤規則更新完成！共更新/新增 {updated_count} 項規則")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新考勤規則時發生錯誤: {e}")
        return False

def verify_updates():
    """驗證更新結果"""
    print("\n🔍 驗證更新結果...")
    print("=" * 60)
    
    return check_current_settings()

def main():
    """主函數"""
    print("🚀 Han AttendanceOS 考勤規則更新工具")
    print(f"⏰ 執行時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 步驟 1: 檢查目前設定
    print("📋 步驟 1: 檢查目前設定")
    if not check_current_settings():
        print("❌ 無法檢查目前設定，程式結束")
        sys.exit(1)
    
    # 步驟 2: 更新規則
    print("\n📋 步驟 2: 更新考勤規則")
    if not update_attendance_rules():
        print("❌ 更新失敗，程式結束")
        sys.exit(1)
    
    # 步驟 3: 驗證結果
    print("\n📋 步驟 3: 驗證更新結果")
    if not verify_updates():
        print("❌ 驗證失敗")
        sys.exit(1)
    
    print("\n✅ 所有步驟完成！考勤規則已成功更新為符合規範的值。")

if __name__ == "__main__":
    main()
