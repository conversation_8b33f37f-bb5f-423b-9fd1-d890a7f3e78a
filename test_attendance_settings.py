#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考勤設定功能測試腳本
測試動態設定讀取功能和考勤規則應用是否正常運作
"""

import sys
import os
from datetime import datetime, time

# 添加 backend 目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_attendance_settings():
    """測試考勤設定模組"""
    print("🧪 測試考勤設定模組")
    print("=" * 60)
    
    try:
        from backend.utils.attendance_settings import attendance_settings
        
        # 測試各項設定讀取
        print("📋 測試設定讀取功能：")
        
        # 測試容許遲到時間
        late_tolerance = attendance_settings.get_late_tolerance_minutes()
        print(f"  ✅ 容許遲到時間: {late_tolerance} 分鐘")
        assert late_tolerance == 5, f"預期 5 分鐘，實際 {late_tolerance} 分鐘"
        
        # 測試容許早退時間
        early_leave_tolerance = attendance_settings.get_early_leave_tolerance_minutes()
        print(f"  ✅ 容許早退時間: {early_leave_tolerance} 分鐘")
        assert early_leave_tolerance == 5, f"預期 5 分鐘，實際 {early_leave_tolerance} 分鐘"
        
        # 測試最小加班單位
        overtime_minimum = attendance_settings.get_overtime_minimum_hours()
        print(f"  ✅ 最小加班單位: {overtime_minimum} 小時")
        assert overtime_minimum == 0.5, f"預期 0.5 小時，實際 {overtime_minimum} 小時"
        
        # 測試換日時間
        day_change_time = attendance_settings.get_day_change_time()
        print(f"  ✅ 換日時間: {day_change_time}")
        assert day_change_time == time(6, 0), f"預期 06:00，實際 {day_change_time}"
        
        print("\n🎉 所有設定測試通過！")
        return True
        
    except Exception as e:
        print(f"❌ 設定測試失敗: {e}")
        return False

def test_attendance_calculation():
    """測試考勤計算邏輯"""
    print("\n🧮 測試考勤計算邏輯")
    print("=" * 60)
    
    try:
        from backend.models.attendance import Attendance
        
        # 測試遲到計算
        print("📋 測試遲到計算：")
        
        # 模擬 9:03 打卡（遲到 3 分鐘，在容許範圍內）
        check_in_normal = datetime(2025, 6, 16, 9, 3, 0)
        status_normal = Attendance._calculate_status(check_in_normal)
        print(f"  ✅ 9:03 打卡狀態: {status_normal} (預期: normal)")
        
        # 模擬 9:08 打卡（遲到 8 分鐘，超過容許範圍）
        check_in_late = datetime(2025, 6, 16, 9, 8, 0)
        status_late = Attendance._calculate_status(check_in_late)
        print(f"  ✅ 9:08 打卡狀態: {status_late} (預期: late)")
        
        print("\n🎉 考勤計算測試通過！")
        return True
        
    except Exception as e:
        print(f"❌ 考勤計算測試失敗: {e}")
        return False

def test_database_settings():
    """測試資料庫設定值"""
    print("\n🗄️ 測試資料庫設定值")
    print("=" * 60)
    
    try:
        import sqlite3
        
        # 連接資料庫
        db_path = 'attendance.db'
        if not os.path.exists(db_path):
            print(f"❌ 資料庫檔案不存在: {db_path}")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查詢設定值
        cursor.execute("""
            SELECT rule_type, rule_value, description 
            FROM schedule_rules 
            WHERE rule_type IN (
                'late_tolerance_minutes',
                'early_leave_tolerance_minutes', 
                'overtime_minimum_hours',
                'day_change_time'
            )
            ORDER BY rule_type
        """)
        
        settings = cursor.fetchall()
        
        print("📋 資料庫中的設定值：")
        expected_values = {
            'day_change_time': '06:00',
            'early_leave_tolerance_minutes': '5',
            'late_tolerance_minutes': '5',
            'overtime_minimum_hours': '0.5'
        }
        
        for rule_type, rule_value, description in settings:
            expected = expected_values.get(rule_type, '未知')
            status = "✅" if rule_value == expected else "❌"
            print(f"  {status} {rule_type}: {rule_value} (預期: {expected})")
            
            if rule_value != expected:
                print(f"    ⚠️  設定值不符合預期！")
        
        conn.close()
        
        print("\n🎉 資料庫設定檢查完成！")
        return True
        
    except Exception as e:
        print(f"❌ 資料庫設定測試失敗: {e}")
        return False

def test_api_integration():
    """測試 API 整合"""
    print("\n🔌 測試 API 整合")
    print("=" * 60)
    
    try:
        # 測試 API 模組是否能正確導入設定
        from backend.api.attendance_api import attendance_settings as api_settings
        
        print("📋 測試 API 模組設定導入：")
        
        # 測試設定讀取
        late_tolerance = api_settings.get_late_tolerance_minutes()
        print(f"  ✅ API 模組讀取容許遲到時間: {late_tolerance} 分鐘")
        
        early_tolerance = api_settings.get_early_leave_tolerance_minutes()
        print(f"  ✅ API 模組讀取容許早退時間: {early_tolerance} 分鐘")
        
        overtime_minimum = api_settings.get_overtime_minimum_hours()
        print(f"  ✅ API 模組讀取最小加班單位: {overtime_minimum} 小時")
        
        print("\n🎉 API 整合測試通過！")
        return True
        
    except Exception as e:
        print(f"❌ API 整合測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 Han AttendanceOS 考勤設定功能測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("考勤設定模組", test_attendance_settings()))
    test_results.append(("考勤計算邏輯", test_attendance_calculation()))
    test_results.append(("資料庫設定值", test_database_settings()))
    test_results.append(("API 整合", test_api_integration()))
    
    # 顯示測試結果摘要
    print("\n📊 測試結果摘要")
    print("=" * 80)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {status} {test_name}")
        if result:
            passed_tests += 1
    
    print("=" * 80)
    print(f"📈 測試統計: {passed_tests}/{total_tests} 項測試通過")
    
    if passed_tests == total_tests:
        print("🎉 所有測試通過！考勤設定功能運作正常。")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查相關功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
