globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/admin/login/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/PWAInstallPrompt.tsx":{"*":{"id":"(ssr)/./src/components/PWAInstallPrompt.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ServiceWorkerRegistration.tsx":{"*":{"id":"(ssr)/./src/components/ServiceWorkerRegistration.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/m/login/page.tsx":{"*":{"id":"(ssr)/./src/app/m/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/m/page.tsx":{"*":{"id":"(ssr)/./src/app/m/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/login/page.tsx":{"id":"(app-pages-browser)/./src/app/admin/login/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx":{"id":"(app-pages-browser)/./src/components/PWAInstallPrompt.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/src/components/ServiceWorkerRegistration.tsx":{"id":"(app-pages-browser)/./src/components/ServiceWorkerRegistration.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx":{"id":"(app-pages-browser)/./src/contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx":{"id":"(app-pages-browser)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/login/page.tsx":{"id":"(app-pages-browser)/./src/app/m/login/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx":{"id":"(app-pages-browser)/./src/app/m/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/2024newdev/attend_next/frontend/src/":[],"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout":["static/css/app/layout.css"],"/Users/<USER>/2024newdev/attend_next/frontend/src/app/_not-found/page":[]}}