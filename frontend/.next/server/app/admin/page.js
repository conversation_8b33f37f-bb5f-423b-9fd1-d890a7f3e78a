/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/page";
exports.ids = ["app/admin/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.tsx */ \"(rsc)/./src/app/admin/page.tsx\")), \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.svg?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.svg?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.svg?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.svg?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/page\",\n        pathname: \"/admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstallPrompt.tsx */ \"(ssr)/./src/components/PWAInstallPrompt.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ServiceWorkerRegistration.tsx */ \"(ssr)/./src/components/ServiceWorkerRegistration.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fadmin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fadmin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.tsx */ \"(ssr)/./src/app/admin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa2V2aW4lMkYyMDI0bmV3ZGV2JTJGYXR0ZW5kX25leHQlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRmFkbWluJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUF3RyIsInNvdXJjZXMiOlsid2VicGFjazovL2F0dGVuZGFuY2UtbmV4dGpzLz9jNWNmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2tldmluLzIwMjRuZXdkZXYvYXR0ZW5kX25leHQvZnJvbnRlbmQvc3JjL2FwcC9hZG1pbi9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fadmin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar-clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction AdminPage() {\n    const { user, login: authLogin, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [autoLoginLoading, setAutoLoginLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 確保組件已掛載，避免 hydration 錯誤\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 自動登入功能 - 使用管理員測試帳號\n    const handleAutoLogin = async ()=>{\n        setAutoLoginLoading(true);\n        try {\n            console.log(\"開始自動登入管理員帳號\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_6__.login)({\n                employee_id: \"admin\",\n                password: \"admin123\"\n            });\n            if (response && response.user) {\n                const userData = response.user;\n                const user = {\n                    id: userData.employee_id,\n                    name: userData.employee_name,\n                    employee_id: userData.employee_code,\n                    department_id: userData.department_id,\n                    position: userData.role_id === 999 ? \"系統管理員\" : \"員工\",\n                    email: userData.email,\n                    role_id: userData.role_id,\n                    department_name: userData.department_name\n                };\n                authLogin(user);\n                console.log(\"自動登入成功:\", user);\n            } else {\n                console.error(\"自動登入失敗:\", response);\n            }\n        } catch (error) {\n            console.error(\"自動登入錯誤:\", error);\n        } finally{\n            setAutoLoginLoading(false);\n        }\n    };\n    // 頁面載入時檢查登入狀態 - 增加延遲避免過早重定向\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted && !loading && !user) {\n            console.log(\"檢測到未登入，延遲重定向到登錄頁面\");\n            const timer = setTimeout(()=>{\n                router.push(\"/admin/login\");\n            }, 500) // 給AuthContext更多時間載入\n            ;\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        mounted,\n        loading,\n        user,\n        router\n    ]);\n    // 在組件未掛載時顯示載入狀態，避免 hydration 錯誤\n    if (!mounted || loading || autoLoginLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: autoLoginLoading ? \"自動登入中...\" : \"載入中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 91,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n            lineNumber: 90,\n            columnNumber: 13\n        }, this);\n    }\n    // 如果沒有用戶，顯示登入選項\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-md mx-auto p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-16 h-16 text-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"管理員登入\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"請登入以訪問管理功能\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleAutoLogin,\n                                loading: autoLoginLoading,\n                                className: \"w-full\",\n                                children: \"使用測試帳號登入 (admin/admin123)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/login\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full\",\n                                    children: \"手動登入\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full\",\n                                    children: \"返回首頁\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 105,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n            lineNumber: 104,\n            columnNumber: 13\n        }, this);\n    }\n    // 如果不是管理員，顯示權限不足\n    if (user.role_id !== 999) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"權限不足\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"您需要管理員權限才能訪問此頁面\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    children: \"返回首頁\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/login\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    children: \"重新登入\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n            lineNumber: 136,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-sm border-b border-white/20 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"管理後台\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Han AttendanceOS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"歡迎，\",\n                                            user.name,\n                                            \" (\",\n                                            user.position,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 73\n                                            }, void 0),\n                                            children: \"返回首頁\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 157,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold mb-2\",\n                                            children: \"管理員控制台\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-base\",\n                                            children: \"歡迎使用 Han AttendanceOS 管理後台，您可以在這裡管理所有系統功能\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 rounded-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"總員工數\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"今日出勤\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"待審核請假\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"本月工時\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"168h\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/attendance-management\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"考勤管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"管理員工考勤記錄、班表設定、工時計算和異常處理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-indigo-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/attendance-processing\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"考勤資料整理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"自動計算遲到、早退、加班時間，整合請假資料並更新考勤狀態\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-cyan-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入整理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/employees\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"員工管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"管理員工基本資料、部門分組、角色權限和個人設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-green-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/leave-approval\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"請假審核\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"審核員工請假申請、管理假期餘額和請假政策設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-purple-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/overtime-approval\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"加班審核\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"審核員工加班申請、管理加班時數和加班政策設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-orange-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/punch-records\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"打卡記錄\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"查看和管理所有員工的打卡記錄和考勤統計\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-orange-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入查看\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 opacity-75\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"數據報表\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm mb-4\",\n                                        children: \"生成各種考勤報表、統計分析和趨勢預測\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-gray-400 text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"開發中...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/shifts\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"排班管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"設定班別時間、加班規則、休息時間和工時計算方式\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/masterdata\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"基本資料管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"管理學歷、職位、假別、薪資等級 基本資料設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-cyan-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/work-reports\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-violet-500 to-purple-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"工作回報管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"查看員工工作回報、提供回覆與指導、管理工作進度\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-violet-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/import-attendance\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"匯入考勤資料\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"從打卡機匯出的文字檔案匯入考勤記錄和員工資料\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-emerald-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入匯入\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/settings\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"系統設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"配置系統參數、考勤規則、通知設定和權限管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-indigo-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"快速操作\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"匯出本月考勤報表\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"批量審核請假\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"新增員工\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"系統備份\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 184,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n        lineNumber: 155,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PWAInstallPrompt.tsx":
/*!*********************************************!*\
  !*** ./src/components/PWAInstallPrompt.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PWAInstallPrompt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PWAInstallPrompt() {\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPrompt, setShowPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isIOS, setIsIOS] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStandalone, setIsStandalone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 檢測是否為iOS設備\n        const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);\n        setIsIOS(iOS);\n        // 檢測是否已經是獨立模式（已安裝）\n        const standalone = window.matchMedia(\"(display-mode: standalone)\").matches;\n        setIsStandalone(standalone);\n        // 監聽beforeinstallprompt事件\n        const handleBeforeInstallPrompt = (e)=>{\n            e.preventDefault();\n            setDeferredPrompt(e);\n            // 延遲顯示提示，讓用戶先體驗應用\n            setTimeout(()=>{\n                if (!standalone) {\n                    setShowPrompt(true);\n                }\n            }, 3000);\n        };\n        // 監聽應用安裝事件\n        const handleAppInstalled = ()=>{\n            setShowPrompt(false);\n            setDeferredPrompt(null);\n            console.log(\"PWA 已安裝\");\n        };\n        window.addEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n        window.addEventListener(\"appinstalled\", handleAppInstalled);\n        return ()=>{\n            window.removeEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n            window.removeEventListener(\"appinstalled\", handleAppInstalled);\n        };\n    }, []);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        try {\n            await deferredPrompt.prompt();\n            const { outcome } = await deferredPrompt.userChoice;\n            if (outcome === \"accepted\") {\n                console.log(\"用戶接受安裝\");\n            } else {\n                console.log(\"用戶拒絕安裝\");\n            }\n            setDeferredPrompt(null);\n            setShowPrompt(false);\n        } catch (error) {\n            console.error(\"安裝過程中發生錯誤:\", error);\n        }\n    };\n    const handleDismiss = ()=>{\n        setShowPrompt(false);\n        // 24小時後再次顯示\n        if (false) {}\n    };\n    // 如果已經是獨立模式，不顯示提示\n    if (isStandalone) return null;\n    // 檢查是否在24小時內被拒絕過（僅在客戶端執行）\n    if (false) {}\n    // iOS設備顯示手動安裝指引\n    if (isIOS && showPrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-4 left-4 right-4 z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-w-sm mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold text-gray-900 mb-1\",\n                                children: \"安裝考勤系統\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-3\",\n                                children: \"點擊 Safari 底部的分享按鈕 \\uD83D\\uDCE4，然後選擇「加入主畫面」\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleDismiss,\n                                    className: \"px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 transition-colors\",\n                                    children: \"稍後再說\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDismiss,\n                        className: \"w-6 h-6 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                lineNumber: 102,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n            lineNumber: 101,\n            columnNumber: 13\n        }, this);\n    }\n    // Android/Desktop 顯示安裝按鈕\n    if (deferredPrompt && showPrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-4 left-4 right-4 z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-w-sm mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold text-gray-900 mb-1\",\n                                children: \"安裝考勤系統\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-3\",\n                                children: \"安裝到您的設備，享受更好的使用體驗\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleInstallClick,\n                                        className: \"flex items-center space-x-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-indigo-600 hover:to-purple-700 transition-all duration-200 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"安裝\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDismiss,\n                                        className: \"px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors\",\n                                        children: \"稍後再說\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDismiss,\n                        className: \"w-6 h-6 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                lineNumber: 135,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n            lineNumber: 134,\n            columnNumber: 13\n        }, this);\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PWAInstallPrompt.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ServiceWorkerRegistration.tsx":
/*!******************************************************!*\
  !*** ./src/components/ServiceWorkerRegistration.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServiceWorkerRegistration)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ServiceWorkerRegistration() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (\"serviceWorker\" in navigator) {\n            navigator.serviceWorker.register(\"/sw.js\").then((registration)=>{\n                console.log(\"Service Worker 註冊成功:\", registration.scope);\n                // 檢查更新\n                registration.addEventListener(\"updatefound\", ()=>{\n                    const newWorker = registration.installing;\n                    if (newWorker) {\n                        newWorker.addEventListener(\"statechange\", ()=>{\n                            if (newWorker.state === \"installed\" && navigator.serviceWorker.controller) {\n                                // 有新版本可用\n                                console.log(\"新版本可用，建議重新載入頁面\");\n                                // 可以在這裡顯示更新提示\n                                if (confirm(\"發現新版本，是否立即更新？\")) {\n                                    newWorker.postMessage({\n                                        type: \"SKIP_WAITING\"\n                                    });\n                                    window.location.reload();\n                                }\n                            }\n                        });\n                    }\n                });\n            }).catch((error)=>{\n                console.error(\"Service Worker 註冊失敗:\", error);\n            });\n            // 監聽Service Worker控制權變更\n            navigator.serviceWorker.addEventListener(\"controllerchange\", ()=>{\n                console.log(\"Service Worker 控制權已變更\");\n                window.location.reload();\n            });\n        } else {\n            console.log(\"此瀏覽器不支援 Service Worker\");\n        }\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ServiceWorkerRegistration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n/**\n * 按鈕組件 - 遷移自 Han AttendanceOS 設計系統\n * \n * 功能特色：\n * - 完整的變體支援（主要、次要、成功、警告、錯誤等）\n * - 多種尺寸選項（xs, sm, md, lg, xl）\n * - 載入狀態支援\n * - 圖標支援\n * - 無障礙設計\n * - Apple 設計語言風格\n */ const Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = \"primary\", size = \"md\", loading = false, icon, children, disabled, ...props }, ref)=>{\n    // 基礎樣式\n    const baseStyles = [\n        // 基礎屬性\n        \"inline-flex items-center justify-center gap-2\",\n        \"font-medium text-center whitespace-nowrap\",\n        \"border border-transparent rounded-lg\",\n        \"transition-all duration-150 ease-in-out\",\n        \"cursor-pointer select-none\",\n        \"outline-none focus:outline-none\",\n        // 焦點樣式\n        \"focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n        // 禁用樣式\n        \"disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none\",\n        // 防止雙擊選中\n        \"tap-highlight-transparent\"\n    ];\n    // 尺寸樣式\n    const sizeStyles = {\n        xs: \"px-2 py-1 text-xs leading-tight rounded-sm min-h-[1.5rem]\",\n        sm: \"px-3 py-1.5 text-sm leading-tight rounded-md min-h-[2rem]\",\n        md: \"px-4 py-2 text-base leading-normal rounded-lg min-h-[2.5rem]\",\n        lg: \"px-6 py-3 text-lg leading-normal rounded-xl min-h-[3rem]\",\n        xl: \"px-8 py-4 text-xl leading-normal rounded-xl min-h-[3.5rem]\"\n    };\n    // 變體樣式\n    const variantStyles = {\n        primary: [\n            \"bg-gradient-primary text-white border-primary-600 shadow-soft\",\n            \"hover:bg-primary-700 hover:border-primary-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-primary-800 active:border-primary-800 active:shadow-soft active:translate-y-0\"\n        ],\n        secondary: [\n            \"bg-gradient-secondary text-white border-secondary-600 shadow-soft\",\n            \"hover:bg-secondary-700 hover:border-secondary-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-secondary-800 active:border-secondary-800 active:shadow-soft active:translate-y-0\"\n        ],\n        success: [\n            \"bg-gradient-success text-white border-success-600 shadow-soft\",\n            \"hover:bg-success-700 hover:border-success-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-success-800 active:border-success-800 active:shadow-soft active:translate-y-0\"\n        ],\n        warning: [\n            \"bg-gradient-warning text-white border-warning-600 shadow-soft\",\n            \"hover:bg-warning-700 hover:border-warning-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-warning-800 active:border-warning-800 active:shadow-soft active:translate-y-0\"\n        ],\n        error: [\n            \"bg-gradient-error text-white border-error-600 shadow-soft\",\n            \"hover:bg-error-700 hover:border-error-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-error-800 active:border-error-800 active:shadow-soft active:translate-y-0\"\n        ],\n        info: [\n            \"bg-gradient-to-r from-info-500 to-info-600 text-white border-info-600 shadow-soft\",\n            \"hover:from-info-600 hover:to-info-700 hover:border-info-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:from-info-700 active:to-info-800 active:border-info-800 active:shadow-soft active:translate-y-0\"\n        ],\n        outline: [\n            \"bg-transparent text-neutral-700 border-neutral-300\",\n            \"hover:bg-neutral-50 hover:border-neutral-400\",\n            \"active:bg-neutral-100\"\n        ],\n        ghost: [\n            \"bg-transparent text-neutral-700 border-transparent\",\n            \"hover:bg-neutral-100 hover:text-neutral-900\",\n            \"active:bg-neutral-200\"\n        ],\n        link: [\n            \"bg-transparent text-primary-600 border-transparent p-0 h-auto min-h-0\",\n            \"hover:text-primary-700 hover:underline\",\n            \"active:text-primary-800\"\n        ],\n        glass: [\n            \"backdrop-blur-sm bg-white/20 text-neutral-900 border-white/20 shadow-soft\",\n            \"hover:bg-white/30 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-white/40 active:shadow-soft active:translate-y-0\"\n        ]\n    };\n    // 載入動畫樣式\n    const loadingSpinner = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"animate-spin h-4 w-4\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                className: \"opacity-25\",\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\",\n                stroke: \"currentColor\",\n                strokeWidth: \"4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/ui/button.tsx\",\n                lineNumber: 134,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                className: \"opacity-75\",\n                fill: \"currentColor\",\n                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/ui/button.tsx\",\n                lineNumber: 142,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/ui/button.tsx\",\n        lineNumber: 128,\n        columnNumber: 13\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseStyles, sizeStyles[size], variantStyles[variant], className),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && loadingSpinner,\n            !loading && icon && icon,\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/ui/button.tsx\",\n        lineNumber: 151,\n        columnNumber: 13\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 確保組件已掛載，避免 hydration 錯誤\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 檢查本地存儲中的用戶資訊\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return; // 只有在組件掛載後才執行\n        const initAuth = async ()=>{\n            try {\n                console.log(\"開始認證初始化\");\n                // 首先檢查 localStorage\n                const storedUser = localStorage.getItem(\"user\");\n                console.log(\"本地存儲的用戶資料:\", storedUser);\n                if (storedUser) {\n                    const userData = JSON.parse(storedUser);\n                    console.log(\"解析的用戶資料:\", userData);\n                    setUser(userData);\n                    // 簡化驗證 - 暫時跳過會話驗證，專注解決跳轉問題\n                    console.log(\"用戶已設定，跳過會話驗證\");\n                } else {\n                    console.log(\"沒有本地用戶資料\");\n                }\n            } catch (error) {\n                console.error(\"認證初始化失敗:\", error);\n                if (false) {}\n                setUser(null);\n            } finally{\n                console.log(\"認證初始化完成，設定 loading = false\");\n                setLoading(false);\n            }\n        };\n        initAuth();\n    }, [\n        mounted\n    ]);\n    const login = (userData)=>{\n        console.log(\"AuthContext login 被調用:\", userData);\n        setUser(userData);\n        if (false) {}\n        console.log(\"AuthContext 用戶狀態已更新\");\n    };\n    const logout = ()=>{\n        setUser(null);\n        if (false) {}\n        router.push(\"/login\");\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        isAuthenticated: !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// 保護路由的 HOC\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { isAuthenticated, loading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        console.log(\"withAuth 檢查:\", {\n            loading,\n            isAuthenticated\n        });\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!loading && !isAuthenticated) {\n                console.log(\"withAuth: 未認證，導向登入頁面\");\n                router.push(\"/login\");\n            }\n        }, [\n            isAuthenticated,\n            loading,\n            router\n        ]);\n        if (loading) {\n            console.log(\"withAuth: 載入中\");\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            console.log(\"withAuth: 未認證，返回 null\");\n            return null;\n        }\n        console.log(\"withAuth: 已認證，渲染組件\");\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n            lineNumber: 137,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addEmployeePromotion: () => (/* binding */ addEmployeePromotion),\n/* harmony export */   addEmployeeReward: () => (/* binding */ addEmployeeReward),\n/* harmony export */   apiDelete: () => (/* binding */ apiDelete),\n/* harmony export */   apiGet: () => (/* binding */ apiGet),\n/* harmony export */   apiPost: () => (/* binding */ apiPost),\n/* harmony export */   apiPut: () => (/* binding */ apiPut),\n/* harmony export */   clockIn: () => (/* binding */ clockIn),\n/* harmony export */   clockOut: () => (/* binding */ clockOut),\n/* harmony export */   createLeaveRequest: () => (/* binding */ createLeaveRequest),\n/* harmony export */   exportAttendanceExcel: () => (/* binding */ exportAttendanceExcel),\n/* harmony export */   exportAttendancePDF: () => (/* binding */ exportAttendancePDF),\n/* harmony export */   getAttendanceEditData: () => (/* binding */ getAttendanceEditData),\n/* harmony export */   getAttendanceManagementRecordDetail: () => (/* binding */ getAttendanceManagementRecordDetail),\n/* harmony export */   getAttendanceManagementRecords: () => (/* binding */ getAttendanceManagementRecords),\n/* harmony export */   getAttendanceRecords: () => (/* binding */ getAttendanceRecords),\n/* harmony export */   getAttendanceStatusOptions: () => (/* binding */ getAttendanceStatusOptions),\n/* harmony export */   getClockStatusTypes: () => (/* binding */ getClockStatusTypes),\n/* harmony export */   getDepartments: () => (/* binding */ getDepartments),\n/* harmony export */   getEducationLevels: () => (/* binding */ getEducationLevels),\n/* harmony export */   getEmployee: () => (/* binding */ getEmployee),\n/* harmony export */   getEmployeeFullDetails: () => (/* binding */ getEmployeeFullDetails),\n/* harmony export */   getEmployeePromotions: () => (/* binding */ getEmployeePromotions),\n/* harmony export */   getEmployeeRewards: () => (/* binding */ getEmployeeRewards),\n/* harmony export */   getEmployees: () => (/* binding */ getEmployees),\n/* harmony export */   getLeaveRequests: () => (/* binding */ getLeaveRequests),\n/* harmony export */   getPositions: () => (/* binding */ getPositions),\n/* harmony export */   getPromotionTypes: () => (/* binding */ getPromotionTypes),\n/* harmony export */   getPunchRecordDetail: () => (/* binding */ getPunchRecordDetail),\n/* harmony export */   getPunchRecords: () => (/* binding */ getPunchRecords),\n/* harmony export */   getRewardTypes: () => (/* binding */ getRewardTypes),\n/* harmony export */   getRoles: () => (/* binding */ getRoles),\n/* harmony export */   getShifts: () => (/* binding */ getShifts),\n/* harmony export */   getSkills: () => (/* binding */ getSkills),\n/* harmony export */   getTodayAttendance: () => (/* binding */ getTodayAttendance),\n/* harmony export */   healthCheck: () => (/* binding */ healthCheck),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   saveAttendanceEdit: () => (/* binding */ saveAttendanceEdit),\n/* harmony export */   updateAttendanceShift: () => (/* binding */ updateAttendanceShift),\n/* harmony export */   updateEmployee: () => (/* binding */ updateEmployee),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\n/**\n * API 客戶端 - 連接到 Flask API\n * 提供統一的 API 調用接口\n */ // Flask API 基礎 URL - 動態檢測環境\nconst getApiBaseUrl = ()=>{\n    if (false) {}\n    // 服務器端渲染環境\n    return \"http://localhost:7072\";\n};\nconst API_BASE_URL = getApiBaseUrl();\n/**\n * 基礎 API 調用函數\n */ async function apiCall(endpoint, options = {}) {\n    try {\n        // 每次調用時動態獲取API基礎URL\n        const apiBaseUrl = getApiBaseUrl();\n        const url = `${apiBaseUrl}${endpoint}`;\n        console.log(\"\\uD83C\\uDF10 API調用開始:\", {\n            url,\n            endpoint,\n            apiBaseUrl,\n            options\n        });\n        const defaultOptions = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                \"Pragma\": \"no-cache\",\n                \"Expires\": \"0\",\n                ...options.headers\n            },\n            credentials: \"include\",\n            cache: \"no-store\",\n            ...options\n        };\n        console.log(\"\\uD83D\\uDCE4 發送請求:\", {\n            url,\n            options: defaultOptions\n        });\n        const response = await fetch(url, defaultOptions);\n        console.log(\"\\uD83D\\uDCE5 收到響應:\", {\n            status: response.status,\n            statusText: response.statusText,\n            ok: response.ok,\n            headers: Object.fromEntries(response.headers.entries())\n        });\n        // 嘗試解析響應數據\n        let data;\n        let rawText = \"\";\n        try {\n            rawText = await response.text();\n            console.log(\"\\uD83D\\uDCC4 原始響應文本:\", rawText);\n            if (rawText.trim() === \"\") {\n                console.warn(\"⚠️ 響應為空\");\n                data = {\n                    error: \"伺服器返回空響應\"\n                };\n            } else {\n                data = JSON.parse(rawText);\n                console.log(\"\\uD83D\\uDCCA 解析後的數據:\", data);\n            }\n        } catch (parseError) {\n            console.error(\"❌ JSON解析失敗:\", parseError);\n            console.log(\"原始響應:\", rawText);\n            data = {\n                error: \"響應解析失敗\",\n                raw_response: rawText,\n                parse_error: parseError instanceof Error ? parseError.message : \"未知解析錯誤\"\n            };\n        }\n        if (!response.ok) {\n            console.error(\"❌ HTTP錯誤:\", {\n                status: response.status,\n                statusText: response.statusText,\n                data\n            });\n            // 對於HTTP錯誤狀態碼，返回錯誤響應而不是拋出異常\n            return {\n                success: false,\n                error: data.error || data.message || `HTTP ${response.status}: ${response.statusText}`,\n                data\n            };\n        }\n        console.log(\"✅ API調用成功:\", data);\n        return {\n            success: true,\n            data\n        };\n    } catch (error) {\n        console.error(\"\\uD83D\\uDCA5 API調用異常:\", {\n            endpoint,\n            error\n        });\n        let errorMessage = \"未知錯誤\";\n        if (error instanceof Error) {\n            errorMessage = error.message;\n            console.error(\"錯誤詳情:\", {\n                name: error.name,\n                message: error.message,\n                stack: error.stack\n            });\n        }\n        return {\n            success: false,\n            error: errorMessage,\n            data: undefined\n        };\n    }\n}\n/**\n * GET 請求\n */ async function apiGet(endpoint) {\n    return apiCall(endpoint, {\n        method: \"GET\"\n    });\n}\n/**\n * POST 請求\n */ async function apiPost(endpoint, data) {\n    return apiCall(endpoint, {\n        method: \"POST\",\n        body: data ? JSON.stringify(data) : undefined\n    });\n}\n/**\n * PUT 請求\n */ async function apiPut(endpoint, data) {\n    return apiCall(endpoint, {\n        method: \"PUT\",\n        body: data ? JSON.stringify(data) : undefined\n    });\n}\n/**\n * DELETE 請求\n */ async function apiDelete(endpoint) {\n    return apiCall(endpoint, {\n        method: \"DELETE\"\n    });\n}\n/**\n * 用戶登入\n */ async function login(credentials) {\n    // 調用 Flask API 的正確端點，使用正確的參數名稱\n    const response = await apiPost(\"/api/login\", {\n        employee_id: credentials.employee_id,\n        password: credentials.password\n    });\n    // 直接返回響應，讓前端處理成功或失敗的情況\n    return response;\n}\n/**\n * 驗證用戶會話\n */ async function verifySession() {\n    return apiGet(\"/api/auth/verify\");\n}\n/**\n * 用戶登出\n */ async function logout() {\n    return apiPost(\"/api/auth/logout\");\n}\n/**\n * 獲取考勤記錄\n */ async function getAttendanceRecords(params) {\n    const queryString = params ? new URLSearchParams(params).toString() : \"\";\n    return apiGet(`/api/attendance/records${queryString ? `?${queryString}` : \"\"}`);\n}\n/**\n * 打卡上班\n */ async function clockIn(data) {\n    return apiPost(\"/api/attendance/clock-in\", data);\n}\n/**\n * 打卡下班\n */ async function clockOut(data) {\n    return apiPost(\"/api/attendance/clock-out\", data);\n}\n/**\n * 獲取今日考勤記錄\n */ async function getTodayAttendance(employeeId) {\n    return apiGet(`/api/attendance/today/${employeeId}`);\n}\n/**\n * 獲取打卡原始記錄\n */ async function getPunchRecords(params) {\n    const queryParams = new URLSearchParams();\n    if (params?.page) queryParams.append(\"page\", params.page.toString());\n    if (params?.limit) queryParams.append(\"limit\", params.limit.toString());\n    if (params?.employee_id) queryParams.append(\"employee_id\", params.employee_id);\n    if (params?.department_id) queryParams.append(\"department_id\", params.department_id);\n    if (params?.start_date) queryParams.append(\"start_date\", params.start_date);\n    if (params?.end_date) queryParams.append(\"end_date\", params.end_date);\n    if (params?.status_code) queryParams.append(\"status_code\", params.status_code);\n    const url = `/api/punch/records${queryParams.toString() ? \"?\" + queryParams.toString() : \"\"}`;\n    return apiGet(url);\n}\n/**\n * 獲取單個打卡記錄詳情\n */ async function getPunchRecordDetail(recordId) {\n    return apiGet(`/api/punch/records/${recordId}`);\n}\n/**\n * 獲取打卡狀態類型\n */ async function getClockStatusTypes() {\n    return apiGet(\"/api/clock-status-types\");\n}\n/**\n * 獲取員工列表\n */ async function getEmployees(params) {\n    const queryString = params ? new URLSearchParams(params).toString() : \"\";\n    const response = await apiGet(`/api/employees${queryString ? `?${queryString}` : \"\"}`);\n    // Flask API 返回格式：{employees: [...]}，需要提取 employees 陣列\n    if (response.success && response.data && response.data.employees) {\n        return {\n            success: true,\n            data: response.data.employees\n        };\n    }\n    return {\n        success: false,\n        error: response.error || \"載入員工資料失敗\"\n    };\n}\n/**\n * 獲取單個員工資料\n */ async function getEmployee(id) {\n    return apiGet(`/api/employees/${id}`);\n}\n/**\n * 更新員工資料\n */ async function updateEmployee(id, data) {\n    return apiPut(`/api/employees/${id}`, data);\n}\n/**\n * 獲取部門列表\n */ async function getDepartments() {\n    const response = await apiGet(\"/api/departments\");\n    // Flask API 返回格式：{departments: [...]}，需要提取 departments 陣列\n    if (response.success && response.data && response.data.departments) {\n        return {\n            success: true,\n            data: response.data.departments\n        };\n    }\n    return {\n        success: false,\n        error: response.error || \"載入部門資料失敗\"\n    };\n}\n/**\n * 獲取請假記錄\n */ async function getLeaveRequests(params) {\n    const queryString = params ? new URLSearchParams(params).toString() : \"\";\n    return apiGet(`/api/leave-requests${queryString ? `?${queryString}` : \"\"}`);\n}\n/**\n * 創建請假申請\n */ async function createLeaveRequest(data) {\n    return apiPost(\"/api/leave-requests\", data);\n}\n// =============================================================================\n// 健康檢查 API\n// =============================================================================\n/**\n * 檢查 API 健康狀態\n */ async function healthCheck() {\n    return apiGet(\"/api/health\");\n}\n// =============================================================================\n// 員工詳細資料相關 API\n// =============================================================================\n/**\n * 獲取技能列表\n */ async function getSkills() {\n    return apiGet(\"/api/skills\");\n}\n/**\n * 獲取員工升遷紀錄\n */ async function getEmployeePromotions(employeeId) {\n    return apiGet(`/api/employees/${employeeId}/promotions`);\n}\n/**\n * 新增員工升遷紀錄\n */ async function addEmployeePromotion(employeeId, data) {\n    return apiPost(`/api/employees/${employeeId}/promotions`, data);\n}\n/**\n * 獲取升遷類型\n */ async function getPromotionTypes() {\n    return apiGet(\"/api/promotion-types\");\n}\n/**\n * 獲取員工獎懲紀錄\n */ async function getEmployeeRewards(employeeId) {\n    return apiGet(`/api/employees/${employeeId}/rewards`);\n}\n/**\n * 新增員工獎懲紀錄\n */ async function addEmployeeReward(employeeId, data) {\n    return apiPost(`/api/employees/${employeeId}/rewards`, data);\n}\n/**\n * 獲取獎懲類型\n */ async function getRewardTypes() {\n    return apiGet(\"/api/reward-types\");\n}\n/**\n * 獲取學歷等級\n */ async function getEducationLevels() {\n    return apiGet(\"/api/education-levels\");\n}\n/**\n * 獲取職位列表\n */ async function getPositions() {\n    return apiGet(\"/api/masterdata/positions\");\n}\n/**\n * 獲取角色權限列表\n */ async function getRoles() {\n    return apiGet(\"/api/permissions/roles\");\n}\n/**\n * 獲取考勤狀態選項\n * 從 AttendanceHelper 的 STATUS_CONFIG 生成狀態選項\n */ function getAttendanceStatusOptions() {\n    // 這裡我們使用前端已有的 AttendanceHelper 配置\n    // 在實際使用時，前端會直接調用 AttendanceHelper.STATUS_CONFIG\n    const statusOptions = [\n        {\n            code: \"normal\",\n            name: \"正常\",\n            color: \"text-green-600\",\n            bgColor: \"bg-green-100\",\n            icon: \"check-circle\"\n        },\n        {\n            code: \"late\",\n            name: \"遲到\",\n            color: \"text-yellow-600\",\n            bgColor: \"bg-yellow-100\",\n            icon: \"clock\"\n        },\n        {\n            code: \"early_leave\",\n            name: \"早退\",\n            color: \"text-orange-600\",\n            bgColor: \"bg-orange-100\",\n            icon: \"log-out\"\n        },\n        {\n            code: \"absent\",\n            name: \"缺勤\",\n            color: \"text-red-600\",\n            bgColor: \"bg-red-100\",\n            icon: \"x-circle\"\n        },\n        {\n            code: \"leave\",\n            name: \"請假\",\n            color: \"text-blue-600\",\n            bgColor: \"bg-blue-100\",\n            icon: \"calendar-x\"\n        },\n        {\n            code: \"overtime\",\n            name: \"加班\",\n            color: \"text-purple-600\",\n            bgColor: \"bg-purple-100\",\n            icon: \"clock\"\n        },\n        {\n            code: \"manual\",\n            name: \"手動\",\n            color: \"text-gray-600\",\n            bgColor: \"bg-gray-100\",\n            icon: \"edit\"\n        }\n    ];\n    return statusOptions;\n}\n/**\n * 獲取考勤管理記錄列表\n */ async function getAttendanceManagementRecords(params) {\n    const searchParams = new URLSearchParams();\n    Object.entries(params).forEach(([key, value])=>{\n        if (value !== undefined && value !== \"\") {\n            searchParams.append(key, value.toString());\n        }\n    });\n    return apiGet(`/api/attendance/records?${searchParams}`);\n}\n/**\n * 獲取考勤記錄詳情\n */ async function getAttendanceManagementRecordDetail(recordId) {\n    return apiGet(`/api/attendance/records/${recordId}`);\n}\n/**\n * 更新考勤記錄班表\n */ async function updateAttendanceShift(recordId, shiftId) {\n    return apiPost(\"/api/attendance/management/update-shift\", {\n        record_id: recordId,\n        shift_id: shiftId\n    });\n}\n/**\n * 獲取考勤記錄編輯數據（包含員工完整資料）\n */ async function getAttendanceEditData(recordId) {\n    return apiGet(`/api/attendance/edit/${recordId}`);\n}\n/**\n * 獲取員工完整詳細資料（用於編輯模態框）\n */ async function getEmployeeFullDetails(employeeId) {\n    try {\n        // 並行獲取所有相關資料\n        const [employeeRes, skillsRes, promotionsRes, rewardsRes, educationRes] = await Promise.all([\n            getEmployee(employeeId.toString()),\n            getSkills(),\n            getEmployeePromotions(employeeId),\n            getEmployeeRewards(employeeId),\n            getEducationLevels()\n        ]);\n        return {\n            success: true,\n            data: {\n                employee: employeeRes.data,\n                skills: skillsRes.data?.skills || [],\n                promotions: promotionsRes.data?.promotions || [],\n                rewards: rewardsRes.data?.rewards || [],\n                education_levels: educationRes.data?.education_levels || []\n            }\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"獲取員工詳細資料失敗\"\n        };\n    }\n}\n/**\n * 保存考勤記錄編輯\n */ async function saveAttendanceEdit(recordId, editData) {\n    return apiPut(`/api/attendance/edit/${recordId}`, editData);\n}\n/**\n * 獲取班表列表\n */ async function getShifts() {\n    return apiGet(\"/api/shifts\");\n}\n/**\n * 匯出考勤記錄Excel\n */ async function exportAttendanceExcel(params) {\n    try {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value])=>{\n            if (value !== undefined && value !== \"\") {\n                searchParams.append(key, value.toString());\n            }\n        });\n        const response = await fetch(`${API_BASE_URL}/api/attendance/records/export?${searchParams}`);\n        if (response.ok) {\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = `考勤記錄_${new Date().toISOString().split(\"T\")[0]}.xlsx`;\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            return {\n                success: true\n            };\n        } else {\n            return {\n                success: false,\n                error: \"匯出失敗\"\n            };\n        }\n    } catch (error) {\n        console.error(\"匯出Excel失敗:\", error);\n        return {\n            success: false,\n            error: \"匯出Excel失敗\"\n        };\n    }\n}\n/**\n * 匯出考勤記錄PDF\n */ async function exportAttendancePDF(params) {\n    try {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value])=>{\n            if (value !== undefined && value !== \"\") {\n                searchParams.append(key, value.toString());\n            }\n        });\n        const response = await fetch(`${API_BASE_URL}/api/attendance/records/export-pdf?${searchParams}`);\n        if (response.ok) {\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = `考勤記錄_${new Date().toISOString().split(\"T\")[0]}.pdf`;\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            return {\n                success: true\n            };\n        } else {\n            return {\n                success: false,\n                error: \"匯出失敗\"\n            };\n        }\n    } catch (error) {\n        console.error(\"匯出PDF失敗:\", error);\n        return {\n            success: false,\n            error: \"匯出PDF失敗\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateHours: () => (/* binding */ calculateHours),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * 合併 Tailwind CSS 類名的工具函數\n * 使用 clsx 處理條件類名，使用 tailwind-merge 解決衝突\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化日期為 YYYY-MM-DD 格式\n */ function formatDate(date) {\n    const d = new Date(date);\n    return d.toISOString().split(\"T\")[0];\n}\n/**\n * 格式化時間為 HH:MM 格式\n */ function formatTime(date) {\n    const d = new Date(date);\n    return d.toTimeString().slice(0, 5);\n}\n/**\n * 格式化日期時間為本地格式\n */ function formatDateTime(date) {\n    const d = new Date(date);\n    return d.toLocaleString(\"zh-TW\", {\n        year: \"numeric\",\n        month: \"2-digit\",\n        day: \"2-digit\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    });\n}\n/**\n * 計算兩個時間之間的小時差\n */ function calculateHours(startTime, endTime) {\n    const start = new Date(`2000-01-01 ${startTime}`);\n    const end = new Date(`2000-01-01 ${endTime}`);\n    const diffMs = end.getTime() - start.getTime();\n    return Math.round(diffMs / (1000 * 60 * 60) * 100) / 100;\n}\n/**\n * 狀態映射函數\n */ function getStatusColor(status) {\n    const statusMap = {\n        \"active\": \"text-success-600 bg-success-50\",\n        \"inactive\": \"text-neutral-600 bg-neutral-50\",\n        \"pending\": \"text-warning-600 bg-warning-50\",\n        \"approved\": \"text-success-600 bg-success-50\",\n        \"rejected\": \"text-error-600 bg-error-50\",\n        \"present\": \"text-success-600 bg-success-50\",\n        \"absent\": \"text-error-600 bg-error-50\",\n        \"late\": \"text-warning-600 bg-warning-50\"\n    };\n    return statusMap[status] || \"text-neutral-600 bg-neutral-50\";\n}\n/**\n * API 錯誤處理\n */ function handleApiError(error) {\n    if (error.response?.data?.message) {\n        return error.response.data.message;\n    }\n    if (error.message) {\n        return error.message;\n    }\n    return \"發生未知錯誤，請稍後再試\";\n}\n/**\n * 防抖函數\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 節流函數\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"17a5a9779ec4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXR0ZW5kYW5jZS1uZXh0anMvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzA3YWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxN2E1YTk3NzllYzRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_PWAInstallPrompt__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/PWAInstallPrompt */ \"(rsc)/./src/components/PWAInstallPrompt.tsx\");\n/* harmony import */ var _components_ServiceWorkerRegistration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ServiceWorkerRegistration */ \"(rsc)/./src/components/ServiceWorkerRegistration.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Han AttendanceOS\",\n    description: \"遠漢科技考勤管理系統 - 智能打卡、請假申請、工作回報\",\n    applicationName: \"Han AttendanceOS\",\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: \"default\",\n        title: \"考勤系統\"\n    },\n    formatDetection: {\n        telephone: false\n    },\n    manifest: \"/manifest.json\",\n    icons: {\n        icon: [\n            {\n                url: \"/favicon.ico\",\n                type: \"image/x-icon\",\n                sizes: \"16x16\"\n            },\n            {\n                url: \"/icon-32x32.png\",\n                sizes: \"32x32\",\n                type: \"image/png\"\n            },\n            {\n                url: \"/icon-16x16.png\",\n                sizes: \"16x16\",\n                type: \"image/png\"\n            }\n        ],\n        shortcut: \"/favicon.ico\",\n        apple: \"/icons/apple-touch-icon.png\"\n    },\n    other: {\n        \"mobile-web-app-capable\": \"yes\",\n        \"msapplication-TileColor\": \"#4f46e5\",\n        \"msapplication-TileImage\": \"/icons/icon-144x144.png\",\n        \"msapplication-config\": \"/browserconfig.xml\"\n    }\n};\n// 將 themeColor 移到 viewport 配置\nconst viewport = {\n    themeColor: \"#4f46e5\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-TW\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PWAInstallPrompt__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ServiceWorkerRegistration__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBT01BO0FBTGdCO0FBQytCO0FBQ087QUFDa0I7QUFJdkUsTUFBTUksV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxpQkFBaUI7SUFDakJDLGFBQWE7UUFDWEMsU0FBUztRQUNUQyxnQkFBZ0I7UUFDaEJMLE9BQU87SUFDVDtJQUNBTSxpQkFBaUI7UUFDZkMsV0FBVztJQUNiO0lBQ0FDLFVBQVU7SUFDVkMsT0FBTztRQUNMQyxNQUFNO1lBQ0o7Z0JBQUVDLEtBQUs7Z0JBQWdCQyxNQUFNO2dCQUFnQkMsT0FBTztZQUFRO1lBQzVEO2dCQUFFRixLQUFLO2dCQUFtQkUsT0FBTztnQkFBU0QsTUFBTTtZQUFZO1lBQzVEO2dCQUFFRCxLQUFLO2dCQUFtQkUsT0FBTztnQkFBU0QsTUFBTTtZQUFZO1NBQzdEO1FBQ0RFLFVBQVU7UUFDVkMsT0FBTztJQUNUO0lBQ0FDLE9BQU87UUFDTCwwQkFBMEI7UUFDMUIsMkJBQTJCO1FBQzNCLDJCQUEyQjtRQUMzQix3QkFBd0I7SUFDMUI7QUFDRixFQUFDO0FBRUQsOEJBQThCO0FBQ3ZCLE1BQU1DLFdBQVc7SUFDdEJDLFlBQVk7QUFDZCxFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVc3QiwrSkFBZTtzQkFDOUIsNEVBQUNDLCtEQUFZQTs7b0JBQ1Z3QjtrQ0FDRCw4REFBQ3ZCLG9FQUFnQkE7Ozs7O2tDQUNqQiw4REFBQ0MsNkVBQXlCQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3BDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXR0ZW5kYW5jZS1uZXh0anMvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnXG5pbXBvcnQgUFdBSW5zdGFsbFByb21wdCBmcm9tICdAL2NvbXBvbmVudHMvUFdBSW5zdGFsbFByb21wdCdcbmltcG9ydCBTZXJ2aWNlV29ya2VyUmVnaXN0cmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9TZXJ2aWNlV29ya2VyUmVnaXN0cmF0aW9uJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnSGFuIEF0dGVuZGFuY2VPUycsXG4gIGRlc2NyaXB0aW9uOiAn6YGg5ryi56eR5oqA6ICD5Yuk566h55CG57O757WxIC0g5pm66IO95omT5Y2h44CB6KuL5YGH55Sz6KuL44CB5bel5L2c5Zue5aCxJyxcbiAgYXBwbGljYXRpb25OYW1lOiAnSGFuIEF0dGVuZGFuY2VPUycsXG4gIGFwcGxlV2ViQXBwOiB7XG4gICAgY2FwYWJsZTogdHJ1ZSxcbiAgICBzdGF0dXNCYXJTdHlsZTogJ2RlZmF1bHQnLFxuICAgIHRpdGxlOiAn6ICD5Yuk57O757WxJyxcbiAgfSxcbiAgZm9ybWF0RGV0ZWN0aW9uOiB7XG4gICAgdGVsZXBob25lOiBmYWxzZSxcbiAgfSxcbiAgbWFuaWZlc3Q6ICcvbWFuaWZlc3QuanNvbicsXG4gIGljb25zOiB7XG4gICAgaWNvbjogW1xuICAgICAgeyB1cmw6ICcvZmF2aWNvbi5pY28nLCB0eXBlOiAnaW1hZ2UveC1pY29uJywgc2l6ZXM6ICcxNngxNicgfSxcbiAgICAgIHsgdXJsOiAnL2ljb24tMzJ4MzIucG5nJywgc2l6ZXM6ICczMngzMicsIHR5cGU6ICdpbWFnZS9wbmcnIH0sXG4gICAgICB7IHVybDogJy9pY29uLTE2eDE2LnBuZycsIHNpemVzOiAnMTZ4MTYnLCB0eXBlOiAnaW1hZ2UvcG5nJyB9LFxuICAgIF0sXG4gICAgc2hvcnRjdXQ6ICcvZmF2aWNvbi5pY28nLFxuICAgIGFwcGxlOiAnL2ljb25zL2FwcGxlLXRvdWNoLWljb24ucG5nJyxcbiAgfSxcbiAgb3RoZXI6IHtcbiAgICAnbW9iaWxlLXdlYi1hcHAtY2FwYWJsZSc6ICd5ZXMnLFxuICAgICdtc2FwcGxpY2F0aW9uLVRpbGVDb2xvcic6ICcjNGY0NmU1JyxcbiAgICAnbXNhcHBsaWNhdGlvbi1UaWxlSW1hZ2UnOiAnL2ljb25zL2ljb24tMTQ0eDE0NC5wbmcnLFxuICAgICdtc2FwcGxpY2F0aW9uLWNvbmZpZyc6ICcvYnJvd3NlcmNvbmZpZy54bWwnLFxuICB9LFxufVxuXG4vLyDlsIcgdGhlbWVDb2xvciDnp7vliLAgdmlld3BvcnQg6YWN572uXG5leHBvcnQgY29uc3Qgdmlld3BvcnQgPSB7XG4gIHRoZW1lQ29sb3I6ICcjNGY0NmU1Jyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInpoLVRXXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDxQV0FJbnN0YWxsUHJvbXB0IC8+XG4gICAgICAgICAgPFNlcnZpY2VXb3JrZXJSZWdpc3RyYXRpb24gLz5cbiAgICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJBdXRoUHJvdmlkZXIiLCJQV0FJbnN0YWxsUHJvbXB0IiwiU2VydmljZVdvcmtlclJlZ2lzdHJhdGlvbiIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImFwcGxpY2F0aW9uTmFtZSIsImFwcGxlV2ViQXBwIiwiY2FwYWJsZSIsInN0YXR1c0JhclN0eWxlIiwiZm9ybWF0RGV0ZWN0aW9uIiwidGVsZXBob25lIiwibWFuaWZlc3QiLCJpY29ucyIsImljb24iLCJ1cmwiLCJ0eXBlIiwic2l6ZXMiLCJzaG9ydGN1dCIsImFwcGxlIiwib3RoZXIiLCJ2aWV3cG9ydCIsInRoZW1lQ29sb3IiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/PWAInstallPrompt.tsx":
/*!*********************************************!*\
  !*** ./src/components/PWAInstallPrompt.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ServiceWorkerRegistration.tsx":
/*!******************************************************!*\
  !*** ./src/components/ServiceWorkerRegistration.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/components/ServiceWorkerRegistration.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx#withAuth`);


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.svg?__next_metadata__":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.svg?__next_metadata__ ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/svg+xml\",\"sizes\":\"any\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"icon.svg\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"?d30912376772e962\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2ljb24uc3ZnP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hdHRlbmRhbmNlLW5leHRqcy8uL3NyYy9hcHAvaWNvbi5zdmc/MTM5NCJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS9zdmcreG1sXCIsXCJzaXplc1wiOlwiYW55XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiaWNvbi5zdmdcIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiP2QzMDkxMjM3Njc3MmU5NjJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.svg?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();