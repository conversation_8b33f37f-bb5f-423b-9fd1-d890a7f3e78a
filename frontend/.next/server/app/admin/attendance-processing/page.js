/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/attendance-processing/page";
exports.ids = ["app/admin/attendance-processing/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fattendance-processing%2Fpage&page=%2Fadmin%2Fattendance-processing%2Fpage&appPaths=%2Fadmin%2Fattendance-processing%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fattendance-processing%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fattendance-processing%2Fpage&page=%2Fadmin%2Fattendance-processing%2Fpage&appPaths=%2Fadmin%2Fattendance-processing%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fattendance-processing%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'attendance-processing',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/attendance-processing/page.tsx */ \"(rsc)/./src/app/admin/attendance-processing/page.tsx\")), \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/attendance-processing/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/attendance-processing/page\",\n        pathname: \"/admin/attendance-processing\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fattendance-processing%2Fpage&page=%2Fadmin%2Fattendance-processing%2Fpage&appPaths=%2Fadmin%2Fattendance-processing%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fattendance-processing%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa2V2aW4lMkYyMDI0bmV3ZGV2JTJGYXR0ZW5kX25leHQlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmFwcC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZrZXZpbiUyRjIwMjRuZXdkZXYlMkZhdHRlbmRfbmV4dCUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZrZXZpbiUyRjIwMjRuZXdkZXYlMkZhdHRlbmRfbmV4dCUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZrZXZpbiUyRjIwMjRuZXdkZXYlMkZhdHRlbmRfbmV4dCUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmtldmluJTJGMjAyNG5ld2RldiUyRmF0dGVuZF9uZXh0JTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZub3QtZm91bmQtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZrZXZpbiUyRjIwMjRuZXdkZXYlMkZhdHRlbmRfbmV4dCUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQXdJO0FBQ3hJO0FBQ0Esb09BQXlJO0FBQ3pJO0FBQ0EsME9BQTRJO0FBQzVJO0FBQ0Esd09BQTJJO0FBQzNJO0FBQ0Esa1BBQWdKO0FBQ2hKO0FBQ0Esc1FBQTBKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXR0ZW5kYW5jZS1uZXh0anMvPzQ2OTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMva2V2aW4vMjAyNG5ld2Rldi9hdHRlbmRfbmV4dC9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9rZXZpbi8yMDI0bmV3ZGV2L2F0dGVuZF9uZXh0L2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9rZXZpbi8yMDI0bmV3ZGV2L2F0dGVuZF9uZXh0L2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9rZXZpbi8yMDI0bmV3ZGV2L2F0dGVuZF9uZXh0L2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2tldmluLzIwMjRuZXdkZXYvYXR0ZW5kX25leHQvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9rZXZpbi8yMDI0bmV3ZGV2L2F0dGVuZF9uZXh0L2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstallPrompt.tsx */ \"(ssr)/./src/components/PWAInstallPrompt.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ServiceWorkerRegistration.tsx */ \"(ssr)/./src/components/ServiceWorkerRegistration.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fadmin%2Fattendance-processing%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fadmin%2Fattendance-processing%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/attendance-processing/page.tsx */ \"(ssr)/./src/app/admin/attendance-processing/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa2V2aW4lMkYyMDI0bmV3ZGV2JTJGYXR0ZW5kX25leHQlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRmFkbWluJTJGYXR0ZW5kYW5jZS1wcm9jZXNzaW5nJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUE4SCIsInNvdXJjZXMiOlsid2VicGFjazovL2F0dGVuZGFuY2UtbmV4dGpzLz8zZjk4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2tldmluLzIwMjRuZXdkZXYvYXR0ZW5kX25leHQvZnJvbnRlbmQvc3JjL2FwcC9hZG1pbi9hdHRlbmRhbmNlLXByb2Nlc3NpbmcvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fadmin%2Fattendance-processing%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/attendance-processing/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/admin/attendance-processing/page.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AttendanceProcessingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AttendanceProcessingPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 狀態管理\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start_date: \"\",\n        end_date: \"\"\n    });\n    const [employeeScope, setEmployeeScope] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"all\"\n    });\n    const [processingOptions, setProcessingOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        calculate_late_early: true,\n        calculate_overtime: true,\n        integrate_leaves: true,\n        overwrite_existing: false\n    });\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        pendingDays: 0,\n        pendingEmployees: 0,\n        estimatedTime: \"0秒\",\n        errorRecords: 0\n    });\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            type: \"info\",\n            message: \"系統就緒，等待處理指令\",\n            timestamp: new Date().toLocaleTimeString()\n        }\n    ]);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewData, setPreviewData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewLoading, setPreviewLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoMode, setAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化日期\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const formatDate = (date)=>date.toISOString().split(\"T\")[0];\n        setDateRange({\n            start_date: formatDate(yesterday),\n            end_date: formatDate(yesterday)\n        });\n    }, []);\n    // 更新統計資料\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        updateStatistics();\n    }, [\n        dateRange,\n        employeeScope\n    ]);\n    const updateStatistics = ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) return;\n        // 計算天數\n        const start = new Date(dateRange.start_date);\n        const end = new Date(dateRange.end_date);\n        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n        // 模擬計算員工數量\n        let employees = 156;\n        switch(employeeScope.type){\n            case \"department\":\n                employees = 45;\n                break;\n            case \"specific\":\n                employees = 12;\n                break;\n        }\n        // 估算時間\n        const estimatedSeconds = Math.ceil(days * employees * 0.5);\n        let timeText = \"\";\n        if (estimatedSeconds < 60) {\n            timeText = `${estimatedSeconds}秒`;\n        } else if (estimatedSeconds < 3600) {\n            timeText = `${Math.ceil(estimatedSeconds / 60)}分鐘`;\n        } else {\n            timeText = `${Math.ceil(estimatedSeconds / 3600)}小時`;\n        }\n        setStatistics({\n            pendingDays: days,\n            pendingEmployees: employees,\n            estimatedTime: timeText,\n            errorRecords: Math.floor(Math.random() * 10)\n        });\n    };\n    const handleQuickDateSelect = (type)=>{\n        const today = new Date();\n        let startDate, endDate;\n        switch(type){\n            case \"1\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                break;\n            case \"7\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                startDate.setDate(startDate.getDate() - 6);\n                break;\n            case \"30\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                startDate.setDate(startDate.getDate() - 29);\n                break;\n            case \"current-month\":\n                startDate = new Date(today.getFullYear(), today.getMonth(), 1);\n                endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);\n                break;\n            default:\n                return;\n        }\n        setDateRange({\n            start_date: startDate.toISOString().split(\"T\")[0],\n            end_date: endDate.toISOString().split(\"T\")[0]\n        });\n    };\n    const addLog = (type, message)=>{\n        const newLog = {\n            type,\n            message,\n            timestamp: new Date().toLocaleTimeString()\n        };\n        setLogs((prev)=>[\n                newLog,\n                ...prev.slice(0, 19)\n            ]) // 保持最多20條記錄\n        ;\n    };\n    const clearLogs = ()=>{\n        setLogs([\n            {\n                type: \"info\",\n                message: \"系統就緒，等待處理指令\",\n                timestamp: new Date().toLocaleTimeString()\n            }\n        ]);\n    };\n    const startProcessing = async ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) {\n            alert(\"請選擇日期範圍\");\n            return;\n        }\n        setIsProcessing(true);\n        setProgress(0);\n        addLog(\"info\", \"開始處理考勤資料...\");\n        try {\n            // 準備API請求資料\n            const processingData = {\n                date_range: dateRange,\n                employee_scope: employeeScope,\n                processing_options: processingOptions\n            };\n            addLog(\"info\", \"發送處理請求到後端...\");\n            // 嘗試調用後端API，如果失敗則使用模擬數據\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/execute\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(processingData)\n                });\n                if (!response.ok) {\n                    throw new Error(`API請求失敗: ${response.status}`);\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"處理啟動失敗\");\n                }\n                const processingId = result.processing_id;\n                addLog(\"success\", `處理已啟動，處理ID: ${processingId}`);\n                addLog(\"info\", `預計處理 ${result.total_records} 條記錄`);\n                // 開始監控處理進度\n                monitorProcessingProgress(processingId);\n            } catch (apiError) {\n                // API調用失敗，使用模擬處理\n                addLog(\"warning\", \"後端API不可用，使用模擬處理模式\");\n                simulateProcessing();\n            }\n        } catch (error) {\n            addLog(\"error\", `處理失敗: ${error instanceof Error ? error.message : \"未知錯誤\"}`);\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const monitorProcessingProgress = async (processingId)=>{\n        const checkProgress = async ()=>{\n            try {\n                const response = await fetch(`http://localhost:7073/api/attendance/processing/status/${processingId}`);\n                if (!response.ok) {\n                    throw new Error(`狀態查詢失敗: ${response.status}`);\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"狀態查詢失敗\");\n                }\n                const statusData = result.data;\n                // 更新進度\n                setProgress(statusData.progress);\n                addLog(\"info\", `${statusData.current_step} (${statusData.progress}%)`);\n                // 檢查是否完成\n                if (statusData.status === \"completed\") {\n                    addLog(\"success\", \"處理完成！\");\n                    addLog(\"info\", `創建: ${statusData.results.created}, 更新: ${statusData.results.updated}, 失敗: ${statusData.results.failed}`);\n                    // 顯示錯誤（如果有）\n                    if (statusData.errors.length > 0) {\n                        statusData.errors.forEach((error)=>{\n                            addLog(\"warning\", error);\n                        });\n                    }\n                    setTimeout(()=>{\n                        setIsProcessing(false);\n                        setProgress(0);\n                    }, 3000);\n                    return;\n                } else if (statusData.status === \"failed\") {\n                    addLog(\"error\", \"處理失敗\");\n                    statusData.errors.forEach((error)=>{\n                        addLog(\"error\", error);\n                    });\n                    setIsProcessing(false);\n                    setProgress(0);\n                    return;\n                }\n                // 繼續監控\n                setTimeout(checkProgress, 1000);\n            } catch (error) {\n                addLog(\"error\", `監控進度失敗: ${error instanceof Error ? error.message : \"未知錯誤\"}`);\n                setIsProcessing(false);\n                setProgress(0);\n            }\n        };\n        // 開始監控\n        setTimeout(checkProgress, 1000);\n    };\n    const simulateProcessing = ()=>{\n        addLog(\"info\", \"開始模擬處理流程...\");\n        const steps = [\n            \"資料載入\",\n            \"時間計算\",\n            \"資料整合\",\n            \"儲存結果\"\n        ];\n        let currentStep = 0;\n        const interval = setInterval(()=>{\n            setProgress((prev)=>{\n                const newProgress = prev + Math.random() * 15 + 5;\n                if (newProgress >= 100) {\n                    clearInterval(interval);\n                    setProgress(100);\n                    addLog(\"success\", \"模擬處理完成！\");\n                    addLog(\"info\", `成功處理 ${statistics.pendingEmployees} 名員工的考勤資料`);\n                    addLog(\"info\", \"創建: 15, 更新: 8, 失敗: 0\");\n                    setTimeout(()=>{\n                        setIsProcessing(false);\n                        setProgress(0);\n                    }, 3000);\n                    return 100;\n                }\n                // 更新步驟狀態\n                const stepIndex = Math.floor(newProgress / 25);\n                if (stepIndex > currentStep && stepIndex < steps.length) {\n                    addLog(\"info\", `正在執行：${steps[stepIndex]} (${Math.round(newProgress)}%)`);\n                    currentStep = stepIndex;\n                }\n                return newProgress;\n            });\n        }, 300);\n    };\n    const handlePreview = async ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) {\n            alert(\"請選擇日期範圍\");\n            return;\n        }\n        setPreviewLoading(true);\n        try {\n            const previewRequest = {\n                date_range: dateRange,\n                employee_scope: employeeScope\n            };\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/preview\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(previewRequest)\n                });\n                if (!response.ok) {\n                    throw new Error(`預覽請求失敗: ${response.status}`);\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"預覽失敗\");\n                }\n                setPreviewData(result.data);\n                setShowPreview(true);\n            } catch (apiError) {\n                // API調用失敗，使用模擬數據\n                addLog(\"warning\", \"後端API不可用，使用模擬預覽數據\");\n                const mockPreviewData = {\n                    employees: [\n                        {\n                            id: 1,\n                            employee_id: \"E001\",\n                            name: \"黎麗玲\",\n                            department_name: \"技術部\"\n                        },\n                        {\n                            id: 2,\n                            employee_id: \"E002\",\n                            name: \"蔡秀娟\",\n                            department_name: \"技術部\"\n                        },\n                        {\n                            id: 3,\n                            employee_id: \"E003\",\n                            name: \"劉志偉\",\n                            department_name: \"業務部\"\n                        },\n                        {\n                            id: 4,\n                            employee_id: \"admin\",\n                            name: \"系統管理員\",\n                            department_name: \"管理部\"\n                        }\n                    ],\n                    total_employees: 4,\n                    date_range: dateRange,\n                    existing_records: 2,\n                    estimated_new_records: statistics.pendingEmployees - 2\n                };\n                setPreviewData(mockPreviewData);\n                setShowPreview(true);\n            }\n        } catch (error) {\n            addLog(\"error\", `預覽失敗: ${error instanceof Error ? error.message : \"未知錯誤\"}`);\n        } finally{\n            setPreviewLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold mb-2 text-white\",\n                                            children: \"考勤資料整理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/admin\",\n                                                    className: \"inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white group-hover:text-indigo-100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-white group-hover:text-indigo-100\",\n                                                            children: \"返回\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-indigo-100 text-base font-medium\",\n                                                    children: \"自動計算遲到、早退、加班時間，整合請假資料並更新考勤狀態\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-white\",\n                                                    children: \"管理員模式\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"考勤整理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex items-center justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-white\",\n                                        children: \"自動處理模式：\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoMode\",\n                                                checked: autoMode,\n                                                onChange: (e)=>setAutoMode(e.target.checked),\n                                                className: \"sr-only\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoMode\",\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-11 h-6 rounded-full transition-colors duration-200 ${autoMode ? \"bg-green-500\" : \"bg-white/30\"}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-200 ${autoMode ? \"translate-x-5\" : \"translate-x-0.5\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm text-white font-medium\",\n                                                children: autoMode ? \"啟用\" : \"停用\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"待處理天數\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                children: statistics.pendingDays\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"待處理員工\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent\",\n                                                children: statistics.pendingEmployees\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"預估時間\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent\",\n                                                children: statistics.estimatedTime\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"異常記錄\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent\",\n                                                children: statistics.errorRecords\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 495,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                    children: \"日期範圍設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"開始日期\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            value: dateRange.start_date,\n                                                            onChange: (e)=>setDateRange((prev)=>({\n                                                                        ...prev,\n                                                                        start_date: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"結束日期\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            value: dateRange.end_date,\n                                                            onChange: (e)=>setDateRange((prev)=>({\n                                                                        ...prev,\n                                                                        end_date: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"1\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"昨天\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"7\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"最近7天\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"30\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"最近30天\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"current-month\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"本月\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent\",\n                                                    children: \"員工範圍設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"employeeScope\",\n                                                            value: \"all\",\n                                                            checked: employeeScope.type === \"all\",\n                                                            onChange: (e)=>setEmployeeScope({\n                                                                    type: \"all\"\n                                                                }),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"所有員工\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"employeeScope\",\n                                                            value: \"department\",\n                                                            checked: employeeScope.type === \"department\",\n                                                            onChange: (e)=>setEmployeeScope({\n                                                                    type: \"department\",\n                                                                    department_ids: []\n                                                                }),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"指定部門\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"employeeScope\",\n                                                            value: \"specific\",\n                                                            checked: employeeScope.type === \"specific\",\n                                                            onChange: (e)=>setEmployeeScope({\n                                                                    type: \"specific\",\n                                                                    employee_ids: []\n                                                                }),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"指定員工\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent\",\n                                                    children: \"處理選項設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"計算遲到時間\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.calculate_late_early,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        calculate_late_early: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"計算加班時間\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.calculate_overtime,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        calculate_overtime: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"整合請假資料\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.integrate_leaves,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        integrate_leaves: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"覆蓋已存在的計算結果\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.overwrite_existing,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        overwrite_existing: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: startProcessing,\n                                                        disabled: isProcessing,\n                                                        className: `flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 ${isProcessing ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700 shadow-lg hover:shadow-xl transform hover:scale-105\"}`,\n                                                        children: [\n                                                            isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-5 h-5 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 45\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isProcessing ? \"處理中...\" : \"開始處理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handlePreview,\n                                                        disabled: previewLoading,\n                                                        className: `flex items-center space-x-2 px-4 py-3 rounded-xl transition-all duration-200 ${previewLoading ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 hover:from-gray-200 hover:to-gray-300 shadow-sm hover:shadow-md transform hover:scale-105\"}`,\n                                                        children: [\n                                                            previewLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 45\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: previewLoading ? \"載入中...\" : \"預覽資料\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 33\n                                            }, this),\n                                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-32 bg-gray-200 rounded-full h-3 shadow-inner\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-3 rounded-full transition-all duration-300 shadow-sm\",\n                                                            style: {\n                                                                width: `${progress}%`\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            Math.round(progress),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\",\n                                                        children: \"處理日誌\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearLogs,\n                                                className: \"flex items-center space-x-1 px-3 py-1.5 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-600 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"清空\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3 p-3 rounded-lg bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-100 shadow-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-2 h-2 rounded-full mt-2 flex-shrink-0 shadow-sm ${log.type === \"success\" ? \"bg-gradient-to-r from-green-400 to-green-500\" : log.type === \"error\" ? \"bg-gradient-to-r from-red-400 to-red-500\" : log.type === \"warning\" ? \"bg-gradient-to-r from-yellow-400 to-orange-500\" : \"bg-gradient-to-r from-blue-400 to-indigo-500\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: log.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: log.timestamp\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 775,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 546,\n                    columnNumber: 17\n                }, this),\n                showPreview && previewData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center p-4 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden border border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-6 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"處理預覽\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-indigo-100 text-sm mt-1\",\n                                                    children: \"檢視即將處理的考勤資料範圍和統計資訊\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowPreview(false),\n                                            className: \"p-2 hover:bg-white/20 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 41\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 overflow-y-auto\",\n                                style: {\n                                    maxHeight: \"calc(90vh - 140px)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-blue-900 mb-4 flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 836,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"處理範圍預覽\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 835,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-blue-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 font-medium\",\n                                                                    children: \"日期範圍：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold text-blue-900 block mt-1\",\n                                                                    children: [\n                                                                        previewData.date_range.start_date,\n                                                                        \" 至 \",\n                                                                        previewData.date_range.end_date\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 840,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-blue-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 font-medium\",\n                                                                    children: \"員工數量：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold text-blue-900 block mt-1\",\n                                                                    children: [\n                                                                        previewData.total_employees,\n                                                                        \" 人\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-blue-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 font-medium\",\n                                                                    children: \"現有記錄：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 849,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold text-blue-900 block mt-1\",\n                                                                    children: [\n                                                                        previewData.existing_records,\n                                                                        \" 條\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 848,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-blue-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 font-medium\",\n                                                                    children: \"預計新增：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold text-blue-900 block mt-1\",\n                                                                    children: [\n                                                                        previewData.estimated_new_records,\n                                                                        \" 條\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 854,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 852,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 839,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 37\n                                        }, this),\n                                        previewData.employees && previewData.employees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-gray-900 mb-4 flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5 text-indigo-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"員工列表（前10名）\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"overflow-x-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                        className: \"w-full text-sm border border-gray-200 rounded-xl overflow-hidden shadow-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                className: \"bg-gradient-to-r from-indigo-500 to-purple-500 text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left font-bold\",\n                                                                            children: \"員工編號\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                            lineNumber: 870,\n                                                                            columnNumber: 61\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left font-bold\",\n                                                                            children: \"員工姓名\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                            lineNumber: 871,\n                                                                            columnNumber: 61\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left font-bold\",\n                                                                            children: \"部門\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                            lineNumber: 872,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 57\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 53\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                className: \"divide-y divide-gray-200\",\n                                                                children: previewData.employees.map((employee, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"hover:bg-gradient-to-r hover:from-slate-50 hover:to-blue-50 transition-all duration-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 font-medium text-gray-900\",\n                                                                                children: employee.employee_id\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 878,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 font-medium text-gray-900\",\n                                                                                children: employee.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 879,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-gray-700\",\n                                                                                children: employee.department_name || \"未分配\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 880,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 877,\n                                                                        columnNumber: 61\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 41\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-6 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-yellow-900 mb-3 flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 892,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"處理注意事項\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-yellow-800 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-1.5 h-1.5 bg-yellow-600 rounded-full mt-2 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"處理過程中請勿關閉瀏覽器\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-1.5 h-1.5 bg-yellow-600 rounded-full mt-2 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"已計算的資料將被覆蓋（如有勾選覆蓋選項）\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 900,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-1.5 h-1.5 bg-yellow-600 rounded-full mt-2 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"建議在非高峰時段進行大量資料處理\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-1.5 h-1.5 bg-yellow-600 rounded-full mt-2 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 909,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"處理完成後將自動生成處理報告\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 910,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 908,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 832,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 831,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-gray-50 to-slate-50 border-t border-gray-200 p-6 flex justify-end space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPreview(false),\n                                        className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium shadow-sm hover:shadow-md\",\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowPreview(false);\n                                            startProcessing();\n                                        },\n                                        className: \"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105\",\n                                        children: \"確認處理\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 924,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                        lineNumber: 815,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 814,\n                    columnNumber: 21\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n            lineNumber: 440,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n        lineNumber: 438,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/attendance-processing/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PWAInstallPrompt.tsx":
/*!*********************************************!*\
  !*** ./src/components/PWAInstallPrompt.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PWAInstallPrompt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PWAInstallPrompt() {\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPrompt, setShowPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isIOS, setIsIOS] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStandalone, setIsStandalone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 檢測是否為iOS設備\n        const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);\n        setIsIOS(iOS);\n        // 檢測是否已經是獨立模式（已安裝）\n        const standalone = window.matchMedia(\"(display-mode: standalone)\").matches;\n        setIsStandalone(standalone);\n        // 監聽beforeinstallprompt事件\n        const handleBeforeInstallPrompt = (e)=>{\n            e.preventDefault();\n            setDeferredPrompt(e);\n            // 延遲顯示提示，讓用戶先體驗應用\n            setTimeout(()=>{\n                if (!standalone) {\n                    setShowPrompt(true);\n                }\n            }, 3000);\n        };\n        // 監聽應用安裝事件\n        const handleAppInstalled = ()=>{\n            setShowPrompt(false);\n            setDeferredPrompt(null);\n            console.log(\"PWA 已安裝\");\n        };\n        window.addEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n        window.addEventListener(\"appinstalled\", handleAppInstalled);\n        return ()=>{\n            window.removeEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n            window.removeEventListener(\"appinstalled\", handleAppInstalled);\n        };\n    }, []);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        try {\n            await deferredPrompt.prompt();\n            const { outcome } = await deferredPrompt.userChoice;\n            if (outcome === \"accepted\") {\n                console.log(\"用戶接受安裝\");\n            } else {\n                console.log(\"用戶拒絕安裝\");\n            }\n            setDeferredPrompt(null);\n            setShowPrompt(false);\n        } catch (error) {\n            console.error(\"安裝過程中發生錯誤:\", error);\n        }\n    };\n    const handleDismiss = ()=>{\n        setShowPrompt(false);\n        // 24小時後再次顯示\n        if (false) {}\n    };\n    // 如果已經是獨立模式，不顯示提示\n    if (isStandalone) return null;\n    // 檢查是否在24小時內被拒絕過（僅在客戶端執行）\n    if (false) {}\n    // iOS設備顯示手動安裝指引\n    if (isIOS && showPrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-4 left-4 right-4 z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-w-sm mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold text-gray-900 mb-1\",\n                                children: \"安裝考勤系統\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-3\",\n                                children: \"點擊 Safari 底部的分享按鈕 \\uD83D\\uDCE4，然後選擇「加入主畫面」\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleDismiss,\n                                    className: \"px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 transition-colors\",\n                                    children: \"稍後再說\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDismiss,\n                        className: \"w-6 h-6 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                lineNumber: 102,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n            lineNumber: 101,\n            columnNumber: 13\n        }, this);\n    }\n    // Android/Desktop 顯示安裝按鈕\n    if (deferredPrompt && showPrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-4 left-4 right-4 z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-w-sm mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold text-gray-900 mb-1\",\n                                children: \"安裝考勤系統\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-3\",\n                                children: \"安裝到您的設備，享受更好的使用體驗\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleInstallClick,\n                                        className: \"flex items-center space-x-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-indigo-600 hover:to-purple-700 transition-all duration-200 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"安裝\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDismiss,\n                                        className: \"px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors\",\n                                        children: \"稍後再說\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDismiss,\n                        className: \"w-6 h-6 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                lineNumber: 135,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n            lineNumber: 134,\n            columnNumber: 13\n        }, this);\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PWAInstallPrompt.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ServiceWorkerRegistration.tsx":
/*!******************************************************!*\
  !*** ./src/components/ServiceWorkerRegistration.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServiceWorkerRegistration)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ServiceWorkerRegistration() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (\"serviceWorker\" in navigator) {\n            navigator.serviceWorker.register(\"/sw.js\").then((registration)=>{\n                console.log(\"Service Worker 註冊成功:\", registration.scope);\n                // 檢查更新\n                registration.addEventListener(\"updatefound\", ()=>{\n                    const newWorker = registration.installing;\n                    if (newWorker) {\n                        newWorker.addEventListener(\"statechange\", ()=>{\n                            if (newWorker.state === \"installed\" && navigator.serviceWorker.controller) {\n                                // 有新版本可用\n                                console.log(\"新版本可用，建議重新載入頁面\");\n                                // 可以在這裡顯示更新提示\n                                if (confirm(\"發現新版本，是否立即更新？\")) {\n                                    newWorker.postMessage({\n                                        type: \"SKIP_WAITING\"\n                                    });\n                                    window.location.reload();\n                                }\n                            }\n                        });\n                    }\n                });\n            }).catch((error)=>{\n                console.error(\"Service Worker 註冊失敗:\", error);\n            });\n            // 監聽Service Worker控制權變更\n            navigator.serviceWorker.addEventListener(\"controllerchange\", ()=>{\n                console.log(\"Service Worker 控制權已變更\");\n                window.location.reload();\n            });\n        } else {\n            console.log(\"此瀏覽器不支援 Service Worker\");\n        }\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ServiceWorkerRegistration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 確保組件已掛載，避免 hydration 錯誤\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 檢查本地存儲中的用戶資訊\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return; // 只有在組件掛載後才執行\n        const initAuth = async ()=>{\n            try {\n                console.log(\"開始認證初始化\");\n                // 首先檢查 localStorage\n                const storedUser = localStorage.getItem(\"user\");\n                console.log(\"本地存儲的用戶資料:\", storedUser);\n                if (storedUser) {\n                    const userData = JSON.parse(storedUser);\n                    console.log(\"解析的用戶資料:\", userData);\n                    setUser(userData);\n                    // 簡化驗證 - 暫時跳過會話驗證，專注解決跳轉問題\n                    console.log(\"用戶已設定，跳過會話驗證\");\n                } else {\n                    console.log(\"沒有本地用戶資料\");\n                }\n            } catch (error) {\n                console.error(\"認證初始化失敗:\", error);\n                if (false) {}\n                setUser(null);\n            } finally{\n                console.log(\"認證初始化完成，設定 loading = false\");\n                setLoading(false);\n            }\n        };\n        initAuth();\n    }, [\n        mounted\n    ]);\n    const login = (userData)=>{\n        console.log(\"AuthContext login 被調用:\", userData);\n        setUser(userData);\n        if (false) {}\n        console.log(\"AuthContext 用戶狀態已更新\");\n    };\n    const logout = ()=>{\n        setUser(null);\n        if (false) {}\n        router.push(\"/login\");\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        isAuthenticated: !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// 保護路由的 HOC\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { isAuthenticated, loading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        console.log(\"withAuth 檢查:\", {\n            loading,\n            isAuthenticated\n        });\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!loading && !isAuthenticated) {\n                console.log(\"withAuth: 未認證，導向登入頁面\");\n                router.push(\"/login\");\n            }\n        }, [\n            isAuthenticated,\n            loading,\n            router\n        ]);\n        if (loading) {\n            console.log(\"withAuth: 載入中\");\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            console.log(\"withAuth: 未認證，返回 null\");\n            return null;\n        }\n        console.log(\"withAuth: 已認證，渲染組件\");\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n            lineNumber: 137,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"17a5a9779ec4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXR0ZW5kYW5jZS1uZXh0anMvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzA3YWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxN2E1YTk3NzllYzRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/attendance-processing/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/admin/attendance-processing/page.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_PWAInstallPrompt__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/PWAInstallPrompt */ \"(rsc)/./src/components/PWAInstallPrompt.tsx\");\n/* harmony import */ var _components_ServiceWorkerRegistration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ServiceWorkerRegistration */ \"(rsc)/./src/components/ServiceWorkerRegistration.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Han AttendanceOS\",\n    description: \"遠漢科技考勤管理系統 - 智能打卡、請假申請、工作回報\",\n    applicationName: \"Han AttendanceOS\",\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: \"default\",\n        title: \"考勤系統\"\n    },\n    formatDetection: {\n        telephone: false\n    },\n    manifest: \"/manifest.json\",\n    icons: {\n        icon: [\n            {\n                url: \"/favicon.ico\",\n                type: \"image/x-icon\",\n                sizes: \"16x16\"\n            },\n            {\n                url: \"/icon-32x32.png\",\n                sizes: \"32x32\",\n                type: \"image/png\"\n            },\n            {\n                url: \"/icon-16x16.png\",\n                sizes: \"16x16\",\n                type: \"image/png\"\n            }\n        ],\n        shortcut: \"/favicon.ico\",\n        apple: \"/icons/apple-touch-icon.png\"\n    },\n    other: {\n        \"mobile-web-app-capable\": \"yes\",\n        \"msapplication-TileColor\": \"#4f46e5\",\n        \"msapplication-TileImage\": \"/icons/icon-144x144.png\",\n        \"msapplication-config\": \"/browserconfig.xml\"\n    }\n};\n// 將 themeColor 移到 viewport 配置\nconst viewport = {\n    themeColor: \"#4f46e5\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-TW\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PWAInstallPrompt__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ServiceWorkerRegistration__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBT01BO0FBTGdCO0FBQytCO0FBQ087QUFDa0I7QUFJdkUsTUFBTUksV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxpQkFBaUI7SUFDakJDLGFBQWE7UUFDWEMsU0FBUztRQUNUQyxnQkFBZ0I7UUFDaEJMLE9BQU87SUFDVDtJQUNBTSxpQkFBaUI7UUFDZkMsV0FBVztJQUNiO0lBQ0FDLFVBQVU7SUFDVkMsT0FBTztRQUNMQyxNQUFNO1lBQ0o7Z0JBQUVDLEtBQUs7Z0JBQWdCQyxNQUFNO2dCQUFnQkMsT0FBTztZQUFRO1lBQzVEO2dCQUFFRixLQUFLO2dCQUFtQkUsT0FBTztnQkFBU0QsTUFBTTtZQUFZO1lBQzVEO2dCQUFFRCxLQUFLO2dCQUFtQkUsT0FBTztnQkFBU0QsTUFBTTtZQUFZO1NBQzdEO1FBQ0RFLFVBQVU7UUFDVkMsT0FBTztJQUNUO0lBQ0FDLE9BQU87UUFDTCwwQkFBMEI7UUFDMUIsMkJBQTJCO1FBQzNCLDJCQUEyQjtRQUMzQix3QkFBd0I7SUFDMUI7QUFDRixFQUFDO0FBRUQsOEJBQThCO0FBQ3ZCLE1BQU1DLFdBQVc7SUFDdEJDLFlBQVk7QUFDZCxFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVc3QiwrSkFBZTtzQkFDOUIsNEVBQUNDLCtEQUFZQTs7b0JBQ1Z3QjtrQ0FDRCw4REFBQ3ZCLG9FQUFnQkE7Ozs7O2tDQUNqQiw4REFBQ0MsNkVBQXlCQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3BDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXR0ZW5kYW5jZS1uZXh0anMvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnXG5pbXBvcnQgUFdBSW5zdGFsbFByb21wdCBmcm9tICdAL2NvbXBvbmVudHMvUFdBSW5zdGFsbFByb21wdCdcbmltcG9ydCBTZXJ2aWNlV29ya2VyUmVnaXN0cmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9TZXJ2aWNlV29ya2VyUmVnaXN0cmF0aW9uJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnSGFuIEF0dGVuZGFuY2VPUycsXG4gIGRlc2NyaXB0aW9uOiAn6YGg5ryi56eR5oqA6ICD5Yuk566h55CG57O757WxIC0g5pm66IO95omT5Y2h44CB6KuL5YGH55Sz6KuL44CB5bel5L2c5Zue5aCxJyxcbiAgYXBwbGljYXRpb25OYW1lOiAnSGFuIEF0dGVuZGFuY2VPUycsXG4gIGFwcGxlV2ViQXBwOiB7XG4gICAgY2FwYWJsZTogdHJ1ZSxcbiAgICBzdGF0dXNCYXJTdHlsZTogJ2RlZmF1bHQnLFxuICAgIHRpdGxlOiAn6ICD5Yuk57O757WxJyxcbiAgfSxcbiAgZm9ybWF0RGV0ZWN0aW9uOiB7XG4gICAgdGVsZXBob25lOiBmYWxzZSxcbiAgfSxcbiAgbWFuaWZlc3Q6ICcvbWFuaWZlc3QuanNvbicsXG4gIGljb25zOiB7XG4gICAgaWNvbjogW1xuICAgICAgeyB1cmw6ICcvZmF2aWNvbi5pY28nLCB0eXBlOiAnaW1hZ2UveC1pY29uJywgc2l6ZXM6ICcxNngxNicgfSxcbiAgICAgIHsgdXJsOiAnL2ljb24tMzJ4MzIucG5nJywgc2l6ZXM6ICczMngzMicsIHR5cGU6ICdpbWFnZS9wbmcnIH0sXG4gICAgICB7IHVybDogJy9pY29uLTE2eDE2LnBuZycsIHNpemVzOiAnMTZ4MTYnLCB0eXBlOiAnaW1hZ2UvcG5nJyB9LFxuICAgIF0sXG4gICAgc2hvcnRjdXQ6ICcvZmF2aWNvbi5pY28nLFxuICAgIGFwcGxlOiAnL2ljb25zL2FwcGxlLXRvdWNoLWljb24ucG5nJyxcbiAgfSxcbiAgb3RoZXI6IHtcbiAgICAnbW9iaWxlLXdlYi1hcHAtY2FwYWJsZSc6ICd5ZXMnLFxuICAgICdtc2FwcGxpY2F0aW9uLVRpbGVDb2xvcic6ICcjNGY0NmU1JyxcbiAgICAnbXNhcHBsaWNhdGlvbi1UaWxlSW1hZ2UnOiAnL2ljb25zL2ljb24tMTQ0eDE0NC5wbmcnLFxuICAgICdtc2FwcGxpY2F0aW9uLWNvbmZpZyc6ICcvYnJvd3NlcmNvbmZpZy54bWwnLFxuICB9LFxufVxuXG4vLyDlsIcgdGhlbWVDb2xvciDnp7vliLAgdmlld3BvcnQg6YWN572uXG5leHBvcnQgY29uc3Qgdmlld3BvcnQgPSB7XG4gIHRoZW1lQ29sb3I6ICcjNGY0NmU1Jyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInpoLVRXXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDxQV0FJbnN0YWxsUHJvbXB0IC8+XG4gICAgICAgICAgPFNlcnZpY2VXb3JrZXJSZWdpc3RyYXRpb24gLz5cbiAgICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJBdXRoUHJvdmlkZXIiLCJQV0FJbnN0YWxsUHJvbXB0IiwiU2VydmljZVdvcmtlclJlZ2lzdHJhdGlvbiIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImFwcGxpY2F0aW9uTmFtZSIsImFwcGxlV2ViQXBwIiwiY2FwYWJsZSIsInN0YXR1c0JhclN0eWxlIiwiZm9ybWF0RGV0ZWN0aW9uIiwidGVsZXBob25lIiwibWFuaWZlc3QiLCJpY29ucyIsImljb24iLCJ1cmwiLCJ0eXBlIiwic2l6ZXMiLCJzaG9ydGN1dCIsImFwcGxlIiwib3RoZXIiLCJ2aWV3cG9ydCIsInRoZW1lQ29sb3IiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/PWAInstallPrompt.tsx":
/*!*********************************************!*\
  !*** ./src/components/PWAInstallPrompt.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ServiceWorkerRegistration.tsx":
/*!******************************************************!*\
  !*** ./src/components/ServiceWorkerRegistration.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/components/ServiceWorkerRegistration.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx#withAuth`);


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hdHRlbmRhbmNlLW5leHRqcy8uL3NyYy9hcHAvZmF2aWNvbi5pY28/NGQ1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fattendance-processing%2Fpage&page=%2Fadmin%2Fattendance-processing%2Fpage&appPaths=%2Fadmin%2Fattendance-processing%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fattendance-processing%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();