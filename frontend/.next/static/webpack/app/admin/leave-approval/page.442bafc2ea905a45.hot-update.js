"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/leave-approval/page",{

/***/ "(app-pages-browser)/./src/app/admin/leave-approval/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/admin/leave-approval/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeaveApprovalPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LeaveApprovalPage() {\n    _s();\n    const { user, login: authLogin, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [leaveRequests, setLeaveRequests] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dataLoading, setDataLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [autoLoginLoading, setAutoLoginLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedRequest, setSelectedRequest] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showApprovalModal, setShowApprovalModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [approvalAction, setApprovalAction] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"approve\");\n    const [approvalComment, setApprovalComment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [searchParams, setSearchParams] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        employee_name: \"\",\n        status: \"\",\n        leave_type: \"\"\n    });\n    const [filteredRequests, setFilteredRequests] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        pending: 0,\n        todayNew: 0,\n        approved: 0,\n        rejected: 0\n    });\n    // 請假類型對應\n    const leaveTypeMap = {\n        annual: \"年假\",\n        sick: \"病假\",\n        personal: \"事假\",\n        maternity: \"產假\",\n        paternity: \"陪產假\",\n        marriage: \"婚假\",\n        funeral: \"喪假\",\n        compensatory: \"補休\",\n        other: \"其他\"\n    };\n    // 自動登入功能 - 使用管理員測試帳號\n    const handleAutoLogin = async ()=>{\n        setAutoLoginLoading(true);\n        try {\n            console.log(\"開始自動登入管理員帳號\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_6__.login)({\n                employee_id: \"admin\",\n                password: \"admin123\"\n            });\n            if (response && response.user) {\n                const userData = response.user;\n                const user = {\n                    id: userData.employee_id,\n                    name: userData.employee_name,\n                    employee_id: userData.employee_code,\n                    department_id: userData.department_id,\n                    position: userData.role_id === 999 ? \"系統管理員\" : \"員工\",\n                    email: userData.email,\n                    role_id: userData.role_id,\n                    department_name: userData.department_name\n                };\n                authLogin(user);\n                console.log(\"自動登入成功:\", user);\n            } else {\n                console.error(\"自動登入失敗:\", response);\n            }\n        } catch (error) {\n            console.error(\"自動登入錯誤:\", error);\n        } finally{\n            setAutoLoginLoading(false);\n        }\n    };\n    // 頁面載入時自動登入（如果未登入）\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!loading && !user) {\n            console.log(\"檢測到未登入，執行自動登入\");\n            handleAutoLogin();\n        }\n    }, [\n        loading,\n        user\n    ]);\n    // 載入真實的請假申請數據\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchLeaveRequests = async ()=>{\n            try {\n                setDataLoading(true);\n                const apiBaseUrl = window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\" ? \"http://localhost:7072\" : \"http://\".concat(window.location.hostname, \":7072\");\n                const response = await fetch(\"\".concat(apiBaseUrl, \"/api/leave-requests\"));\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch leave requests\");\n                }\n                const data = await response.json();\n                const rawRequests = data.records || [];\n                // 映射 API 數據到前端期望的格式\n                const requests = rawRequests.map((item)=>{\n                    // 安全的日期計算\n                    let daysCount = 1;\n                    try {\n                        const startDate = new Date(item.start_date);\n                        const endDate = new Date(item.end_date);\n                        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {\n                            daysCount = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n                        }\n                    } catch (error) {\n                        console.warn(\"日期計算錯誤:\", item.start_date, item.end_date, error);\n                    }\n                    // 處理附件數據\n                    let attachments = [];\n                    if (item.attachments) {\n                        try {\n                            attachments = typeof item.attachments === \"string\" ? JSON.parse(item.attachments) : item.attachments;\n                        } catch (error) {\n                            console.warn(\"解析附件數據失敗:\", item.attachments, error);\n                            attachments = [];\n                        }\n                    }\n                    return {\n                        id: item.id,\n                        employee_id: item.employee_code || item.employee_id || \"\",\n                        employee_name: item.employee_name || \"未知員工\",\n                        department_name: item.department_name || \"未知部門\",\n                        leave_type: item.leave_type,\n                        start_date: item.start_date,\n                        end_date: item.end_date,\n                        days_count: daysCount,\n                        reason: item.reason || \"無\",\n                        status: item.status,\n                        submitted_at: item.created_at,\n                        created_at: item.created_at,\n                        approved_by: item.approver_name,\n                        approved_at: item.approved_at,\n                        rejection_reason: item.comment,\n                        emergency_contact: item.emergency_contact || \"無\",\n                        substitute_id: item.substitute_id,\n                        substitute_name: item.substitute_name || \"無\",\n                        attachments: attachments\n                    };\n                });\n                console.log(\"API 原始數據筆數:\", rawRequests.length);\n                console.log(\"映射後數據筆數:\", requests.length);\n                console.log(\"前3筆映射後的數據:\", requests.slice(0, 3));\n                setLeaveRequests(requests);\n                setFilteredRequests(requests);\n                // 計算統計數據\n                const newStats = {\n                    pending: requests.filter((r)=>r.status === \"pending\").length,\n                    todayNew: requests.filter((r)=>new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()).length,\n                    approved: requests.filter((r)=>r.status === \"approved\").length,\n                    rejected: requests.filter((r)=>r.status === \"rejected\").length\n                };\n                setStats(newStats);\n            } catch (error) {\n                console.error(\"Error fetching leave requests:\", error);\n                // 如果API失敗，設置空數據\n                setLeaveRequests([]);\n                setFilteredRequests([]);\n                setStats({\n                    pending: 0,\n                    todayNew: 0,\n                    approved: 0,\n                    rejected: 0\n                });\n            } finally{\n                setDataLoading(false);\n            }\n        };\n        fetchLeaveRequests();\n    }, []);\n    // 搜索和篩選\n    const handleSearch = ()=>{\n        let filtered = leaveRequests;\n        if (searchParams.employee_name) {\n            filtered = filtered.filter((request)=>{\n                var _request_employee_name;\n                return (_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.toLowerCase().includes(searchParams.employee_name.toLowerCase());\n            });\n        }\n        if (searchParams.status) {\n            filtered = filtered.filter((request)=>request.status === searchParams.status);\n        }\n        if (searchParams.leave_type) {\n            filtered = filtered.filter((request)=>request.leave_type === searchParams.leave_type);\n        }\n        setFilteredRequests(filtered);\n    };\n    const handleReset = ()=>{\n        setSearchParams({\n            employee_name: \"\",\n            status: \"\",\n            leave_type: \"\"\n        });\n        setFilteredRequests(leaveRequests);\n    };\n    // 處理審核\n    const handleApproval = async ()=>{\n        if (!selectedRequest) return;\n        try {\n            // 調用真實的API\n            const apiBaseUrl = window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\" ? \"http://localhost:7072\" : \"http://\".concat(window.location.hostname, \":7072\");\n            const response = await fetch(\"\".concat(apiBaseUrl, \"/api/approval/leaves/\").concat(selectedRequest.id), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: approvalAction,\n                    comment: approvalAction === \"reject\" ? approvalComment : undefined\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update leave request\");\n            }\n            // 重新載入數據\n            const fetchResponse = await fetch(\"\".concat(apiBaseUrl, \"/api/leave-requests\"));\n            if (fetchResponse.ok) {\n                const data = await fetchResponse.json();\n                const rawRequests = data.records || [];\n                // 映射 API 數據到前端期望的格式\n                const requests = rawRequests.map((item)=>{\n                    // 安全的日期計算\n                    let daysCount = 1;\n                    try {\n                        const startDate = new Date(item.start_date);\n                        const endDate = new Date(item.end_date);\n                        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {\n                            daysCount = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n                        }\n                    } catch (error) {\n                        console.warn(\"日期計算錯誤:\", item.start_date, item.end_date, error);\n                    }\n                    // 處理附件數據\n                    let attachments = [];\n                    if (item.attachments) {\n                        try {\n                            attachments = typeof item.attachments === \"string\" ? JSON.parse(item.attachments) : item.attachments;\n                        } catch (error) {\n                            console.warn(\"解析附件數據失敗:\", item.attachments, error);\n                            attachments = [];\n                        }\n                    }\n                    return {\n                        id: item.id,\n                        employee_id: item.employee_code || item.employee_id || \"\",\n                        employee_name: item.employee_name || \"未知員工\",\n                        department_name: item.department_name || \"未知部門\",\n                        leave_type: item.leave_type,\n                        start_date: item.start_date,\n                        end_date: item.end_date,\n                        days_count: daysCount,\n                        reason: item.reason || \"無\",\n                        status: item.status,\n                        submitted_at: item.created_at,\n                        created_at: item.created_at,\n                        approved_by: item.approver_name,\n                        approved_at: item.approved_at,\n                        rejection_reason: item.comment,\n                        emergency_contact: item.emergency_contact || \"無\",\n                        substitute_id: item.substitute_id,\n                        substitute_name: item.substitute_name || \"無\",\n                        attachments: attachments\n                    };\n                });\n                setLeaveRequests(requests);\n                setFilteredRequests(requests.filter((request)=>{\n                    let match = true;\n                    if (searchParams.employee_name) {\n                        var _request_employee_name;\n                        match = match && ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.toLowerCase().includes(searchParams.employee_name.toLowerCase()));\n                    }\n                    if (searchParams.status) {\n                        match = match && request.status === searchParams.status;\n                    }\n                    if (searchParams.leave_type) {\n                        match = match && request.leave_type === searchParams.leave_type;\n                    }\n                    return match;\n                }));\n                // 更新統計\n                const newStats = {\n                    pending: requests.filter((r)=>r.status === \"pending\").length,\n                    todayNew: requests.filter((r)=>new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()).length,\n                    approved: requests.filter((r)=>r.status === \"approved\").length,\n                    rejected: requests.filter((r)=>r.status === \"rejected\").length\n                };\n                setStats(newStats);\n            }\n            setShowApprovalModal(false);\n            setSelectedRequest(null);\n            setApprovalComment(\"\");\n        } catch (error) {\n            console.error(\"Error updating leave request:\", error);\n            alert(\"審核失敗，請稍後再試\");\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"text-warning-600 bg-warning-50 border-warning-200\";\n            case \"approved\":\n                return \"text-success-600 bg-success-50 border-success-200\";\n            case \"rejected\":\n                return \"text-error-600 bg-error-50 border-error-200\";\n            default:\n                return \"text-neutral-600 bg-neutral-50 border-neutral-200\";\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"待審核\";\n            case \"approved\":\n                return \"已核准\";\n            case \"rejected\":\n                return \"已拒絕\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 30\n                }, this);\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 31\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 31\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    if (loading || autoLoginLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: autoLoginLoading ? \"自動登入中...\" : \"載入中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 403,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n            lineNumber: 402,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user || user.role_id !== 999) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"權限不足\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"您需要管理員權限才能訪問此頁面\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>router.push(\"/admin\"),\n                        children: \"返回管理後台\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 416,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n            lineNumber: 415,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold mb-2 text-white\",\n                                    children: \"請假審核管理\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            href: \"/admin\",\n                                            className: \"inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white group-hover:text-indigo-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-white group-hover:text-indigo-100\",\n                                                    children: \"返回\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-base font-medium\",\n                                            children: \"管理待審核的請假申請與其他審核事項\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-white\",\n                                            children: \"管理員模式\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-indigo-100\",\n                                            children: \"請假審核\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 432,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: \"請假審核管理中心\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600\",\n                                children: \"處理員工請假申請，確保工作流程順暢\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-warning-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.pending\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"待審核\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.todayNew\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"今日新增\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6 text-success-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.approved\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"已核准\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-error-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-error-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.rejected\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"已拒絕\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-purple-500/5 to-pink-500/5 px-8 py-6 border-b border-gray-100/60\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-gray-900\",\n                                                            children: \"篩選條件\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 mt-1\",\n                                                            children: \"快速找到特定的請假申請\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: ()=>window.location.reload(),\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"重新載入\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"匯出報表\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-4 h-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"員工姓名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: searchParams.employee_name,\n                                                                onChange: (e)=>setSearchParams({\n                                                                        ...searchParams,\n                                                                        employee_name: e.target.value\n                                                                    }),\n                                                                placeholder: \"搜尋員工姓名...\",\n                                                                className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 pl-11 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-4 h-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"審核狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: searchParams.status,\n                                                        onChange: (e)=>setSearchParams({\n                                                                ...searchParams,\n                                                                status: e.target.value\n                                                            }),\n                                                        className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"所有狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"pending\",\n                                                                children: \"待審核\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"approved\",\n                                                                children: \"已核准\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"rejected\",\n                                                                children: \"已拒絕\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4 text-purple-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"請假類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: searchParams.leave_type,\n                                                        onChange: (e)=>setSearchParams({\n                                                                ...searchParams,\n                                                                leave_type: e.target.value\n                                                            }),\n                                                        className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"所有類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            Object.entries(leaveTypeMap).map((param)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: key,\n                                                                    children: value\n                                                                }, key, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 21\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between pt-6 border-t border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"primary\",\n                                                        onClick: handleSearch,\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"查詢\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"重置\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"共找到 \",\n                                                    filteredRequests.length,\n                                                    \" 筆申請\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 555,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-green-500/5 to-blue-500/5 px-8 py-6 border-b border-gray-100/60\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"請假申請列表\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: \"管理員工的請假申請\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 11\n                            }, this),\n                            dataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-16\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"載入請假申請中...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 13\n                            }, this) : filteredRequests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:block overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gradient-to-r from-green-500 to-blue-600 text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"申請人\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"請假類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"請假日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"天數\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"申請時間\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"操作\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: filteredRequests.map((request)=>{\n                                                        var _request_employee_name;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-white font-medium text-sm\",\n                                                                                    children: ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.charAt(0)) || \"?\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 681,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 680,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: request.employee_name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 686,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: request.employee_id\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 687,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-400\",\n                                                                                        children: request.department_name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 688,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 685,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-600 border border-blue-200\",\n                                                                        children: leaveTypeMap[request.leave_type] || request.leave_type || \"未知\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 693,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: new Date(request.start_date).toLocaleDateString(\"zh-TW\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        request.start_date !== request.end_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"至 \",\n                                                                                new Date(request.end_date).toLocaleDateString(\"zh-TW\")\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        request.days_count,\n                                                                        \" 天\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 705,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border \".concat(getStatusColor(request.status)),\n                                                                        children: [\n                                                                            getStatusIcon(request.status),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: getStatusText(request.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 711,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 709,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        new Date(request.created_at || request.submitted_at).toLocaleDateString(\"zh-TW\"),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: new Date(request.created_at || request.submitted_at).toLocaleTimeString(\"zh-TW\", {\n                                                                                hour: \"2-digit\",\n                                                                                minute: \"2-digit\"\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 716,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm font-medium\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>setSelectedRequest(request),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 730,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 725,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            request.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedRequest(request);\n                                                                                            setApprovalAction(\"approve\");\n                                                                                            setShowApprovalModal(true);\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-green-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                            lineNumber: 743,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 734,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedRequest(request);\n                                                                                            setApprovalAction(\"reject\");\n                                                                                            setShowApprovalModal(true);\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-red-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                            lineNumber: 754,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 745,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 724,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 723,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, request.id, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:hidden space-y-4 p-6\",\n                                        children: filteredRequests.map((request)=>{\n                                            var _request_employee_name;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-2xl p-6 border border-gray-200 shadow-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-medium text-sm\",\n                                                                            children: ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.charAt(0)) || \"?\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 773,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 772,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900\",\n                                                                                children: request.employee_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 778,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: request.department_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 779,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 777,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border \".concat(getStatusColor(request.status)),\n                                                                children: [\n                                                                    getStatusIcon(request.status),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-1\",\n                                                                        children: getStatusText(request.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 784,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-sm mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"類型:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 790,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: leaveTypeMap[request.leave_type] || request.leave_type || \"未知\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"天數:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 794,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: [\n                                                                            request.days_count,\n                                                                            \" 天\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"開始:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 798,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: new Date(request.start_date).toLocaleDateString(\"zh-TW\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 799,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"結束:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 804,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: new Date(request.end_date).toLocaleDateString(\"zh-TW\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 805,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: \"原因:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-900 text-sm mt-1\",\n                                                                children: request.reason\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 813,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"申請於 \",\n                                                                    new Date(request.created_at || request.submitted_at).toLocaleDateString(\"zh-TW\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>setSelectedRequest(request),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 826,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 821,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    request.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedRequest(request);\n                                                                                    setApprovalAction(\"approve\");\n                                                                                    setShowApprovalModal(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-green-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 839,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 830,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedRequest(request);\n                                                                                    setApprovalAction(\"reject\");\n                                                                                    setShowApprovalModal(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-red-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 850,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 841,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 820,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, request.id, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"查無請假申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"請調整搜尋條件或等待新的申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 861,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 461,\n                columnNumber: 7\n            }, this),\n            showApprovalModal && selectedRequest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-3xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: approvalAction === \"approve\" ? \"核准請假申請\" : \"拒絕請假申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowApprovalModal(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 875,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"申請詳情\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 890,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"申請人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 892,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"部門:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.department_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 896,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假類型:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: leaveTypeMap[selectedRequest.leave_type] || selectedRequest.leave_type || \"未知\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假天數:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: [\n                                                            selectedRequest.days_count,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 906,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"開始日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.start_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 910,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 908,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"結束日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.end_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedRequest.substitute_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"代理人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.substitute_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"緊急聯絡:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.emergency_contact\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 928,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 891,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"請假原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900 mt-1\",\n                                                children: selectedRequest.reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 889,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: approvalAction === \"approve\" ? \"核准意見（可選）\" : \"拒絕原因（必填）\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 939,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: approvalComment,\n                                        onChange: (e)=>setApprovalComment(e.target.value),\n                                        rows: 4,\n                                        className: \"w-full px-3 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none\",\n                                        placeholder: approvalAction === \"approve\" ? \"輸入核准意見...\" : \"請說明拒絕原因...\",\n                                        required: approvalAction === \"reject\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 938,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setShowApprovalModal(false),\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: approvalAction === \"approve\" ? \"success\" : \"error\",\n                                        onClick: handleApproval,\n                                        disabled: approvalAction === \"reject\" && !approvalComment.trim(),\n                                        className: \"flex items-center space-x-2\",\n                                        children: approvalAction === \"approve\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 972,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"核准申請\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 973,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 977,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"拒絕申請\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 978,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 957,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 874,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 873,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 872,\n                columnNumber: 9\n            }, this),\n            selectedRequest && !showApprovalModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-3xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"請假申請詳情\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 994,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedRequest(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 1000,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 993,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"申請人資訊\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"姓名:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"員工編號:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_id\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1014,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1012,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"部門:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1017,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.department_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1016,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"緊急聯絡:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1021,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.emergency_contact\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1022,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1020,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1007,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1005,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"請假資訊\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假類型:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: leaveTypeMap[selectedRequest.leave_type] || selectedRequest.leave_type || \"未知\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假天數:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: [\n                                                            selectedRequest.days_count,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1037,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"開始日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1040,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.start_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1041,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"結束日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.end_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedRequest.substitute_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"代理人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1053,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.substitute_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1054,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1052,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1030,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"請假原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1059,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900 mt-1\",\n                                                children: selectedRequest.reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1060,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1058,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1028,\n                                columnNumber: 15\n                            }, this),\n                            selectedRequest.attachments && selectedRequest.attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5 text-purple-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1068,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"請假文件附件\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1067,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: selectedRequest.attachments.map((attachment, index)=>{\n                                            var _fileName_split_pop;\n                                            const fileName = attachment.split(\"/\").pop() || \"附件\".concat(index + 1);\n                                            const fileExtension = ((_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase()) || \"\";\n                                            // 判斷文件類型圖標\n                                            let fileIcon = \"\\uD83D\\uDCC4\";\n                                            if ([\n                                                \"jpg\",\n                                                \"jpeg\",\n                                                \"png\",\n                                                \"gif\",\n                                                \"webp\"\n                                            ].includes(fileExtension)) {\n                                                fileIcon = \"\\uD83D\\uDDBC️\";\n                                            } else if (fileExtension === \"pdf\") {\n                                                fileIcon = \"\\uD83D\\uDCD5\";\n                                            } else if ([\n                                                \"doc\",\n                                                \"docx\"\n                                            ].includes(fileExtension)) {\n                                                fileIcon = \"\\uD83D\\uDCDD\";\n                                            }\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between bg-white rounded-lg p-4 border border-purple-200 hover:border-purple-300 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl\",\n                                                                children: fileIcon\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 1089,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: fileName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 1091,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"請假證明文件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 1092,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 1090,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>window.open(\"http://localhost:7072\".concat(attachment), \"_blank\"),\n                                                                className: \"text-blue-600 hover:text-blue-700 hover:bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 1102,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    \"查看\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 1096,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    const link = document.createElement(\"a\");\n                                                                    link.href = \"http://localhost:7072\".concat(attachment);\n                                                                    link.download = fileName;\n                                                                    document.body.appendChild(link);\n                                                                    link.click();\n                                                                    document.body.removeChild(link);\n                                                                },\n                                                                className: \"text-green-600 hover:text-green-700 hover:bg-green-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 1118,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    \"下載\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 1105,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1095,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1087,\n                                                columnNumber: 25\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1071,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1066,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-2xl p-6 mb-6 \".concat(selectedRequest.status === \"approved\" ? \"bg-green-50\" : selectedRequest.status === \"rejected\" ? \"bg-red-50\" : \"bg-yellow-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"審核狀態\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1133,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border \".concat(getStatusColor(selectedRequest.status)),\n                                                children: [\n                                                    getStatusIcon(selectedRequest.status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1\",\n                                                        children: getStatusText(selectedRequest.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1137,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1135,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"申請於 \",\n                                                    new Date(selectedRequest.created_at || selectedRequest.submitted_at).toLocaleString(\"zh-TW\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1139,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1134,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedRequest.approved_by && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"審核人:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1145,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-gray-900\",\n                                                children: selectedRequest.approved_by\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1146,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-4 text-gray-500\",\n                                                children: \"審核時間:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1147,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-gray-900\",\n                                                children: selectedRequest.approved_at && new Date(selectedRequest.approved_at).toLocaleString(\"zh-TW\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1148,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1144,\n                                        columnNumber: 19\n                                    }, this),\n                                    selectedRequest.rejection_reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 p-3 bg-red-100 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-700 text-sm font-medium\",\n                                                children: \"拒絕原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1155,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-800 text-sm mt-1\",\n                                                children: selectedRequest.rejection_reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1156,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1154,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1130,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end space-x-4\",\n                                children: [\n                                    selectedRequest.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"success\",\n                                                onClick: ()=>{\n                                                    setApprovalAction(\"approve\");\n                                                    setShowApprovalModal(true);\n                                                },\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1173,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"核准\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1174,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1165,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"error\",\n                                                onClick: ()=>{\n                                                    setApprovalAction(\"reject\");\n                                                    setShowApprovalModal(true);\n                                                },\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1184,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"拒絕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1185,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1176,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setSelectedRequest(null),\n                                        children: \"關閉\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1189,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1162,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 992,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 991,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 990,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n        lineNumber: 429,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaveApprovalPage, \"Ar0WQKmFVdQPYkPfgrKnB00pfUc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = LeaveApprovalPage;\nvar _c;\n$RefreshReg$(_c, \"LeaveApprovalPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vbGVhdmUtYXBwcm92YWwvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWdEO0FBQ0w7QUFDQTtBQUNJO0FBQ25CO0FBQ1k7QUFvQm5CO0FBd0JOLFNBQVN1Qjs7SUFDdEIsTUFBTSxFQUFFQyxJQUFJLEVBQUVsQixPQUFPbUIsU0FBUyxFQUFFQyxPQUFPLEVBQUUsR0FBRzFCLDhEQUFPQTtJQUNuRCxNQUFNMkIsU0FBU3hCLDBEQUFTQTtJQUN4QixNQUFNLENBQUN5QixlQUFlQyxpQkFBaUIsR0FBRzVCLCtDQUFRQSxDQUFpQixFQUFFO0lBQ3JFLE1BQU0sQ0FBQzZCLGFBQWFDLGVBQWUsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQytCLGtCQUFrQkMsb0JBQW9CLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNpQyxpQkFBaUJDLG1CQUFtQixHQUFHbEMsK0NBQVFBLENBQXNCO0lBQzVFLE1BQU0sQ0FBQ21DLG1CQUFtQkMscUJBQXFCLEdBQUdwQywrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUNxQyxnQkFBZ0JDLGtCQUFrQixHQUFHdEMsK0NBQVFBLENBQXVCO0lBQzNFLE1BQU0sQ0FBQ3VDLGlCQUFpQkMsbUJBQW1CLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUN5QyxjQUFjQyxnQkFBZ0IsR0FBRzFDLCtDQUFRQSxDQUFDO1FBQy9DMkMsZUFBZTtRQUNmQyxRQUFRO1FBQ1JDLFlBQVk7SUFDZDtJQUNBLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBRy9DLCtDQUFRQSxDQUFpQixFQUFFO0lBQzNFLE1BQU0sQ0FBQ2dELE9BQU9DLFNBQVMsR0FBR2pELCtDQUFRQSxDQUFDO1FBQ2pDa0QsU0FBUztRQUNUQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtJQUNaO0lBRUEsU0FBUztJQUNULE1BQU1DLGVBQTBDO1FBQzlDQyxRQUFRO1FBQ1JDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsVUFBVTtRQUNWQyxTQUFTO1FBQ1RDLGNBQWM7UUFDZEMsT0FBTztJQUNUO0lBRUEscUJBQXFCO0lBQ3JCLE1BQU1DLGtCQUFrQjtRQUN0QmhDLG9CQUFvQjtRQUNwQixJQUFJO1lBQ0ZpQyxRQUFRQyxHQUFHLENBQUM7WUFDWixNQUFNQyxXQUFXLE1BQU05RCxzREFBS0EsQ0FBQztnQkFDM0IrRCxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFFQSxJQUFJRixZQUFZQSxTQUFTNUMsSUFBSSxFQUFFO2dCQUM3QixNQUFNK0MsV0FBV0gsU0FBUzVDLElBQUk7Z0JBQzlCLE1BQU1BLE9BQU87b0JBQ1hnRCxJQUFJRCxTQUFTRixXQUFXO29CQUN4QkksTUFBTUYsU0FBUzNCLGFBQWE7b0JBQzVCeUIsYUFBYUUsU0FBU0csYUFBYTtvQkFDbkNDLGVBQWVKLFNBQVNJLGFBQWE7b0JBQ3JDQyxVQUFVTCxTQUFTTSxPQUFPLEtBQUssTUFBTSxVQUFVO29CQUMvQ0MsT0FBT1AsU0FBU08sS0FBSztvQkFDckJELFNBQVNOLFNBQVNNLE9BQU87b0JBQ3pCRSxpQkFBaUJSLFNBQVNRLGVBQWU7Z0JBQzNDO2dCQUVBdEQsVUFBVUQ7Z0JBQ1YwQyxRQUFRQyxHQUFHLENBQUMsV0FBVzNDO1lBQ3pCLE9BQU87Z0JBQ0wwQyxRQUFRYyxLQUFLLENBQUMsV0FBV1o7WUFDM0I7UUFDRixFQUFFLE9BQU9ZLE9BQU87WUFDZGQsUUFBUWMsS0FBSyxDQUFDLFdBQVdBO1FBQzNCLFNBQVU7WUFDUi9DLG9CQUFvQjtRQUN0QjtJQUNGO0lBRUEsbUJBQW1CO0lBQ25CL0IsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUN3QixXQUFXLENBQUNGLE1BQU07WUFDckIwQyxRQUFRQyxHQUFHLENBQUM7WUFDWkY7UUFDRjtJQUNGLEdBQUc7UUFBQ3ZDO1FBQVNGO0tBQUs7SUFFbEIsY0FBYztJQUNkdEIsZ0RBQVNBLENBQUM7UUFDUixNQUFNK0UscUJBQXFCO1lBQ3pCLElBQUk7Z0JBQ0ZsRCxlQUFlO2dCQUNmLE1BQU1tRCxhQUFhQyxPQUFPQyxRQUFRLENBQUNDLFFBQVEsS0FBSyxlQUFlRixPQUFPQyxRQUFRLENBQUNDLFFBQVEsS0FBSyxjQUN4RiwwQkFDQSxVQUFtQyxPQUF6QkYsT0FBT0MsUUFBUSxDQUFDQyxRQUFRLEVBQUM7Z0JBQ3ZDLE1BQU1qQixXQUFXLE1BQU1rQixNQUFNLEdBQWMsT0FBWEosWUFBVztnQkFFM0MsSUFBSSxDQUFDZCxTQUFTbUIsRUFBRSxFQUFFO29CQUNoQixNQUFNLElBQUlDLE1BQU07Z0JBQ2xCO2dCQUVBLE1BQU1DLE9BQU8sTUFBTXJCLFNBQVNzQixJQUFJO2dCQUNoQyxNQUFNQyxjQUFjRixLQUFLRyxPQUFPLElBQUksRUFBRTtnQkFFdEMsb0JBQW9CO2dCQUNwQixNQUFNQyxXQUFXRixZQUFZRyxHQUFHLENBQUMsQ0FBQ0M7b0JBQ2hDLFVBQVU7b0JBQ1YsSUFBSUMsWUFBWTtvQkFDaEIsSUFBSTt3QkFDRixNQUFNQyxZQUFZLElBQUlDLEtBQUtILEtBQUtJLFVBQVU7d0JBQzFDLE1BQU1DLFVBQVUsSUFBSUYsS0FBS0gsS0FBS00sUUFBUTt3QkFDdEMsSUFBSSxDQUFDQyxNQUFNTCxVQUFVTSxPQUFPLE9BQU8sQ0FBQ0QsTUFBTUYsUUFBUUcsT0FBTyxLQUFLOzRCQUM1RFAsWUFBWVEsS0FBS0MsSUFBSSxDQUFDLENBQUNMLFFBQVFHLE9BQU8sS0FBS04sVUFBVU0sT0FBTyxFQUFDLElBQU0sUUFBTyxLQUFLLEtBQUssRUFBQyxLQUFNO3dCQUM3RjtvQkFDRixFQUFFLE9BQU92QixPQUFPO3dCQUNkZCxRQUFRd0MsSUFBSSxDQUFDLFdBQVdYLEtBQUtJLFVBQVUsRUFBRUosS0FBS00sUUFBUSxFQUFFckI7b0JBQzFEO29CQUVBLFNBQVM7b0JBQ1QsSUFBSTJCLGNBQXdCLEVBQUU7b0JBQzlCLElBQUlaLEtBQUtZLFdBQVcsRUFBRTt3QkFDcEIsSUFBSTs0QkFDRkEsY0FBYyxPQUFPWixLQUFLWSxXQUFXLEtBQUssV0FDdENDLEtBQUtDLEtBQUssQ0FBQ2QsS0FBS1ksV0FBVyxJQUMzQlosS0FBS1ksV0FBVzt3QkFDdEIsRUFBRSxPQUFPM0IsT0FBTzs0QkFDZGQsUUFBUXdDLElBQUksQ0FBQyxhQUFhWCxLQUFLWSxXQUFXLEVBQUUzQjs0QkFDNUMyQixjQUFjLEVBQUU7d0JBQ2xCO29CQUNGO29CQUVBLE9BQU87d0JBQ0xuQyxJQUFJdUIsS0FBS3ZCLEVBQUU7d0JBQ1hILGFBQWEwQixLQUFLckIsYUFBYSxJQUFJcUIsS0FBSzFCLFdBQVcsSUFBSTt3QkFDdkR6QixlQUFlbUQsS0FBS25ELGFBQWEsSUFBSTt3QkFDckNtQyxpQkFBaUJnQixLQUFLaEIsZUFBZSxJQUFJO3dCQUN6Q2pDLFlBQVlpRCxLQUFLakQsVUFBVTt3QkFDM0JxRCxZQUFZSixLQUFLSSxVQUFVO3dCQUMzQkUsVUFBVU4sS0FBS00sUUFBUTt3QkFDdkJTLFlBQVlkO3dCQUNaZSxRQUFRaEIsS0FBS2dCLE1BQU0sSUFBSTt3QkFDdkJsRSxRQUFRa0QsS0FBS2xELE1BQU07d0JBQ25CbUUsY0FBY2pCLEtBQUtrQixVQUFVO3dCQUM3QkEsWUFBWWxCLEtBQUtrQixVQUFVO3dCQUMzQkMsYUFBYW5CLEtBQUtvQixhQUFhO3dCQUMvQkMsYUFBYXJCLEtBQUtxQixXQUFXO3dCQUM3QkMsa0JBQWtCdEIsS0FBS3VCLE9BQU87d0JBQzlCQyxtQkFBbUJ4QixLQUFLd0IsaUJBQWlCLElBQUk7d0JBQzdDQyxlQUFlekIsS0FBS3lCLGFBQWE7d0JBQ2pDQyxpQkFBaUIxQixLQUFLMEIsZUFBZSxJQUFJO3dCQUN6Q2QsYUFBYUE7b0JBQ2Y7Z0JBQ0Y7Z0JBRUF6QyxRQUFRQyxHQUFHLENBQUMsZUFBZXdCLFlBQVkrQixNQUFNO2dCQUM3Q3hELFFBQVFDLEdBQUcsQ0FBQyxZQUFZMEIsU0FBUzZCLE1BQU07Z0JBQ3ZDeEQsUUFBUUMsR0FBRyxDQUFDLGNBQWMwQixTQUFTOEIsS0FBSyxDQUFDLEdBQUc7Z0JBRTVDOUYsaUJBQWlCZ0U7Z0JBQ2pCN0Msb0JBQW9CNkM7Z0JBRXBCLFNBQVM7Z0JBQ1QsTUFBTStCLFdBQVc7b0JBQ2Z6RSxTQUFTMEMsU0FBU2dDLE1BQU0sQ0FBQyxDQUFDQyxJQUFvQkEsRUFBRWpGLE1BQU0sS0FBSyxXQUFXNkUsTUFBTTtvQkFDNUV0RSxVQUFVeUMsU0FBU2dDLE1BQU0sQ0FBQyxDQUFDQyxJQUN6QixJQUFJNUIsS0FBSzRCLEVBQUViLFVBQVUsSUFBSWEsRUFBRWQsWUFBWSxFQUFFZSxZQUFZLE9BQU8sSUFBSTdCLE9BQU82QixZQUFZLElBQ25GTCxNQUFNO29CQUNSckUsVUFBVXdDLFNBQVNnQyxNQUFNLENBQUMsQ0FBQ0MsSUFBb0JBLEVBQUVqRixNQUFNLEtBQUssWUFBWTZFLE1BQU07b0JBQzlFcEUsVUFBVXVDLFNBQVNnQyxNQUFNLENBQUMsQ0FBQ0MsSUFBb0JBLEVBQUVqRixNQUFNLEtBQUssWUFBWTZFLE1BQU07Z0JBQ2hGO2dCQUNBeEUsU0FBUzBFO1lBQ1gsRUFBRSxPQUFPNUMsT0FBTztnQkFDZGQsUUFBUWMsS0FBSyxDQUFDLGtDQUFrQ0E7Z0JBQ2hELGdCQUFnQjtnQkFDaEJuRCxpQkFBaUIsRUFBRTtnQkFDbkJtQixvQkFBb0IsRUFBRTtnQkFDdEJFLFNBQVM7b0JBQUVDLFNBQVM7b0JBQUdDLFVBQVU7b0JBQUdDLFVBQVU7b0JBQUdDLFVBQVU7Z0JBQUU7WUFDL0QsU0FBVTtnQkFDUnZCLGVBQWU7WUFDakI7UUFDRjtRQUVBa0Q7SUFDRixHQUFHLEVBQUU7SUFFTCxRQUFRO0lBQ1IsTUFBTStDLGVBQWU7UUFDbkIsSUFBSUMsV0FBV3JHO1FBRWYsSUFBSWMsYUFBYUUsYUFBYSxFQUFFO1lBQzlCcUYsV0FBV0EsU0FBU0osTUFBTSxDQUFDSyxDQUFBQTtvQkFDekJBO3dCQUFBQSx5QkFBQUEsUUFBUXRGLGFBQWEsY0FBckJzRiw2Q0FBQUEsdUJBQXVCQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQzFGLGFBQWFFLGFBQWEsQ0FBQ3VGLFdBQVc7O1FBRXhGO1FBQ0EsSUFBSXpGLGFBQWFHLE1BQU0sRUFBRTtZQUN2Qm9GLFdBQVdBLFNBQVNKLE1BQU0sQ0FBQ0ssQ0FBQUEsVUFBV0EsUUFBUXJGLE1BQU0sS0FBS0gsYUFBYUcsTUFBTTtRQUM5RTtRQUNBLElBQUlILGFBQWFJLFVBQVUsRUFBRTtZQUMzQm1GLFdBQVdBLFNBQVNKLE1BQU0sQ0FBQ0ssQ0FBQUEsVUFBV0EsUUFBUXBGLFVBQVUsS0FBS0osYUFBYUksVUFBVTtRQUN0RjtRQUVBRSxvQkFBb0JpRjtJQUN0QjtJQUVBLE1BQU1JLGNBQWM7UUFDbEIxRixnQkFBZ0I7WUFDZEMsZUFBZTtZQUNmQyxRQUFRO1lBQ1JDLFlBQVk7UUFDZDtRQUNBRSxvQkFBb0JwQjtJQUN0QjtJQUVBLE9BQU87SUFDUCxNQUFNMEcsaUJBQWlCO1FBQ3JCLElBQUksQ0FBQ3BHLGlCQUFpQjtRQUV0QixJQUFJO1lBQ0YsV0FBVztZQUNYLE1BQU1nRCxhQUFhQyxPQUFPQyxRQUFRLENBQUNDLFFBQVEsS0FBSyxlQUFlRixPQUFPQyxRQUFRLENBQUNDLFFBQVEsS0FBSyxjQUN4RiwwQkFDQSxVQUFtQyxPQUF6QkYsT0FBT0MsUUFBUSxDQUFDQyxRQUFRLEVBQUM7WUFDdkMsTUFBTWpCLFdBQVcsTUFBTWtCLE1BQU0sR0FBcUNwRCxPQUFsQ2dELFlBQVcseUJBQTBDLE9BQW5CaEQsZ0JBQWdCc0MsRUFBRSxHQUFJO2dCQUN0RitELFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTTdCLEtBQUs4QixTQUFTLENBQUM7b0JBQ25CQyxRQUFRckc7b0JBQ1JnRixTQUFTaEYsbUJBQW1CLFdBQVdFLGtCQUFrQm9HO2dCQUMzRDtZQUNGO1lBRUEsSUFBSSxDQUFDeEUsU0FBU21CLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUEsU0FBUztZQUNULE1BQU1xRCxnQkFBZ0IsTUFBTXZELE1BQU0sR0FBYyxPQUFYSixZQUFXO1lBQ2hELElBQUkyRCxjQUFjdEQsRUFBRSxFQUFFO2dCQUNwQixNQUFNRSxPQUFPLE1BQU1vRCxjQUFjbkQsSUFBSTtnQkFDckMsTUFBTUMsY0FBY0YsS0FBS0csT0FBTyxJQUFJLEVBQUU7Z0JBRXRDLG9CQUFvQjtnQkFDcEIsTUFBTUMsV0FBV0YsWUFBWUcsR0FBRyxDQUFDLENBQUNDO29CQUNoQyxVQUFVO29CQUNWLElBQUlDLFlBQVk7b0JBQ2hCLElBQUk7d0JBQ0YsTUFBTUMsWUFBWSxJQUFJQyxLQUFLSCxLQUFLSSxVQUFVO3dCQUMxQyxNQUFNQyxVQUFVLElBQUlGLEtBQUtILEtBQUtNLFFBQVE7d0JBQ3RDLElBQUksQ0FBQ0MsTUFBTUwsVUFBVU0sT0FBTyxPQUFPLENBQUNELE1BQU1GLFFBQVFHLE9BQU8sS0FBSzs0QkFDNURQLFlBQVlRLEtBQUtDLElBQUksQ0FBQyxDQUFDTCxRQUFRRyxPQUFPLEtBQUtOLFVBQVVNLE9BQU8sRUFBQyxJQUFNLFFBQU8sS0FBSyxLQUFLLEVBQUMsS0FBTTt3QkFDN0Y7b0JBQ0YsRUFBRSxPQUFPdkIsT0FBTzt3QkFDZGQsUUFBUXdDLElBQUksQ0FBQyxXQUFXWCxLQUFLSSxVQUFVLEVBQUVKLEtBQUtNLFFBQVEsRUFBRXJCO29CQUMxRDtvQkFFQSxTQUFTO29CQUNULElBQUkyQixjQUF3QixFQUFFO29CQUM5QixJQUFJWixLQUFLWSxXQUFXLEVBQUU7d0JBQ3BCLElBQUk7NEJBQ0ZBLGNBQWMsT0FBT1osS0FBS1ksV0FBVyxLQUFLLFdBQ3RDQyxLQUFLQyxLQUFLLENBQUNkLEtBQUtZLFdBQVcsSUFDM0JaLEtBQUtZLFdBQVc7d0JBQ3RCLEVBQUUsT0FBTzNCLE9BQU87NEJBQ2RkLFFBQVF3QyxJQUFJLENBQUMsYUFBYVgsS0FBS1ksV0FBVyxFQUFFM0I7NEJBQzVDMkIsY0FBYyxFQUFFO3dCQUNsQjtvQkFDRjtvQkFFQSxPQUFPO3dCQUNMbkMsSUFBSXVCLEtBQUt2QixFQUFFO3dCQUNYSCxhQUFhMEIsS0FBS3JCLGFBQWEsSUFBSXFCLEtBQUsxQixXQUFXLElBQUk7d0JBQ3ZEekIsZUFBZW1ELEtBQUtuRCxhQUFhLElBQUk7d0JBQ3JDbUMsaUJBQWlCZ0IsS0FBS2hCLGVBQWUsSUFBSTt3QkFDekNqQyxZQUFZaUQsS0FBS2pELFVBQVU7d0JBQzNCcUQsWUFBWUosS0FBS0ksVUFBVTt3QkFDM0JFLFVBQVVOLEtBQUtNLFFBQVE7d0JBQ3ZCUyxZQUFZZDt3QkFDWmUsUUFBUWhCLEtBQUtnQixNQUFNLElBQUk7d0JBQ3ZCbEUsUUFBUWtELEtBQUtsRCxNQUFNO3dCQUNuQm1FLGNBQWNqQixLQUFLa0IsVUFBVTt3QkFDN0JBLFlBQVlsQixLQUFLa0IsVUFBVTt3QkFDM0JDLGFBQWFuQixLQUFLb0IsYUFBYTt3QkFDL0JDLGFBQWFyQixLQUFLcUIsV0FBVzt3QkFDN0JDLGtCQUFrQnRCLEtBQUt1QixPQUFPO3dCQUM5QkMsbUJBQW1CeEIsS0FBS3dCLGlCQUFpQixJQUFJO3dCQUM3Q0MsZUFBZXpCLEtBQUt5QixhQUFhO3dCQUNqQ0MsaUJBQWlCMUIsS0FBSzBCLGVBQWUsSUFBSTt3QkFDekNkLGFBQWFBO29CQUNmO2dCQUNGO2dCQUVBOUUsaUJBQWlCZ0U7Z0JBQ2pCN0Msb0JBQW9CNkMsU0FBU2dDLE1BQU0sQ0FBQyxDQUFDSztvQkFDbkMsSUFBSVksUUFBUTtvQkFDWixJQUFJcEcsYUFBYUUsYUFBYSxFQUFFOzRCQUNic0Y7d0JBQWpCWSxRQUFRQSxXQUFTWix5QkFBQUEsUUFBUXRGLGFBQWEsY0FBckJzRiw2Q0FBQUEsdUJBQXVCQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQzFGLGFBQWFFLGFBQWEsQ0FBQ3VGLFdBQVc7b0JBQ3ZHO29CQUNBLElBQUl6RixhQUFhRyxNQUFNLEVBQUU7d0JBQ3ZCaUcsUUFBUUEsU0FBU1osUUFBUXJGLE1BQU0sS0FBS0gsYUFBYUcsTUFBTTtvQkFDekQ7b0JBQ0EsSUFBSUgsYUFBYUksVUFBVSxFQUFFO3dCQUMzQmdHLFFBQVFBLFNBQVNaLFFBQVFwRixVQUFVLEtBQUtKLGFBQWFJLFVBQVU7b0JBQ2pFO29CQUNBLE9BQU9nRztnQkFDVDtnQkFFQSxPQUFPO2dCQUNQLE1BQU1sQixXQUFXO29CQUNmekUsU0FBUzBDLFNBQVNnQyxNQUFNLENBQUMsQ0FBQ0MsSUFBb0JBLEVBQUVqRixNQUFNLEtBQUssV0FBVzZFLE1BQU07b0JBQzVFdEUsVUFBVXlDLFNBQVNnQyxNQUFNLENBQUMsQ0FBQ0MsSUFDekIsSUFBSTVCLEtBQUs0QixFQUFFYixVQUFVLElBQUlhLEVBQUVkLFlBQVksRUFBRWUsWUFBWSxPQUFPLElBQUk3QixPQUFPNkIsWUFBWSxJQUNuRkwsTUFBTTtvQkFDUnJFLFVBQVV3QyxTQUFTZ0MsTUFBTSxDQUFDLENBQUNDLElBQW9CQSxFQUFFakYsTUFBTSxLQUFLLFlBQVk2RSxNQUFNO29CQUM5RXBFLFVBQVV1QyxTQUFTZ0MsTUFBTSxDQUFDLENBQUNDLElBQW9CQSxFQUFFakYsTUFBTSxLQUFLLFlBQVk2RSxNQUFNO2dCQUNoRjtnQkFDQXhFLFNBQVMwRTtZQUNYO1lBRUF2RixxQkFBcUI7WUFDckJGLG1CQUFtQjtZQUNuQk0sbUJBQW1CO1FBQ3JCLEVBQUUsT0FBT3VDLE9BQU87WUFDZGQsUUFBUWMsS0FBSyxDQUFDLGlDQUFpQ0E7WUFDL0MrRCxNQUFNO1FBQ1I7SUFDRjtJQUVBLE1BQU1DLGlCQUFpQixDQUFDbkc7UUFDdEIsT0FBUUE7WUFDTixLQUFLO2dCQUFXLE9BQU87WUFDdkIsS0FBSztnQkFBWSxPQUFPO1lBQ3hCLEtBQUs7Z0JBQVksT0FBTztZQUN4QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNb0csZ0JBQWdCLENBQUNwRztRQUNyQixPQUFRQTtZQUNOLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFZLE9BQU87WUFDeEIsS0FBSztnQkFBWSxPQUFPO1lBQ3hCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU1xRyxnQkFBZ0IsQ0FBQ3JHO1FBQ3JCLE9BQVFBO1lBQ04sS0FBSztnQkFBVyxxQkFBTyw4REFBQ3JDLDBNQUFLQTtvQkFBQzJJLFdBQVU7Ozs7OztZQUN4QyxLQUFLO2dCQUFZLHFCQUFPLDhEQUFDekksME1BQVdBO29CQUFDeUksV0FBVTs7Ozs7O1lBQy9DLEtBQUs7Z0JBQVkscUJBQU8sOERBQUN4SSwwTUFBT0E7b0JBQUN3SSxXQUFVOzs7Ozs7WUFDM0M7Z0JBQVMscUJBQU8sOERBQUNqSSwyTUFBV0E7b0JBQUNpSSxXQUFVOzs7Ozs7UUFDekM7SUFDRjtJQUVBLElBQUl6SCxXQUFXTSxrQkFBa0I7UUFDL0IscUJBQ0UsOERBQUNvSDtZQUFJRCxXQUFVO3NCQUNiLDRFQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFJRCxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNFO3dCQUFFRixXQUFVO2tDQUNWbkgsbUJBQW1CLGFBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSzNDO0lBRUEsSUFBSSxDQUFDUixRQUFRQSxLQUFLcUQsT0FBTyxLQUFLLEtBQUs7UUFDakMscUJBQ0UsOERBQUN1RTtZQUFJRCxXQUFVO3NCQUNiLDRFQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUN4SSwwTUFBT0E7d0JBQUN3SSxXQUFVOzs7Ozs7a0NBQ25CLDhEQUFDRzt3QkFBR0gsV0FBVTtrQ0FBd0M7Ozs7OztrQ0FDdEQsOERBQUNFO3dCQUFFRixXQUFVO2tDQUFxQjs7Ozs7O2tDQUNsQyw4REFBQy9JLHlEQUFNQTt3QkFBQ21KLFNBQVMsSUFBTTVILE9BQU82SCxJQUFJLENBQUM7a0NBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTXREO0lBRUEscUJBQ0UsOERBQUNKO1FBQUlELFdBQVU7OzBCQUdiLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUlELFdBQVU7O3NDQUViLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNNO29DQUFHTixXQUFVOzhDQUFxQzs7Ozs7OzhDQUNuRCw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUViLDhEQUFDOUksaURBQUlBOzRDQUFDcUosTUFBSzs0Q0FBU1AsV0FBVTs7OERBQzVCLDhEQUFDNUksMk1BQVNBO29EQUFDNEksV0FBVTs7Ozs7OzhEQUNyQiw4REFBQ1E7b0RBQUtSLFdBQVU7OERBQTZEOzs7Ozs7Ozs7Ozs7c0RBRS9FLDhEQUFDRTs0Q0FBRUYsV0FBVTtzREFBd0M7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLekQsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7O3NEQUNDLDhEQUFDQzs0Q0FBRUYsV0FBVTtzREFBaUM7Ozs7OztzREFDOUMsOERBQUNFOzRDQUFFRixXQUFVO3NEQUEwQjs7Ozs7Ozs7Ozs7OzhDQUV6Qyw4REFBQ0M7b0NBQUlELFdBQVU7OENBQ2IsNEVBQUNySSwyTUFBUUE7d0NBQUNxSSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU81Qiw4REFBQ1M7Z0JBQUtULFdBQVU7O2tDQUVkLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNHO2dDQUFHSCxXQUFVOzBDQUF3Qzs7Ozs7OzBDQUN0RCw4REFBQ0U7Z0NBQUVGLFdBQVU7MENBQXdCOzs7Ozs7Ozs7Ozs7a0NBSXZDLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBRWIsOERBQUNDO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUNiLDRFQUFDM0ksME1BQUtBO2dEQUFDMkksV0FBVTs7Ozs7Ozs7Ozs7c0RBRW5CLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNFO29EQUFFRixXQUFVOzhEQUFvQ2xHLE1BQU1FLE9BQU87Ozs7Ozs4REFDOUQsOERBQUNrRztvREFBRUYsV0FBVTs4REFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU0zQyw4REFBQ0M7Z0NBQUlELFdBQVU7MENBQ2IsNEVBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7c0RBQ2IsNEVBQUMxSSwyTUFBVUE7Z0RBQUMwSSxXQUFVOzs7Ozs7Ozs7OztzREFFeEIsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0U7b0RBQUVGLFdBQVU7OERBQW9DbEcsTUFBTUcsUUFBUTs7Ozs7OzhEQUMvRCw4REFBQ2lHO29EQUFFRixXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTTNDLDhEQUFDQztnQ0FBSUQsV0FBVTswQ0FDYiw0RUFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ3pJLDBNQUFXQTtnREFBQ3lJLFdBQVU7Ozs7Ozs7Ozs7O3NEQUV6Qiw4REFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDRTtvREFBRUYsV0FBVTs4REFBb0NsRyxNQUFNSSxRQUFROzs7Ozs7OERBQy9ELDhEQUFDZ0c7b0RBQUVGLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNM0MsOERBQUNDO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUNiLDRFQUFDeEksME1BQU9BO2dEQUFDd0ksV0FBVTs7Ozs7Ozs7Ozs7c0RBRXJCLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNFO29EQUFFRixXQUFVOzhEQUFvQ2xHLE1BQU1LLFFBQVE7Ozs7Ozs4REFDL0QsOERBQUMrRjtvREFBRUYsV0FBVTs4REFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU83Qyw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUViLDhEQUFDQztnQ0FBSUQsV0FBVTswQ0FDYiw0RUFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFJRCxXQUFVOzhEQUNiLDRFQUFDL0gsMk1BQU1BO3dEQUFDK0gsV0FBVTs7Ozs7Ozs7Ozs7OERBRXBCLDhEQUFDQzs7c0VBQ0MsOERBQUNTOzREQUFHVixXQUFVO3NFQUFrQzs7Ozs7O3NFQUNoRCw4REFBQ0U7NERBQUVGLFdBQVU7c0VBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBRzlDLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUMvSSx5REFBTUE7b0RBQ0wwSixTQUFRO29EQUNSUCxTQUFTLElBQU1wRSxPQUFPQyxRQUFRLENBQUMyRSxNQUFNO29EQUNyQ1osV0FBVTs7c0VBRVYsOERBQUM3SCwyTUFBU0E7NERBQUM2SCxXQUFVOzs7Ozs7c0VBQ3JCLDhEQUFDUTtzRUFBSzs7Ozs7Ozs7Ozs7OzhEQUVSLDhEQUFDdkoseURBQU1BO29EQUFDMEosU0FBUTtvREFBVVgsV0FBVTs7c0VBQ2xDLDhEQUFDOUgsMk1BQVFBOzREQUFDOEgsV0FBVTs7Ozs7O3NFQUNwQiw4REFBQ1E7c0VBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU9kLDhEQUFDUDtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVOzswREFFYiw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDQzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUN0SSwyTUFBSUE7Z0VBQUNzSSxXQUFVOzs7Ozs7MEVBQ2hCLDhEQUFDYTtnRUFBTWIsV0FBVTswRUFBNEM7Ozs7Ozs7Ozs7OztrRUFFL0QsOERBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ2hJLDJNQUFNQTtnRUFBQ2dJLFdBQVU7Ozs7OzswRUFDbEIsOERBQUNjO2dFQUNDQyxNQUFLO2dFQUNMQyxPQUFPekgsYUFBYUUsYUFBYTtnRUFDakN3SCxVQUFVLENBQUNDLElBQU0xSCxnQkFBZ0I7d0VBQUUsR0FBR0QsWUFBWTt3RUFBRUUsZUFBZXlILEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztvRUFBQztnRUFDbEZJLGFBQVk7Z0VBQ1pwQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTWhCLDhEQUFDQztnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ3pJLDBNQUFXQTtnRUFBQ3lJLFdBQVU7Ozs7OzswRUFDdkIsOERBQUNhO2dFQUFNYixXQUFVOzBFQUE0Qzs7Ozs7Ozs7Ozs7O2tFQUUvRCw4REFBQ3FCO3dEQUNDTCxPQUFPekgsYUFBYUcsTUFBTTt3REFDMUJ1SCxVQUFVLENBQUNDLElBQU0xSCxnQkFBZ0I7Z0VBQUUsR0FBR0QsWUFBWTtnRUFBRUcsUUFBUXdILEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0REFBQzt3REFDM0VoQixXQUFVOzswRUFFViw4REFBQ3NCO2dFQUFPTixPQUFNOzBFQUFHOzs7Ozs7MEVBQ2pCLDhEQUFDTTtnRUFBT04sT0FBTTswRUFBVTs7Ozs7OzBFQUN4Qiw4REFBQ007Z0VBQU9OLE9BQU07MEVBQVc7Ozs7OzswRUFDekIsOERBQUNNO2dFQUFPTixPQUFNOzBFQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSzdCLDhEQUFDZjtnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ3ZJLDJNQUFRQTtnRUFBQ3VJLFdBQVU7Ozs7OzswRUFDcEIsOERBQUNhO2dFQUFNYixXQUFVOzBFQUE0Qzs7Ozs7Ozs7Ozs7O2tFQUUvRCw4REFBQ3FCO3dEQUNDTCxPQUFPekgsYUFBYUksVUFBVTt3REFDOUJzSCxVQUFVLENBQUNDLElBQU0xSCxnQkFBZ0I7Z0VBQUUsR0FBR0QsWUFBWTtnRUFBRUksWUFBWXVILEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0REFBQzt3REFDL0VoQixXQUFVOzswRUFFViw4REFBQ3NCO2dFQUFPTixPQUFNOzBFQUFHOzs7Ozs7NERBQ2hCTyxPQUFPQyxPQUFPLENBQUNwSCxjQUFjdUMsR0FBRyxDQUFDO29FQUFDLENBQUM4RSxLQUFLVCxNQUFNO3FGQUM3Qyw4REFBQ007b0VBQWlCTixPQUFPUzs4RUFBTVQ7bUVBQWxCUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU9yQiw4REFBQ3hCO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDL0kseURBQU1BO3dEQUFDMEosU0FBUTt3REFBVVAsU0FBU3ZCO3dEQUFjbUIsV0FBVTs7MEVBQ3pELDhEQUFDaEksMk1BQU1BO2dFQUFDZ0ksV0FBVTs7Ozs7OzBFQUNsQiw4REFBQ1E7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFHUiw4REFBQ3ZKLHlEQUFNQTt3REFBQzBKLFNBQVE7d0RBQVVQLFNBQVNsQjt3REFBYWMsV0FBVTs7MEVBQ3hELDhEQUFDN0gsMk1BQVNBO2dFQUFDNkgsV0FBVTs7Ozs7OzBFQUNyQiw4REFBQ1E7MEVBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFNViw4REFBQ1A7Z0RBQUlELFdBQVU7O29EQUF3QjtvREFDaENwRyxpQkFBaUIyRSxNQUFNO29EQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9yQyw4REFBQzBCO3dCQUFJRCxXQUFVOzswQ0FFYiw4REFBQ0M7Z0NBQUlELFdBQVU7MENBQ2IsNEVBQUNDO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDQzt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNDO2dEQUFJRCxXQUFVOzBEQUNiLDRFQUFDckksMk1BQVFBO29EQUFDcUksV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDQzs7a0VBQ0MsOERBQUNTO3dEQUFHVixXQUFVO2tFQUFtQzs7Ozs7O2tFQUNqRCw4REFBQ0U7d0RBQUVGLFdBQVU7a0VBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU1qRHJILDRCQUNDLDhEQUFDc0g7Z0NBQUlELFdBQVU7MENBQ2IsNEVBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7Ozs7OztzREFDZiw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQWdCOzs7Ozs7Ozs7Ozs7Ozs7O3VDQUcvQnBHLGlCQUFpQjJFLE1BQU0sR0FBRyxrQkFDNUI7O2tEQUVFLDhEQUFDMEI7d0NBQUlELFdBQVU7a0RBQ2IsNEVBQUMwQjs0Q0FBTTFCLFdBQVU7OzhEQUNmLDhEQUFDMkI7b0RBQU0zQixXQUFVOzhEQUNmLDRFQUFDNEI7OzBFQUNDLDhEQUFDQztnRUFBRzdCLFdBQVU7MEVBQWlFOzs7Ozs7MEVBQy9FLDhEQUFDNkI7Z0VBQUc3QixXQUFVOzBFQUFpRTs7Ozs7OzBFQUMvRSw4REFBQzZCO2dFQUFHN0IsV0FBVTswRUFBaUU7Ozs7OzswRUFDL0UsOERBQUM2QjtnRUFBRzdCLFdBQVU7MEVBQWlFOzs7Ozs7MEVBQy9FLDhEQUFDNkI7Z0VBQUc3QixXQUFVOzBFQUFpRTs7Ozs7OzBFQUMvRSw4REFBQzZCO2dFQUFHN0IsV0FBVTswRUFBaUU7Ozs7OzswRUFDL0UsOERBQUM2QjtnRUFBRzdCLFdBQVU7MEVBQWlFOzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFHbkYsOERBQUM4QjtvREFBTTlCLFdBQVU7OERBQ2RwRyxpQkFBaUIrQyxHQUFHLENBQUMsQ0FBQ29DOzREQU1WQTs2RUFMWCw4REFBQzZDOzREQUFvQjVCLFdBQVU7OzhFQUM3Qiw4REFBQytCO29FQUFHL0IsV0FBVTs4RUFDWiw0RUFBQ0M7d0VBQUlELFdBQVU7OzBGQUNiLDhEQUFDQztnRkFBSUQsV0FBVTswRkFDYiw0RUFBQ1E7b0ZBQUtSLFdBQVU7OEZBQ2JqQixFQUFBQSx5QkFBQUEsUUFBUXRGLGFBQWEsY0FBckJzRiw2Q0FBQUEsdUJBQXVCaUQsTUFBTSxDQUFDLE9BQU07Ozs7Ozs7Ozs7OzBGQUd6Qyw4REFBQy9COztrR0FDQyw4REFBQ0E7d0ZBQUlELFdBQVU7a0dBQTZCakIsUUFBUXRGLGFBQWE7Ozs7OztrR0FDakUsOERBQUN3Rzt3RkFBSUQsV0FBVTtrR0FBeUJqQixRQUFRN0QsV0FBVzs7Ozs7O2tHQUMzRCw4REFBQytFO3dGQUFJRCxXQUFVO2tHQUF5QmpCLFFBQVFuRCxlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFJckUsOERBQUNtRztvRUFBRy9CLFdBQVU7OEVBQ1osNEVBQUNRO3dFQUFLUixXQUFVO2tGQUNiNUYsWUFBWSxDQUFDMkUsUUFBUXBGLFVBQVUsQ0FBQyxJQUFJb0YsUUFBUXBGLFVBQVUsSUFBSTs7Ozs7Ozs7Ozs7OEVBRy9ELDhEQUFDb0k7b0VBQUcvQixXQUFVOztzRkFDWiw4REFBQ0M7c0ZBQUssSUFBSWxELEtBQUtnQyxRQUFRL0IsVUFBVSxFQUFFaUYsa0JBQWtCLENBQUM7Ozs7Ozt3RUFDckRsRCxRQUFRL0IsVUFBVSxLQUFLK0IsUUFBUTdCLFFBQVEsa0JBQ3RDLDhEQUFDK0M7NEVBQUlELFdBQVU7O2dGQUF3QjtnRkFDbEMsSUFBSWpELEtBQUtnQyxRQUFRN0IsUUFBUSxFQUFFK0Usa0JBQWtCLENBQUM7Ozs7Ozs7Ozs7Ozs7OEVBSXZELDhEQUFDRjtvRUFBRy9CLFdBQVU7O3dFQUNYakIsUUFBUXBCLFVBQVU7d0VBQUM7Ozs7Ozs7OEVBRXRCLDhEQUFDb0U7b0VBQUcvQixXQUFVOzhFQUNaLDRFQUFDUTt3RUFBS1IsV0FBVyw4RUFBNkcsT0FBL0JILGVBQWVkLFFBQVFyRixNQUFNOzs0RUFDekhxRyxjQUFjaEIsUUFBUXJGLE1BQU07MEZBQzdCLDhEQUFDOEc7Z0ZBQUtSLFdBQVU7MEZBQVFGLGNBQWNmLFFBQVFyRixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFHeEQsOERBQUNxSTtvRUFBRy9CLFdBQVU7O3dFQUNYLElBQUlqRCxLQUFLZ0MsUUFBUWpCLFVBQVUsSUFBSWlCLFFBQVFsQixZQUFZLEVBQUVvRSxrQkFBa0IsQ0FBQztzRkFDekUsOERBQUNoQzs0RUFBSUQsV0FBVTtzRkFDWixJQUFJakQsS0FBS2dDLFFBQVFqQixVQUFVLElBQUlpQixRQUFRbEIsWUFBWSxFQUFFcUUsa0JBQWtCLENBQUMsU0FBUztnRkFDaEZDLE1BQU07Z0ZBQ05DLFFBQVE7NEVBQ1Y7Ozs7Ozs7Ozs7Ozs4RUFHSiw4REFBQ0w7b0VBQUcvQixXQUFVOzhFQUNaLDRFQUFDQzt3RUFBSUQsV0FBVTs7MEZBQ2IsOERBQUMvSSx5REFBTUE7Z0ZBQ0wwSixTQUFRO2dGQUNSMEIsTUFBSztnRkFDTGpDLFNBQVMsSUFBTXBILG1CQUFtQitGOzBGQUVsQyw0RUFBQ25ILDJNQUFHQTtvRkFBQ29JLFdBQVU7Ozs7Ozs7Ozs7OzRFQUVoQmpCLFFBQVFyRixNQUFNLEtBQUssMkJBQ2xCOztrR0FDRSw4REFBQ3pDLHlEQUFNQTt3RkFDTDBKLFNBQVE7d0ZBQ1IwQixNQUFLO3dGQUNMakMsU0FBUzs0RkFDUHBILG1CQUFtQitGOzRGQUNuQjNGLGtCQUFrQjs0RkFDbEJGLHFCQUFxQjt3RkFDdkI7a0dBRUEsNEVBQUNyQiwyTUFBS0E7NEZBQUNtSSxXQUFVOzs7Ozs7Ozs7OztrR0FFbkIsOERBQUMvSSx5REFBTUE7d0ZBQ0wwSixTQUFRO3dGQUNSMEIsTUFBSzt3RkFDTGpDLFNBQVM7NEZBQ1BwSCxtQkFBbUIrRjs0RkFDbkIzRixrQkFBa0I7NEZBQ2xCRixxQkFBcUI7d0ZBQ3ZCO2tHQUVBLDRFQUFDcEIsMk1BQUNBOzRGQUFDa0ksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyREE3RWhCakIsUUFBUTFELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBMEYzQiw4REFBQzRFO3dDQUFJRCxXQUFVO2tEQUNacEcsaUJBQWlCK0MsR0FBRyxDQUFDLENBQUNvQztnREFNVkE7aUVBTFgsOERBQUNrQjtnREFBcUJELFdBQVU7O2tFQUM5Qiw4REFBQ0M7d0RBQUlELFdBQVU7OzBFQUNiLDhEQUFDQztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUNDO3dFQUFJRCxXQUFVO2tGQUNiLDRFQUFDUTs0RUFBS1IsV0FBVTtzRkFDYmpCLEVBQUFBLHlCQUFBQSxRQUFRdEYsYUFBYSxjQUFyQnNGLDZDQUFBQSx1QkFBdUJpRCxNQUFNLENBQUMsT0FBTTs7Ozs7Ozs7Ozs7a0ZBR3pDLDhEQUFDL0I7OzBGQUNDLDhEQUFDUztnRkFBR1YsV0FBVTswRkFBK0JqQixRQUFRdEYsYUFBYTs7Ozs7OzBGQUNsRSw4REFBQ3lHO2dGQUFFRixXQUFVOzBGQUF5QmpCLFFBQVFuRCxlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBR2pFLDhEQUFDNEU7Z0VBQUtSLFdBQVcsa0ZBQWlILE9BQS9CSCxlQUFlZCxRQUFRckYsTUFBTTs7b0VBQzdIcUcsY0FBY2hCLFFBQVFyRixNQUFNO2tGQUM3Qiw4REFBQzhHO3dFQUFLUixXQUFVO2tGQUFRRixjQUFjZixRQUFRckYsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUl4RCw4REFBQ3VHO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ0M7O2tGQUNDLDhEQUFDTzt3RUFBS1IsV0FBVTtrRkFBZ0I7Ozs7OztrRkFDaEMsOERBQUNRO3dFQUFLUixXQUFVO2tGQUFzQjVGLFlBQVksQ0FBQzJFLFFBQVFwRixVQUFVLENBQUMsSUFBSW9GLFFBQVFwRixVQUFVLElBQUk7Ozs7Ozs7Ozs7OzswRUFFbEcsOERBQUNzRzs7a0ZBQ0MsOERBQUNPO3dFQUFLUixXQUFVO2tGQUFnQjs7Ozs7O2tGQUNoQyw4REFBQ1E7d0VBQUtSLFdBQVU7OzRFQUFzQmpCLFFBQVFwQixVQUFVOzRFQUFDOzs7Ozs7Ozs7Ozs7OzBFQUUzRCw4REFBQ3NDOztrRkFDQyw4REFBQ087d0VBQUtSLFdBQVU7a0ZBQWdCOzs7Ozs7a0ZBQ2hDLDhEQUFDUTt3RUFBS1IsV0FBVTtrRkFDYixJQUFJakQsS0FBS2dDLFFBQVEvQixVQUFVLEVBQUVpRixrQkFBa0IsQ0FBQzs7Ozs7Ozs7Ozs7OzBFQUdyRCw4REFBQ2hDOztrRkFDQyw4REFBQ087d0VBQUtSLFdBQVU7a0ZBQWdCOzs7Ozs7a0ZBQ2hDLDhEQUFDUTt3RUFBS1IsV0FBVTtrRkFDYixJQUFJakQsS0FBS2dDLFFBQVE3QixRQUFRLEVBQUUrRSxrQkFBa0IsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUtyRCw4REFBQ2hDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ1E7Z0VBQUtSLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3hDLDhEQUFDRTtnRUFBRUYsV0FBVTswRUFBOEJqQixRQUFRbkIsTUFBTTs7Ozs7Ozs7Ozs7O2tFQUczRCw4REFBQ3FDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ0M7Z0VBQUlELFdBQVU7O29FQUF3QjtvRUFDaEMsSUFBSWpELEtBQUtnQyxRQUFRakIsVUFBVSxJQUFJaUIsUUFBUWxCLFlBQVksRUFBRW9FLGtCQUFrQixDQUFDOzs7Ozs7OzBFQUUvRSw4REFBQ2hDO2dFQUFJRCxXQUFVOztrRkFDYiw4REFBQy9JLHlEQUFNQTt3RUFDTDBKLFNBQVE7d0VBQ1IwQixNQUFLO3dFQUNMakMsU0FBUyxJQUFNcEgsbUJBQW1CK0Y7a0ZBRWxDLDRFQUFDbkgsMk1BQUdBOzRFQUFDb0ksV0FBVTs7Ozs7Ozs7Ozs7b0VBRWhCakIsUUFBUXJGLE1BQU0sS0FBSywyQkFDbEI7OzBGQUNFLDhEQUFDekMseURBQU1BO2dGQUNMMEosU0FBUTtnRkFDUjBCLE1BQUs7Z0ZBQ0xqQyxTQUFTO29GQUNQcEgsbUJBQW1CK0Y7b0ZBQ25CM0Ysa0JBQWtCO29GQUNsQkYscUJBQXFCO2dGQUN2QjswRkFFQSw0RUFBQ3JCLDJNQUFLQTtvRkFBQ21JLFdBQVU7Ozs7Ozs7Ozs7OzBGQUVuQiw4REFBQy9JLHlEQUFNQTtnRkFDTDBKLFNBQVE7Z0ZBQ1IwQixNQUFLO2dGQUNMakMsU0FBUztvRkFDUHBILG1CQUFtQitGO29GQUNuQjNGLGtCQUFrQjtvRkFDbEJGLHFCQUFxQjtnRkFDdkI7MEZBRUEsNEVBQUNwQiwyTUFBQ0E7b0ZBQUNrSSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQ0FqRmZqQixRQUFRMUQsRUFBRTs7Ozs7Ozs7Ozs7OzZEQTRGMUIsOERBQUM0RTtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNySSwyTUFBUUE7d0NBQUNxSSxXQUFVOzs7Ozs7a0RBQ3BCLDhEQUFDVTt3Q0FBR1YsV0FBVTtrREFBeUM7Ozs7OztrREFDdkQsOERBQUNFO3dDQUFFRixXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT3BDL0cscUJBQXFCRixpQ0FDcEIsOERBQUNrSDtnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDVTt3Q0FBR1YsV0FBVTtrREFDWDdHLG1CQUFtQixZQUFZLFdBQVc7Ozs7OztrREFFN0MsOERBQUNsQyx5REFBTUE7d0NBQ0wwSixTQUFRO3dDQUNSMEIsTUFBSzt3Q0FDTGpDLFNBQVMsSUFBTWxILHFCQUFxQjtrREFFcEMsNEVBQUNwQiwyTUFBQ0E7NENBQUNrSSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLakIsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ3NDO3dDQUFHdEMsV0FBVTtrREFBbUM7Ozs7OztrREFDakQsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0M7O2tFQUNDLDhEQUFDTzt3REFBS1IsV0FBVTtrRUFBZ0I7Ozs7OztrRUFDaEMsOERBQUNRO3dEQUFLUixXQUFVO2tFQUFzQmpILGdCQUFnQlUsYUFBYTs7Ozs7Ozs7Ozs7OzBEQUVyRSw4REFBQ3dHOztrRUFDQyw4REFBQ087d0RBQUtSLFdBQVU7a0VBQWdCOzs7Ozs7a0VBQ2hDLDhEQUFDUTt3REFBS1IsV0FBVTtrRUFBc0JqSCxnQkFBZ0I2QyxlQUFlOzs7Ozs7Ozs7Ozs7MERBRXZFLDhEQUFDcUU7O2tFQUNDLDhEQUFDTzt3REFBS1IsV0FBVTtrRUFBZ0I7Ozs7OztrRUFDaEMsOERBQUNRO3dEQUFLUixXQUFVO2tFQUFzQjVGLFlBQVksQ0FBQ3JCLGdCQUFnQlksVUFBVSxDQUFDLElBQUlaLGdCQUFnQlksVUFBVSxJQUFJOzs7Ozs7Ozs7Ozs7MERBRWxILDhEQUFDc0c7O2tFQUNDLDhEQUFDTzt3REFBS1IsV0FBVTtrRUFBZ0I7Ozs7OztrRUFDaEMsOERBQUNRO3dEQUFLUixXQUFVOzs0REFBc0JqSCxnQkFBZ0I0RSxVQUFVOzREQUFDOzs7Ozs7Ozs7Ozs7OzBEQUVuRSw4REFBQ3NDOztrRUFDQyw4REFBQ087d0RBQUtSLFdBQVU7a0VBQWdCOzs7Ozs7a0VBQ2hDLDhEQUFDUTt3REFBS1IsV0FBVTtrRUFDYixJQUFJakQsS0FBS2hFLGdCQUFnQmlFLFVBQVUsRUFBRWlGLGtCQUFrQixDQUFDOzs7Ozs7Ozs7Ozs7MERBRzdELDhEQUFDaEM7O2tFQUNDLDhEQUFDTzt3REFBS1IsV0FBVTtrRUFBZ0I7Ozs7OztrRUFDaEMsOERBQUNRO3dEQUFLUixXQUFVO2tFQUNiLElBQUlqRCxLQUFLaEUsZ0JBQWdCbUUsUUFBUSxFQUFFK0Usa0JBQWtCLENBQUM7Ozs7Ozs7Ozs7Ozs0Q0FHMURsSixnQkFBZ0J1RixlQUFlLGtCQUM5Qiw4REFBQzJCOztrRUFDQyw4REFBQ087d0RBQUtSLFdBQVU7a0VBQWdCOzs7Ozs7a0VBQ2hDLDhEQUFDUTt3REFBS1IsV0FBVTtrRUFBc0JqSCxnQkFBZ0J1RixlQUFlOzs7Ozs7Ozs7Ozs7MERBR3pFLDhEQUFDMkI7O2tFQUNDLDhEQUFDTzt3REFBS1IsV0FBVTtrRUFBZ0I7Ozs7OztrRUFDaEMsOERBQUNRO3dEQUFLUixXQUFVO2tFQUFzQmpILGdCQUFnQnFGLGlCQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUczRSw4REFBQzZCO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUtSLFdBQVU7MERBQXdCOzs7Ozs7MERBQ3hDLDhEQUFDRTtnREFBRUYsV0FBVTswREFBc0JqSCxnQkFBZ0I2RSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSzdELDhEQUFDcUM7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDYTt3Q0FBTWIsV0FBVTtrREFDZDdHLG1CQUFtQixZQUFZLGFBQWE7Ozs7OztrREFFL0MsOERBQUNvSjt3Q0FDQ3ZCLE9BQU8zSDt3Q0FDUDRILFVBQVUsQ0FBQ0MsSUFBTTVILG1CQUFtQjRILEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FDbER3QixNQUFNO3dDQUNOeEMsV0FBVTt3Q0FDVm9CLGFBQ0VqSSxtQkFBbUIsWUFDZixjQUNBO3dDQUVOc0osVUFBVXRKLG1CQUFtQjs7Ozs7Ozs7Ozs7OzBDQUtqQyw4REFBQzhHO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQy9JLHlEQUFNQTt3Q0FDTDBKLFNBQVE7d0NBQ1JQLFNBQVMsSUFBTWxILHFCQUFxQjtrREFDckM7Ozs7OztrREFHRCw4REFBQ2pDLHlEQUFNQTt3Q0FDTDBKLFNBQVN4SCxtQkFBbUIsWUFBWSxZQUFZO3dDQUNwRGlILFNBQVNqQjt3Q0FDVHVELFVBQVV2SixtQkFBbUIsWUFBWSxDQUFDRSxnQkFBZ0JzSixJQUFJO3dDQUM5RDNDLFdBQVU7a0RBRVQ3RyxtQkFBbUIsMEJBQ2xCOzs4REFDRSw4REFBQ3RCLDJNQUFLQTtvREFBQ21JLFdBQVU7Ozs7Ozs4REFDakIsOERBQUNROzhEQUFLOzs7Ozs7O3lFQUdSOzs4REFDRSw4REFBQzFJLDJNQUFDQTtvREFBQ2tJLFdBQVU7Ozs7Ozs4REFDYiw4REFBQ1E7OERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBV3JCekgsbUJBQW1CLENBQUNFLG1DQUNuQiw4REFBQ2dIO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNVO3dDQUFHVixXQUFVO2tEQUFtQzs7Ozs7O2tEQUNqRCw4REFBQy9JLHlEQUFNQTt3Q0FDTDBKLFNBQVE7d0NBQ1IwQixNQUFLO3dDQUNMakMsU0FBUyxJQUFNcEgsbUJBQW1CO2tEQUVsQyw0RUFBQ2xCLDJNQUFDQTs0Q0FBQ2tJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtqQiw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDc0M7d0NBQUd0QyxXQUFVO2tEQUFtQzs7Ozs7O2tEQUNqRCw4REFBQ0M7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDQzs7a0VBQ0MsOERBQUNPO3dEQUFLUixXQUFVO2tFQUFnQjs7Ozs7O2tFQUNoQyw4REFBQ1E7d0RBQUtSLFdBQVU7a0VBQXNCakgsZ0JBQWdCVSxhQUFhOzs7Ozs7Ozs7Ozs7MERBRXJFLDhEQUFDd0c7O2tFQUNDLDhEQUFDTzt3REFBS1IsV0FBVTtrRUFBZ0I7Ozs7OztrRUFDaEMsOERBQUNRO3dEQUFLUixXQUFVO2tFQUFzQmpILGdCQUFnQm1DLFdBQVc7Ozs7Ozs7Ozs7OzswREFFbkUsOERBQUMrRTs7a0VBQ0MsOERBQUNPO3dEQUFLUixXQUFVO2tFQUFnQjs7Ozs7O2tFQUNoQyw4REFBQ1E7d0RBQUtSLFdBQVU7a0VBQXNCakgsZ0JBQWdCNkMsZUFBZTs7Ozs7Ozs7Ozs7OzBEQUV2RSw4REFBQ3FFOztrRUFDQyw4REFBQ087d0RBQUtSLFdBQVU7a0VBQWdCOzs7Ozs7a0VBQ2hDLDhEQUFDUTt3REFBS1IsV0FBVTtrRUFBc0JqSCxnQkFBZ0JxRixpQkFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNN0UsOERBQUM2QjtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNzQzt3Q0FBR3RDLFdBQVU7a0RBQW1DOzs7Ozs7a0RBQ2pELDhEQUFDQzt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNDOztrRUFDQyw4REFBQ087d0RBQUtSLFdBQVU7a0VBQWdCOzs7Ozs7a0VBQ2hDLDhEQUFDUTt3REFBS1IsV0FBVTtrRUFBc0I1RixZQUFZLENBQUNyQixnQkFBZ0JZLFVBQVUsQ0FBQyxJQUFJWixnQkFBZ0JZLFVBQVUsSUFBSTs7Ozs7Ozs7Ozs7OzBEQUVsSCw4REFBQ3NHOztrRUFDQyw4REFBQ087d0RBQUtSLFdBQVU7a0VBQWdCOzs7Ozs7a0VBQ2hDLDhEQUFDUTt3REFBS1IsV0FBVTs7NERBQXNCakgsZ0JBQWdCNEUsVUFBVTs0REFBQzs7Ozs7Ozs7Ozs7OzswREFFbkUsOERBQUNzQzs7a0VBQ0MsOERBQUNPO3dEQUFLUixXQUFVO2tFQUFnQjs7Ozs7O2tFQUNoQyw4REFBQ1E7d0RBQUtSLFdBQVU7a0VBQ2IsSUFBSWpELEtBQUtoRSxnQkFBZ0JpRSxVQUFVLEVBQUVpRixrQkFBa0IsQ0FBQzs7Ozs7Ozs7Ozs7OzBEQUc3RCw4REFBQ2hDOztrRUFDQyw4REFBQ087d0RBQUtSLFdBQVU7a0VBQWdCOzs7Ozs7a0VBQ2hDLDhEQUFDUTt3REFBS1IsV0FBVTtrRUFDYixJQUFJakQsS0FBS2hFLGdCQUFnQm1FLFFBQVEsRUFBRStFLGtCQUFrQixDQUFDOzs7Ozs7Ozs7Ozs7NENBRzFEbEosZ0JBQWdCdUYsZUFBZSxrQkFDOUIsOERBQUMyQjtnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUNRO3dEQUFLUixXQUFVO2tFQUFnQjs7Ozs7O2tFQUNoQyw4REFBQ1E7d0RBQUtSLFdBQVU7a0VBQXNCakgsZ0JBQWdCdUYsZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUkzRSw4REFBQzJCO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUtSLFdBQVU7MERBQXdCOzs7Ozs7MERBQ3hDLDhEQUFDRTtnREFBRUYsV0FBVTswREFBc0JqSCxnQkFBZ0I2RSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBSzVEN0UsZ0JBQWdCeUUsV0FBVyxJQUFJekUsZ0JBQWdCeUUsV0FBVyxDQUFDZSxNQUFNLEdBQUcsbUJBQ25FLDhEQUFDMEI7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDc0M7d0NBQUd0QyxXQUFVOzswREFDWiw4REFBQ3JJLDJNQUFRQTtnREFBQ3FJLFdBQVU7Ozs7Ozs0Q0FBaUM7Ozs7Ozs7a0RBR3ZELDhEQUFDQzt3Q0FBSUQsV0FBVTtrREFDWmpILGdCQUFnQnlFLFdBQVcsQ0FBQ2IsR0FBRyxDQUFDLENBQUNpRyxZQUFZQztnREFFdEJDOzRDQUR0QixNQUFNQSxXQUFXRixXQUFXRyxLQUFLLENBQUMsS0FBS0MsR0FBRyxNQUFNLEtBQWUsT0FBVkgsUUFBUTs0Q0FDN0QsTUFBTUksZ0JBQWdCSCxFQUFBQSxzQkFBQUEsU0FBU0MsS0FBSyxDQUFDLEtBQUtDLEdBQUcsZ0JBQXZCRiwwQ0FBQUEsb0JBQTJCOUQsV0FBVyxPQUFNOzRDQUVsRSxXQUFXOzRDQUNYLElBQUlrRSxXQUFXOzRDQUNmLElBQUk7Z0RBQUM7Z0RBQU87Z0RBQVE7Z0RBQU87Z0RBQU87NkNBQU8sQ0FBQ2pFLFFBQVEsQ0FBQ2dFLGdCQUFnQjtnREFDakVDLFdBQVc7NENBQ2IsT0FBTyxJQUFJRCxrQkFBa0IsT0FBTztnREFDbENDLFdBQVc7NENBQ2IsT0FBTyxJQUFJO2dEQUFDO2dEQUFPOzZDQUFPLENBQUNqRSxRQUFRLENBQUNnRSxnQkFBZ0I7Z0RBQ2xEQyxXQUFXOzRDQUNiOzRDQUVBLHFCQUNFLDhEQUFDakQ7Z0RBQWdCRCxXQUFVOztrRUFDekIsOERBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ1E7Z0VBQUtSLFdBQVU7MEVBQVlrRDs7Ozs7OzBFQUM1Qiw4REFBQ2pEOztrRkFDQyw4REFBQ0M7d0VBQUVGLFdBQVU7a0ZBQXFDOEM7Ozs7OztrRkFDbEQsOERBQUM1Qzt3RUFBRUYsV0FBVTtrRkFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFHekMsOERBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQy9JLHlEQUFNQTtnRUFDTDBKLFNBQVE7Z0VBQ1IwQixNQUFLO2dFQUNMakMsU0FBUyxJQUFNcEUsT0FBT21ILElBQUksQ0FBQyx3QkFBbUMsT0FBWFAsYUFBYztnRUFDakU1QyxXQUFVOztrRkFFViw4REFBQ3BJLDJNQUFHQTt3RUFBQ29JLFdBQVU7Ozs7OztvRUFBaUI7Ozs7Ozs7MEVBR2xDLDhEQUFDL0kseURBQU1BO2dFQUNMMEosU0FBUTtnRUFDUjBCLE1BQUs7Z0VBQ0xqQyxTQUFTO29FQUNQLE1BQU1nRCxPQUFPQyxTQUFTQyxhQUFhLENBQUM7b0VBQ3BDRixLQUFLN0MsSUFBSSxHQUFHLHdCQUFtQyxPQUFYcUM7b0VBQ3BDUSxLQUFLRyxRQUFRLEdBQUdUO29FQUNoQk8sU0FBUy9ELElBQUksQ0FBQ2tFLFdBQVcsQ0FBQ0o7b0VBQzFCQSxLQUFLSyxLQUFLO29FQUNWSixTQUFTL0QsSUFBSSxDQUFDb0UsV0FBVyxDQUFDTjtnRUFDNUI7Z0VBQ0FwRCxXQUFVOztrRkFFViw4REFBQzlILDJNQUFRQTt3RUFBQzhILFdBQVU7Ozs7OztvRUFBaUI7Ozs7Ozs7Ozs7Ozs7OytDQS9CakM2Qzs7Ozs7d0NBcUNkOzs7Ozs7Ozs7Ozs7MENBTU4sOERBQUM1QztnQ0FBSUQsV0FBVyx3QkFFYixPQUZxQ2pILGdCQUFnQlcsTUFBTSxLQUFLLGFBQWEsZ0JBQzlFWCxnQkFBZ0JXLE1BQU0sS0FBSyxhQUFhLGNBQWM7O2tEQUV0RCw4REFBQzRJO3dDQUFHdEMsV0FBVTtrREFBbUM7Ozs7OztrREFDakQsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUtSLFdBQVcsOEVBQXFILE9BQXZDSCxlQUFlOUcsZ0JBQWdCVyxNQUFNOztvREFDaklxRyxjQUFjaEgsZ0JBQWdCVyxNQUFNO2tFQUNyQyw4REFBQzhHO3dEQUFLUixXQUFVO2tFQUFRRixjQUFjL0csZ0JBQWdCVyxNQUFNOzs7Ozs7Ozs7Ozs7MERBRTlELDhEQUFDOEc7Z0RBQUtSLFdBQVU7O29EQUF3QjtvREFDakMsSUFBSWpELEtBQUtoRSxnQkFBZ0IrRSxVQUFVLElBQUkvRSxnQkFBZ0I4RSxZQUFZLEVBQUU4RixjQUFjLENBQUM7Ozs7Ozs7Ozs7Ozs7b0NBRzVGNUssZ0JBQWdCZ0YsV0FBVyxrQkFDMUIsOERBQUNrQzt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNRO2dEQUFLUixXQUFVOzBEQUFnQjs7Ozs7OzBEQUNoQyw4REFBQ1E7Z0RBQUtSLFdBQVU7MERBQXNCakgsZ0JBQWdCZ0YsV0FBVzs7Ozs7OzBEQUNqRSw4REFBQ3lDO2dEQUFLUixXQUFVOzBEQUFxQjs7Ozs7OzBEQUNyQyw4REFBQ1E7Z0RBQUtSLFdBQVU7MERBQ2JqSCxnQkFBZ0JrRixXQUFXLElBQUksSUFBSWxCLEtBQUtoRSxnQkFBZ0JrRixXQUFXLEVBQUUwRixjQUFjLENBQUM7Ozs7Ozs7Ozs7OztvQ0FJMUY1SyxnQkFBZ0JtRixnQkFBZ0Isa0JBQy9CLDhEQUFDK0I7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDUTtnREFBS1IsV0FBVTswREFBbUM7Ozs7OzswREFDbkQsOERBQUNFO2dEQUFFRixXQUFVOzBEQUE2QmpILGdCQUFnQm1GLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU1oRiw4REFBQytCO2dDQUFJRCxXQUFVOztvQ0FDWmpILGdCQUFnQlcsTUFBTSxLQUFLLDJCQUMxQjs7MERBQ0UsOERBQUN6Qyx5REFBTUE7Z0RBQ0wwSixTQUFRO2dEQUNSUCxTQUFTO29EQUNQaEgsa0JBQWtCO29EQUNsQkYscUJBQXFCO2dEQUN2QjtnREFDQThHLFdBQVU7O2tFQUVWLDhEQUFDbkksMk1BQUtBO3dEQUFDbUksV0FBVTs7Ozs7O2tFQUNqQiw4REFBQ1E7a0VBQUs7Ozs7Ozs7Ozs7OzswREFFUiw4REFBQ3ZKLHlEQUFNQTtnREFDTDBKLFNBQVE7Z0RBQ1JQLFNBQVM7b0RBQ1BoSCxrQkFBa0I7b0RBQ2xCRixxQkFBcUI7Z0RBQ3ZCO2dEQUNBOEcsV0FBVTs7a0VBRVYsOERBQUNsSSwyTUFBQ0E7d0RBQUNrSSxXQUFVOzs7Ozs7a0VBQ2IsOERBQUNRO2tFQUFLOzs7Ozs7Ozs7Ozs7OztrREFJWiw4REFBQ3ZKLHlEQUFNQTt3Q0FDTDBKLFNBQVE7d0NBQ1JQLFNBQVMsSUFBTXBILG1CQUFtQjtrREFDbkM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVakI7R0E5bkN3Qlo7O1FBQ3NCdkIsMERBQU9BO1FBQ3BDRyxzREFBU0E7OztLQUZGb0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9hZG1pbi9sZWF2ZS1hcHByb3ZhbC9wYWdlLnRzeD83NzRlIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2NvbnRleHRzL0F1dGhDb250ZXh0J1xuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IGxvZ2luIH0gZnJvbSAnQC9saWIvYXBpLWNsaWVudCdcbmltcG9ydCB7XG4gIEFycm93TGVmdCxcbiAgQ2xvY2ssXG4gIFBsdXNDaXJjbGUsXG4gIENoZWNrQ2lyY2xlLFxuICBYQ2lyY2xlLFxuICBDYWxlbmRhcixcbiAgVXNlcixcbiAgRmlsZVRleHQsXG4gIEV5ZSxcbiAgQ2hlY2ssXG4gIFgsXG4gIEFsZXJ0Q2lyY2xlLFxuICBTZWFyY2gsXG4gIEZpbHRlcixcbiAgRG93bmxvYWQsXG4gIFJlZnJlc2hDdyxcbiAgTWVzc2FnZVNxdWFyZSxcbiAgQnVpbGRpbmdcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgTGVhdmVSZXF1ZXN0IHtcbiAgaWQ6IG51bWJlclxuICBlbXBsb3llZV9pZDogc3RyaW5nXG4gIGVtcGxveWVlX25hbWU6IHN0cmluZ1xuICBkZXBhcnRtZW50X25hbWU6IHN0cmluZ1xuICBsZWF2ZV90eXBlOiBzdHJpbmdcbiAgc3RhcnRfZGF0ZTogc3RyaW5nXG4gIGVuZF9kYXRlOiBzdHJpbmdcbiAgZGF5c19jb3VudDogbnVtYmVyXG4gIHJlYXNvbjogc3RyaW5nXG4gIHN0YXR1czogJ3BlbmRpbmcnIHwgJ2FwcHJvdmVkJyB8ICdyZWplY3RlZCdcbiAgc3VibWl0dGVkX2F0OiBzdHJpbmdcbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIGFwcHJvdmVkX2J5Pzogc3RyaW5nXG4gIGFwcHJvdmVkX2F0Pzogc3RyaW5nXG4gIHJlamVjdGlvbl9yZWFzb24/OiBzdHJpbmdcbiAgZW1lcmdlbmN5X2NvbnRhY3Q6IHN0cmluZ1xuICBzdWJzdGl0dXRlX2lkPzogbnVtYmVyXG4gIHN1YnN0aXR1dGVfbmFtZT86IHN0cmluZ1xuICBhdHRhY2htZW50cz86IHN0cmluZ1tdXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExlYXZlQXBwcm92YWxQYWdlKCkge1xuICBjb25zdCB7IHVzZXIsIGxvZ2luOiBhdXRoTG9naW4sIGxvYWRpbmcgfSA9IHVzZUF1dGgoKVxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICBjb25zdCBbbGVhdmVSZXF1ZXN0cywgc2V0TGVhdmVSZXF1ZXN0c10gPSB1c2VTdGF0ZTxMZWF2ZVJlcXVlc3RbXT4oW10pXG4gIGNvbnN0IFtkYXRhTG9hZGluZywgc2V0RGF0YUxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFthdXRvTG9naW5Mb2FkaW5nLCBzZXRBdXRvTG9naW5Mb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2VsZWN0ZWRSZXF1ZXN0LCBzZXRTZWxlY3RlZFJlcXVlc3RdID0gdXNlU3RhdGU8TGVhdmVSZXF1ZXN0IHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3Nob3dBcHByb3ZhbE1vZGFsLCBzZXRTaG93QXBwcm92YWxNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2FwcHJvdmFsQWN0aW9uLCBzZXRBcHByb3ZhbEFjdGlvbl0gPSB1c2VTdGF0ZTwnYXBwcm92ZScgfCAncmVqZWN0Jz4oJ2FwcHJvdmUnKVxuICBjb25zdCBbYXBwcm92YWxDb21tZW50LCBzZXRBcHByb3ZhbENvbW1lbnRdID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFtzZWFyY2hQYXJhbXMsIHNldFNlYXJjaFBhcmFtc10gPSB1c2VTdGF0ZSh7XG4gICAgZW1wbG95ZWVfbmFtZTogJycsXG4gICAgc3RhdHVzOiAnJyxcbiAgICBsZWF2ZV90eXBlOiAnJ1xuICB9KVxuICBjb25zdCBbZmlsdGVyZWRSZXF1ZXN0cywgc2V0RmlsdGVyZWRSZXF1ZXN0c10gPSB1c2VTdGF0ZTxMZWF2ZVJlcXVlc3RbXT4oW10pXG4gIGNvbnN0IFtzdGF0cywgc2V0U3RhdHNdID0gdXNlU3RhdGUoe1xuICAgIHBlbmRpbmc6IDAsXG4gICAgdG9kYXlOZXc6IDAsXG4gICAgYXBwcm92ZWQ6IDAsXG4gICAgcmVqZWN0ZWQ6IDBcbiAgfSlcblxuICAvLyDoq4vlgYfpoZ7lnovlsI3mh4lcbiAgY29uc3QgbGVhdmVUeXBlTWFwOiB7IFtrZXk6IHN0cmluZ106IHN0cmluZyB9ID0ge1xuICAgIGFubnVhbDogJ+W5tOWBhycsXG4gICAgc2ljazogJ+eXheWBhycsXG4gICAgcGVyc29uYWw6ICfkuovlgYcnLFxuICAgIG1hdGVybml0eTogJ+eUouWBhycsXG4gICAgcGF0ZXJuaXR5OiAn6Zmq55Si5YGHJyxcbiAgICBtYXJyaWFnZTogJ+WpmuWBhycsXG4gICAgZnVuZXJhbDogJ+WWquWBhycsXG4gICAgY29tcGVuc2F0b3J5OiAn6KOc5LyRJyxcbiAgICBvdGhlcjogJ+WFtuS7lidcbiAgfVxuXG4gIC8vIOiHquWLleeZu+WFpeWKn+iDvSAtIOS9v+eUqOeuoeeQhuWToea4rOippuW4s+iZn1xuICBjb25zdCBoYW5kbGVBdXRvTG9naW4gPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0QXV0b0xvZ2luTG9hZGluZyh0cnVlKVxuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn6ZaL5aeL6Ieq5YuV55m75YWl566h55CG5ZOh5biz6JmfJylcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbG9naW4oe1xuICAgICAgICBlbXBsb3llZV9pZDogJ2FkbWluJyxcbiAgICAgICAgcGFzc3dvcmQ6ICdhZG1pbjEyMydcbiAgICAgIH0pXG5cbiAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS51c2VyKSB7XG4gICAgICAgIGNvbnN0IHVzZXJEYXRhID0gcmVzcG9uc2UudXNlclxuICAgICAgICBjb25zdCB1c2VyID0ge1xuICAgICAgICAgIGlkOiB1c2VyRGF0YS5lbXBsb3llZV9pZCxcbiAgICAgICAgICBuYW1lOiB1c2VyRGF0YS5lbXBsb3llZV9uYW1lLFxuICAgICAgICAgIGVtcGxveWVlX2lkOiB1c2VyRGF0YS5lbXBsb3llZV9jb2RlLFxuICAgICAgICAgIGRlcGFydG1lbnRfaWQ6IHVzZXJEYXRhLmRlcGFydG1lbnRfaWQsXG4gICAgICAgICAgcG9zaXRpb246IHVzZXJEYXRhLnJvbGVfaWQgPT09IDk5OSA/ICfns7vntbHnrqHnkIblk6EnIDogJ+WToeW3pScsXG4gICAgICAgICAgZW1haWw6IHVzZXJEYXRhLmVtYWlsLFxuICAgICAgICAgIHJvbGVfaWQ6IHVzZXJEYXRhLnJvbGVfaWQsXG4gICAgICAgICAgZGVwYXJ0bWVudF9uYW1lOiB1c2VyRGF0YS5kZXBhcnRtZW50X25hbWVcbiAgICAgICAgfVxuXG4gICAgICAgIGF1dGhMb2dpbih1c2VyKVxuICAgICAgICBjb25zb2xlLmxvZygn6Ieq5YuV55m75YWl5oiQ5YqfOicsIHVzZXIpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfoh6rli5XnmbvlhaXlpLHmlZc6JywgcmVzcG9uc2UpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+iHquWLleeZu+WFpemMr+iqpDonLCBlcnJvcilcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QXV0b0xvZ2luTG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICAvLyDpoIHpnaLovInlhaXmmYLoh6rli5XnmbvlhaXvvIjlpoLmnpzmnKrnmbvlhaXvvIlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWxvYWRpbmcgJiYgIXVzZXIpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfmqqLmuKzliLDmnKrnmbvlhaXvvIzln7fooYzoh6rli5XnmbvlhaUnKVxuICAgICAgaGFuZGxlQXV0b0xvZ2luKClcbiAgICB9XG4gIH0sIFtsb2FkaW5nLCB1c2VyXSlcblxuICAvLyDovInlhaXnnJ/lr6bnmoToq4vlgYfnlLPoq4vmlbjmk5pcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBmZXRjaExlYXZlUmVxdWVzdHMgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBzZXREYXRhTG9hZGluZyh0cnVlKVxuICAgICAgICBjb25zdCBhcGlCYXNlVXJsID0gd2luZG93LmxvY2F0aW9uLmhvc3RuYW1lID09PSAnbG9jYWxob3N0JyB8fCB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUgPT09ICcxMjcuMC4wLjEnXG4gICAgICAgICAgPyAnaHR0cDovL2xvY2FsaG9zdDo3MDcyJ1xuICAgICAgICAgIDogYGh0dHA6Ly8ke3dpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZX06NzA3MmBcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHthcGlCYXNlVXJsfS9hcGkvbGVhdmUtcmVxdWVzdHNgKVxuXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBsZWF2ZSByZXF1ZXN0cycpXG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICAgIGNvbnN0IHJhd1JlcXVlc3RzID0gZGF0YS5yZWNvcmRzIHx8IFtdXG5cbiAgICAgICAgLy8g5pig5bCEIEFQSSDmlbjmk5rliLDliY3nq6/mnJ/mnJvnmoTmoLzlvI9cbiAgICAgICAgY29uc3QgcmVxdWVzdHMgPSByYXdSZXF1ZXN0cy5tYXAoKGl0ZW06IGFueSkgPT4ge1xuICAgICAgICAgIC8vIOWuieWFqOeahOaXpeacn+ioiOeul1xuICAgICAgICAgIGxldCBkYXlzQ291bnQgPSAxXG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKGl0ZW0uc3RhcnRfZGF0ZSlcbiAgICAgICAgICAgIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZShpdGVtLmVuZF9kYXRlKVxuICAgICAgICAgICAgaWYgKCFpc05hTihzdGFydERhdGUuZ2V0VGltZSgpKSAmJiAhaXNOYU4oZW5kRGF0ZS5nZXRUaW1lKCkpKSB7XG4gICAgICAgICAgICAgIGRheXNDb3VudCA9IE1hdGguY2VpbCgoZW5kRGF0ZS5nZXRUaW1lKCkgLSBzdGFydERhdGUuZ2V0VGltZSgpKSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSkgKyAxXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2Fybign5pel5pyf6KiI566X6Yyv6KqkOicsIGl0ZW0uc3RhcnRfZGF0ZSwgaXRlbS5lbmRfZGF0ZSwgZXJyb3IpXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8g6JmV55CG6ZmE5Lu25pW45pOaXG4gICAgICAgICAgbGV0IGF0dGFjaG1lbnRzOiBzdHJpbmdbXSA9IFtdXG4gICAgICAgICAgaWYgKGl0ZW0uYXR0YWNobWVudHMpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGF0dGFjaG1lbnRzID0gdHlwZW9mIGl0ZW0uYXR0YWNobWVudHMgPT09ICdzdHJpbmcnXG4gICAgICAgICAgICAgICAgPyBKU09OLnBhcnNlKGl0ZW0uYXR0YWNobWVudHMpXG4gICAgICAgICAgICAgICAgOiBpdGVtLmF0dGFjaG1lbnRzXG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+ino+aekOmZhOS7tuaVuOaTmuWkseaVlzonLCBpdGVtLmF0dGFjaG1lbnRzLCBlcnJvcilcbiAgICAgICAgICAgICAgYXR0YWNobWVudHMgPSBbXVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBpZDogaXRlbS5pZCxcbiAgICAgICAgICAgIGVtcGxveWVlX2lkOiBpdGVtLmVtcGxveWVlX2NvZGUgfHwgaXRlbS5lbXBsb3llZV9pZCB8fCAnJyxcbiAgICAgICAgICAgIGVtcGxveWVlX25hbWU6IGl0ZW0uZW1wbG95ZWVfbmFtZSB8fCAn5pyq55+l5ZOh5belJyxcbiAgICAgICAgICAgIGRlcGFydG1lbnRfbmFtZTogaXRlbS5kZXBhcnRtZW50X25hbWUgfHwgJ+acquefpemDqOmWgCcsXG4gICAgICAgICAgICBsZWF2ZV90eXBlOiBpdGVtLmxlYXZlX3R5cGUsXG4gICAgICAgICAgICBzdGFydF9kYXRlOiBpdGVtLnN0YXJ0X2RhdGUsXG4gICAgICAgICAgICBlbmRfZGF0ZTogaXRlbS5lbmRfZGF0ZSxcbiAgICAgICAgICAgIGRheXNfY291bnQ6IGRheXNDb3VudCxcbiAgICAgICAgICAgIHJlYXNvbjogaXRlbS5yZWFzb24gfHwgJ+eEoScsXG4gICAgICAgICAgICBzdGF0dXM6IGl0ZW0uc3RhdHVzLFxuICAgICAgICAgICAgc3VibWl0dGVkX2F0OiBpdGVtLmNyZWF0ZWRfYXQsXG4gICAgICAgICAgICBjcmVhdGVkX2F0OiBpdGVtLmNyZWF0ZWRfYXQsXG4gICAgICAgICAgICBhcHByb3ZlZF9ieTogaXRlbS5hcHByb3Zlcl9uYW1lLFxuICAgICAgICAgICAgYXBwcm92ZWRfYXQ6IGl0ZW0uYXBwcm92ZWRfYXQsXG4gICAgICAgICAgICByZWplY3Rpb25fcmVhc29uOiBpdGVtLmNvbW1lbnQsXG4gICAgICAgICAgICBlbWVyZ2VuY3lfY29udGFjdDogaXRlbS5lbWVyZ2VuY3lfY29udGFjdCB8fCAn54ShJyxcbiAgICAgICAgICAgIHN1YnN0aXR1dGVfaWQ6IGl0ZW0uc3Vic3RpdHV0ZV9pZCxcbiAgICAgICAgICAgIHN1YnN0aXR1dGVfbmFtZTogaXRlbS5zdWJzdGl0dXRlX25hbWUgfHwgJ+eEoScsXG4gICAgICAgICAgICBhdHRhY2htZW50czogYXR0YWNobWVudHNcbiAgICAgICAgICB9XG4gICAgICAgIH0pXG5cbiAgICAgICAgY29uc29sZS5sb2coJ0FQSSDljp/lp4vmlbjmk5rnrYbmlbg6JywgcmF3UmVxdWVzdHMubGVuZ3RoKVxuICAgICAgICBjb25zb2xlLmxvZygn5pig5bCE5b6M5pW45pOa562G5pW4OicsIHJlcXVlc3RzLmxlbmd0aClcbiAgICAgICAgY29uc29sZS5sb2coJ+WJjTPnrYbmmKDlsITlvoznmoTmlbjmk5o6JywgcmVxdWVzdHMuc2xpY2UoMCwgMykpXG5cbiAgICAgICAgc2V0TGVhdmVSZXF1ZXN0cyhyZXF1ZXN0cylcbiAgICAgICAgc2V0RmlsdGVyZWRSZXF1ZXN0cyhyZXF1ZXN0cylcblxuICAgICAgICAvLyDoqIjnrpfntbHoqIjmlbjmk5pcbiAgICAgICAgY29uc3QgbmV3U3RhdHMgPSB7XG4gICAgICAgICAgcGVuZGluZzogcmVxdWVzdHMuZmlsdGVyKChyOiBMZWF2ZVJlcXVlc3QpID0+IHIuc3RhdHVzID09PSAncGVuZGluZycpLmxlbmd0aCxcbiAgICAgICAgICB0b2RheU5ldzogcmVxdWVzdHMuZmlsdGVyKChyOiBMZWF2ZVJlcXVlc3QpID0+XG4gICAgICAgICAgICBuZXcgRGF0ZShyLmNyZWF0ZWRfYXQgfHwgci5zdWJtaXR0ZWRfYXQpLnRvRGF0ZVN0cmluZygpID09PSBuZXcgRGF0ZSgpLnRvRGF0ZVN0cmluZygpXG4gICAgICAgICAgKS5sZW5ndGgsXG4gICAgICAgICAgYXBwcm92ZWQ6IHJlcXVlc3RzLmZpbHRlcigocjogTGVhdmVSZXF1ZXN0KSA9PiByLnN0YXR1cyA9PT0gJ2FwcHJvdmVkJykubGVuZ3RoLFxuICAgICAgICAgIHJlamVjdGVkOiByZXF1ZXN0cy5maWx0ZXIoKHI6IExlYXZlUmVxdWVzdCkgPT4gci5zdGF0dXMgPT09ICdyZWplY3RlZCcpLmxlbmd0aFxuICAgICAgICB9XG4gICAgICAgIHNldFN0YXRzKG5ld1N0YXRzKVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgbGVhdmUgcmVxdWVzdHM6JywgZXJyb3IpXG4gICAgICAgIC8vIOWmguaenEFQSeWkseaVl++8jOioree9ruepuuaVuOaTmlxuICAgICAgICBzZXRMZWF2ZVJlcXVlc3RzKFtdKVxuICAgICAgICBzZXRGaWx0ZXJlZFJlcXVlc3RzKFtdKVxuICAgICAgICBzZXRTdGF0cyh7IHBlbmRpbmc6IDAsIHRvZGF5TmV3OiAwLCBhcHByb3ZlZDogMCwgcmVqZWN0ZWQ6IDAgfSlcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldERhdGFMb2FkaW5nKGZhbHNlKVxuICAgICAgfVxuICAgIH1cblxuICAgIGZldGNoTGVhdmVSZXF1ZXN0cygpXG4gIH0sIFtdKVxuXG4gIC8vIOaQnOe0ouWSjOevqemBuFxuICBjb25zdCBoYW5kbGVTZWFyY2ggPSAoKSA9PiB7XG4gICAgbGV0IGZpbHRlcmVkID0gbGVhdmVSZXF1ZXN0c1xuXG4gICAgaWYgKHNlYXJjaFBhcmFtcy5lbXBsb3llZV9uYW1lKSB7XG4gICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihyZXF1ZXN0ID0+XG4gICAgICAgIHJlcXVlc3QuZW1wbG95ZWVfbmFtZT8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hQYXJhbXMuZW1wbG95ZWVfbmFtZS50b0xvd2VyQ2FzZSgpKVxuICAgICAgKVxuICAgIH1cbiAgICBpZiAoc2VhcmNoUGFyYW1zLnN0YXR1cykge1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIocmVxdWVzdCA9PiByZXF1ZXN0LnN0YXR1cyA9PT0gc2VhcmNoUGFyYW1zLnN0YXR1cylcbiAgICB9XG4gICAgaWYgKHNlYXJjaFBhcmFtcy5sZWF2ZV90eXBlKSB7XG4gICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihyZXF1ZXN0ID0+IHJlcXVlc3QubGVhdmVfdHlwZSA9PT0gc2VhcmNoUGFyYW1zLmxlYXZlX3R5cGUpXG4gICAgfVxuXG4gICAgc2V0RmlsdGVyZWRSZXF1ZXN0cyhmaWx0ZXJlZClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVJlc2V0ID0gKCkgPT4ge1xuICAgIHNldFNlYXJjaFBhcmFtcyh7XG4gICAgICBlbXBsb3llZV9uYW1lOiAnJyxcbiAgICAgIHN0YXR1czogJycsXG4gICAgICBsZWF2ZV90eXBlOiAnJ1xuICAgIH0pXG4gICAgc2V0RmlsdGVyZWRSZXF1ZXN0cyhsZWF2ZVJlcXVlc3RzKVxuICB9XG5cbiAgLy8g6JmV55CG5a+p5qC4XG4gIGNvbnN0IGhhbmRsZUFwcHJvdmFsID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghc2VsZWN0ZWRSZXF1ZXN0KSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICAvLyDoqr/nlKjnnJ/lr6bnmoRBUElcbiAgICAgIGNvbnN0IGFwaUJhc2VVcmwgPSB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUgPT09ICdsb2NhbGhvc3QnIHx8IHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZSA9PT0gJzEyNy4wLjAuMSdcbiAgICAgICAgPyAnaHR0cDovL2xvY2FsaG9zdDo3MDcyJ1xuICAgICAgICA6IGBodHRwOi8vJHt3aW5kb3cubG9jYXRpb24uaG9zdG5hbWV9OjcwNzJgXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2FwaUJhc2VVcmx9L2FwaS9hcHByb3ZhbC9sZWF2ZXMvJHtzZWxlY3RlZFJlcXVlc3QuaWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBhY3Rpb246IGFwcHJvdmFsQWN0aW9uLFxuICAgICAgICAgIGNvbW1lbnQ6IGFwcHJvdmFsQWN0aW9uID09PSAncmVqZWN0JyA/IGFwcHJvdmFsQ29tbWVudCA6IHVuZGVmaW5lZFxuICAgICAgICB9KVxuICAgICAgfSlcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgbGVhdmUgcmVxdWVzdCcpXG4gICAgICB9XG5cbiAgICAgIC8vIOmHjeaWsOi8ieWFpeaVuOaTmlxuICAgICAgY29uc3QgZmV0Y2hSZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2FwaUJhc2VVcmx9L2FwaS9sZWF2ZS1yZXF1ZXN0c2ApXG4gICAgICBpZiAoZmV0Y2hSZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgZmV0Y2hSZXNwb25zZS5qc29uKClcbiAgICAgICAgY29uc3QgcmF3UmVxdWVzdHMgPSBkYXRhLnJlY29yZHMgfHwgW11cblxuICAgICAgICAvLyDmmKDlsIQgQVBJIOaVuOaTmuWIsOWJjeerr+acn+acm+eahOagvOW8j1xuICAgICAgICBjb25zdCByZXF1ZXN0cyA9IHJhd1JlcXVlc3RzLm1hcCgoaXRlbTogYW55KSA9PiB7XG4gICAgICAgICAgLy8g5a6J5YWo55qE5pel5pyf6KiI566XXG4gICAgICAgICAgbGV0IGRheXNDb3VudCA9IDFcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUoaXRlbS5zdGFydF9kYXRlKVxuICAgICAgICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKGl0ZW0uZW5kX2RhdGUpXG4gICAgICAgICAgICBpZiAoIWlzTmFOKHN0YXJ0RGF0ZS5nZXRUaW1lKCkpICYmICFpc05hTihlbmREYXRlLmdldFRpbWUoKSkpIHtcbiAgICAgICAgICAgICAgZGF5c0NvdW50ID0gTWF0aC5jZWlsKChlbmREYXRlLmdldFRpbWUoKSAtIHN0YXJ0RGF0ZS5nZXRUaW1lKCkpIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKSArIDFcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKCfml6XmnJ/oqIjnrpfpjK/oqqQ6JywgaXRlbS5zdGFydF9kYXRlLCBpdGVtLmVuZF9kYXRlLCBlcnJvcilcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyDomZXnkIbpmYTku7bmlbjmk5pcbiAgICAgICAgICBsZXQgYXR0YWNobWVudHM6IHN0cmluZ1tdID0gW11cbiAgICAgICAgICBpZiAoaXRlbS5hdHRhY2htZW50cykge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgYXR0YWNobWVudHMgPSB0eXBlb2YgaXRlbS5hdHRhY2htZW50cyA9PT0gJ3N0cmluZydcbiAgICAgICAgICAgICAgICA/IEpTT04ucGFyc2UoaXRlbS5hdHRhY2htZW50cylcbiAgICAgICAgICAgICAgICA6IGl0ZW0uYXR0YWNobWVudHNcbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUud2Fybign6Kej5p6Q6ZmE5Lu25pW45pOa5aSx5pWXOicsIGl0ZW0uYXR0YWNobWVudHMsIGVycm9yKVxuICAgICAgICAgICAgICBhdHRhY2htZW50cyA9IFtdXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGlkOiBpdGVtLmlkLFxuICAgICAgICAgICAgZW1wbG95ZWVfaWQ6IGl0ZW0uZW1wbG95ZWVfY29kZSB8fCBpdGVtLmVtcGxveWVlX2lkIHx8ICcnLFxuICAgICAgICAgICAgZW1wbG95ZWVfbmFtZTogaXRlbS5lbXBsb3llZV9uYW1lIHx8ICfmnKrnn6Xlk6Hlt6UnLFxuICAgICAgICAgICAgZGVwYXJ0bWVudF9uYW1lOiBpdGVtLmRlcGFydG1lbnRfbmFtZSB8fCAn5pyq55+l6YOo6ZaAJyxcbiAgICAgICAgICAgIGxlYXZlX3R5cGU6IGl0ZW0ubGVhdmVfdHlwZSxcbiAgICAgICAgICAgIHN0YXJ0X2RhdGU6IGl0ZW0uc3RhcnRfZGF0ZSxcbiAgICAgICAgICAgIGVuZF9kYXRlOiBpdGVtLmVuZF9kYXRlLFxuICAgICAgICAgICAgZGF5c19jb3VudDogZGF5c0NvdW50LFxuICAgICAgICAgICAgcmVhc29uOiBpdGVtLnJlYXNvbiB8fCAn54ShJyxcbiAgICAgICAgICAgIHN0YXR1czogaXRlbS5zdGF0dXMsXG4gICAgICAgICAgICBzdWJtaXR0ZWRfYXQ6IGl0ZW0uY3JlYXRlZF9hdCxcbiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6IGl0ZW0uY3JlYXRlZF9hdCxcbiAgICAgICAgICAgIGFwcHJvdmVkX2J5OiBpdGVtLmFwcHJvdmVyX25hbWUsXG4gICAgICAgICAgICBhcHByb3ZlZF9hdDogaXRlbS5hcHByb3ZlZF9hdCxcbiAgICAgICAgICAgIHJlamVjdGlvbl9yZWFzb246IGl0ZW0uY29tbWVudCxcbiAgICAgICAgICAgIGVtZXJnZW5jeV9jb250YWN0OiBpdGVtLmVtZXJnZW5jeV9jb250YWN0IHx8ICfnhKEnLFxuICAgICAgICAgICAgc3Vic3RpdHV0ZV9pZDogaXRlbS5zdWJzdGl0dXRlX2lkLFxuICAgICAgICAgICAgc3Vic3RpdHV0ZV9uYW1lOiBpdGVtLnN1YnN0aXR1dGVfbmFtZSB8fCAn54ShJyxcbiAgICAgICAgICAgIGF0dGFjaG1lbnRzOiBhdHRhY2htZW50c1xuICAgICAgICAgIH1cbiAgICAgICAgfSlcblxuICAgICAgICBzZXRMZWF2ZVJlcXVlc3RzKHJlcXVlc3RzKVxuICAgICAgICBzZXRGaWx0ZXJlZFJlcXVlc3RzKHJlcXVlc3RzLmZpbHRlcigocmVxdWVzdDogTGVhdmVSZXF1ZXN0KSA9PiB7XG4gICAgICAgICAgbGV0IG1hdGNoID0gdHJ1ZVxuICAgICAgICAgIGlmIChzZWFyY2hQYXJhbXMuZW1wbG95ZWVfbmFtZSkge1xuICAgICAgICAgICAgbWF0Y2ggPSBtYXRjaCAmJiByZXF1ZXN0LmVtcGxveWVlX25hbWU/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUGFyYW1zLmVtcGxveWVlX25hbWUudG9Mb3dlckNhc2UoKSlcbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKHNlYXJjaFBhcmFtcy5zdGF0dXMpIHtcbiAgICAgICAgICAgIG1hdGNoID0gbWF0Y2ggJiYgcmVxdWVzdC5zdGF0dXMgPT09IHNlYXJjaFBhcmFtcy5zdGF0dXNcbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKHNlYXJjaFBhcmFtcy5sZWF2ZV90eXBlKSB7XG4gICAgICAgICAgICBtYXRjaCA9IG1hdGNoICYmIHJlcXVlc3QubGVhdmVfdHlwZSA9PT0gc2VhcmNoUGFyYW1zLmxlYXZlX3R5cGVcbiAgICAgICAgICB9XG4gICAgICAgICAgcmV0dXJuIG1hdGNoXG4gICAgICAgIH0pKVxuXG4gICAgICAgIC8vIOabtOaWsOe1seioiFxuICAgICAgICBjb25zdCBuZXdTdGF0cyA9IHtcbiAgICAgICAgICBwZW5kaW5nOiByZXF1ZXN0cy5maWx0ZXIoKHI6IExlYXZlUmVxdWVzdCkgPT4gci5zdGF0dXMgPT09ICdwZW5kaW5nJykubGVuZ3RoLFxuICAgICAgICAgIHRvZGF5TmV3OiByZXF1ZXN0cy5maWx0ZXIoKHI6IExlYXZlUmVxdWVzdCkgPT5cbiAgICAgICAgICAgIG5ldyBEYXRlKHIuY3JlYXRlZF9hdCB8fCByLnN1Ym1pdHRlZF9hdCkudG9EYXRlU3RyaW5nKCkgPT09IG5ldyBEYXRlKCkudG9EYXRlU3RyaW5nKClcbiAgICAgICAgICApLmxlbmd0aCxcbiAgICAgICAgICBhcHByb3ZlZDogcmVxdWVzdHMuZmlsdGVyKChyOiBMZWF2ZVJlcXVlc3QpID0+IHIuc3RhdHVzID09PSAnYXBwcm92ZWQnKS5sZW5ndGgsXG4gICAgICAgICAgcmVqZWN0ZWQ6IHJlcXVlc3RzLmZpbHRlcigocjogTGVhdmVSZXF1ZXN0KSA9PiByLnN0YXR1cyA9PT0gJ3JlamVjdGVkJykubGVuZ3RoXG4gICAgICAgIH1cbiAgICAgICAgc2V0U3RhdHMobmV3U3RhdHMpXG4gICAgICB9XG5cbiAgICAgIHNldFNob3dBcHByb3ZhbE1vZGFsKGZhbHNlKVxuICAgICAgc2V0U2VsZWN0ZWRSZXF1ZXN0KG51bGwpXG4gICAgICBzZXRBcHByb3ZhbENvbW1lbnQoJycpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGxlYXZlIHJlcXVlc3Q6JywgZXJyb3IpXG4gICAgICBhbGVydCgn5a+p5qC45aSx5pWX77yM6KuL56iN5b6M5YaN6KmmJylcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTdGF0dXNDb2xvciA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdwZW5kaW5nJzogcmV0dXJuICd0ZXh0LXdhcm5pbmctNjAwIGJnLXdhcm5pbmctNTAgYm9yZGVyLXdhcm5pbmctMjAwJ1xuICAgICAgY2FzZSAnYXBwcm92ZWQnOiByZXR1cm4gJ3RleHQtc3VjY2Vzcy02MDAgYmctc3VjY2Vzcy01MCBib3JkZXItc3VjY2Vzcy0yMDAnXG4gICAgICBjYXNlICdyZWplY3RlZCc6IHJldHVybiAndGV4dC1lcnJvci02MDAgYmctZXJyb3ItNTAgYm9yZGVyLWVycm9yLTIwMCdcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAndGV4dC1uZXV0cmFsLTYwMCBiZy1uZXV0cmFsLTUwIGJvcmRlci1uZXV0cmFsLTIwMCdcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTdGF0dXNUZXh0ID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ3BlbmRpbmcnOiByZXR1cm4gJ+W+heWvqeaguCdcbiAgICAgIGNhc2UgJ2FwcHJvdmVkJzogcmV0dXJuICflt7LmoLjlh4YnXG4gICAgICBjYXNlICdyZWplY3RlZCc6IHJldHVybiAn5bey5ouS57WVJ1xuICAgICAgZGVmYXVsdDogcmV0dXJuICfmnKrnn6UnXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0U3RhdHVzSWNvbiA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdwZW5kaW5nJzogcmV0dXJuIDxDbG9jayBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgIGNhc2UgJ2FwcHJvdmVkJzogcmV0dXJuIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgIGNhc2UgJ3JlamVjdGVkJzogcmV0dXJuIDxYQ2lyY2xlIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgZGVmYXVsdDogcmV0dXJuIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICB9XG4gIH1cblxuICBpZiAobG9hZGluZyB8fCBhdXRvTG9naW5Mb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB2aWEtaW5kaWdvLTUwIHRvLXB1cnBsZS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1pbmRpZ28tNjAwIG14LWF1dG8gbWItNFwiPjwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgIHthdXRvTG9naW5Mb2FkaW5nID8gJ+iHquWLleeZu+WFpeS4rS4uLicgOiAn6LyJ5YWl5LitLi4uJ31cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgaWYgKCF1c2VyIHx8IHVzZXIucm9sZV9pZCAhPT0gOTk5KSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8WENpcmNsZSBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgdGV4dC1yZWQtNTAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj7mrIrpmZDkuI3otrM8L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPuaCqOmcgOimgeeuoeeQhuWToeasiumZkOaJjeiDveioquWVj+atpOmggemdojwvcD5cbiAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvYWRtaW4nKX0+XG4gICAgICAgICAgICDov5Tlm57nrqHnkIblvozlj7BcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB2aWEtaW5kaWdvLTUwIHRvLXB1cnBsZS01MFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIHsvKiDwn46oIOmggemdouaomemhjCAtIOe1seS4gOeahOaomemhjOioreioiCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWluZGlnby01MDAgdmlhLXB1cnBsZS01MDAgdG8tcGluay01MDAgcm91bmRlZC0yeGwgcC02IHRleHQtd2hpdGUgc2hhZG93LTJ4bCBtYi02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgey8qIPCfk40g5bem5YG05qiZ6aGM5Y2AICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMFwiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCBtYi0yIHRleHQtd2hpdGVcIj7oq4vlgYflr6nmoLjnrqHnkIY8L2gxPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgey8qIPCflJkg6L+U5Zue5oyJ6YiVIC0g5ZyW5qiZK+aWh+Wtl+ioreioiCAqL31cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pblwiIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgcHgtMyBweS0xLjUgYmctd2hpdGUvMjAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLWxnIGhvdmVyOmJnLXdoaXRlLzMwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBncm91cCBib3JkZXIgYm9yZGVyLXdoaXRlLzMwXCI+XG4gICAgICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGUgZ3JvdXAtaG92ZXI6dGV4dC1pbmRpZ28tMTAwXCIgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgZ3JvdXAtaG92ZXI6dGV4dC1pbmRpZ28tMTAwXCI+6L+U5ZuePC9zcGFuPlxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtaW5kaWdvLTEwMCB0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj7nrqHnkIblvoXlr6nmoLjnmoToq4vlgYfnlLPoq4voiIflhbbku5blr6nmoLjkuovpoIU8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDwn5ONIOWPs+WBtOizh+ioiuWNgCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyB0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGVcIj7nrqHnkIblk6HmqKHlvI88L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1pbmRpZ28tMTAwXCI+6KuL5YGH5a+p5qC4PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy13aGl0ZS8yMCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtMnhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggcHktOFwiPlxuICAgICAgICB7Lyog6aCB6Z2i5qiZ6aGMICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLThcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPuiri+WBh+WvqeaguOeuoeeQhuS4reW/gzwvaDI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWdyYXktNjAwXCI+6JmV55CG5ZOh5bel6KuL5YGH55Sz6KuL77yM56K65L+d5bel5L2c5rWB56iL6aCG5pqiPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog57Wx6KiI5Y2h54mHICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTYgbWItOFwiPlxuICAgICAgICAgIHsvKiDlvoXlr6nmoLjnuL3mlbggKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS82MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtMnhsIHAtNiBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHNoYWRvdy1zb2Z0XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy13YXJuaW5nLTUwIHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdhcm5pbmctNjAwXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+e3N0YXRzLnBlbmRpbmd9PC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPuW+heWvqeaguDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDku4rml6XmlrDlop4gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS82MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtMnhsIHAtNiBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHNoYWRvdy1zb2Z0XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1ibHVlLTUwIHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8UGx1c0NpcmNsZSBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj57c3RhdHMudG9kYXlOZXd9PC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPuS7iuaXpeaWsOWinjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDlt7LmoLjlh4YgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS82MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtMnhsIHAtNiBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHNoYWRvdy1zb2Z0XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1zdWNjZXNzLTUwIHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXN1Y2Nlc3MtNjAwXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+e3N0YXRzLmFwcHJvdmVkfTwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj7lt7LmoLjlh4Y8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5bey5ouS57WVICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvNjAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLTJ4bCBwLTYgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCBzaGFkb3ctc29mdFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctZXJyb3ItNTAgcm91bmRlZC14bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxYQ2lyY2xlIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1lcnJvci02MDBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj57c3RhdHMucmVqZWN0ZWR9PC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPuW3suaLkue1lTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOafpeipouW3peWFt+ashCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS82MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtM3hsIHNoYWRvdy14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIG92ZXJmbG93LWhpZGRlbiBtYi04XCI+XG4gICAgICAgICAgey8qIOaomemhjOWNgOWfnyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNTAwLzUgdG8tcGluay01MDAvNSBweC04IHB5LTYgYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwLzYwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHVycGxlLTUwMCB0by1waW5rLTYwMCByb3VuZGVkLTJ4bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxGaWx0ZXIgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj7nr6npgbjmop3ku7Y8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG10LTFcIj7lv6vpgJ/mib7liLDnibnlrprnmoToq4vlgYfnlLPoq4s8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5sb2NhdGlvbi5yZWxvYWQoKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPumHjeaWsOi8ieWFpTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj7ljK/lh7rloLHooag8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5p+l6Kmi6KGo5ZauICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC04XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLTYgbWItNlwiPlxuICAgICAgICAgICAgICB7Lyog5ZOh5bel5aeT5ZCN5pCc5bCLICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktNzAwXCI+5ZOh5bel5aeT5ZCNPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHctNCBoLTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoUGFyYW1zLmVtcGxveWVlX25hbWV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoUGFyYW1zKHsgLi4uc2VhcmNoUGFyYW1zLCBlbXBsb3llZV9uYW1lOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLmkJzlsIvlk6Hlt6Xlp5PlkI0uLi5cIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS01MC84MCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQteGwgcHgtNCBweS0zIHBsLTExIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7Lyog5a+p5qC454uA5oWL56+p6YG4ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZ3JheS03MDBcIj7lr6nmoLjni4DmhYs8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hQYXJhbXMuc3RhdHVzfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hQYXJhbXMoeyAuLi5zZWFyY2hQYXJhbXMsIHN0YXR1czogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS01MC84MCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQteGwgcHgtNCBweS0zIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWdyZWVuLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIHRleHQtc21cIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj7miYDmnInni4DmhYs8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJwZW5kaW5nXCI+5b6F5a+p5qC4PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYXBwcm92ZWRcIj7lt7LmoLjlh4Y8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJyZWplY3RlZFwiPuW3suaLkue1lTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7Lyog6KuL5YGH6aGe5Z6L56+p6YG4ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXB1cnBsZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktNzAwXCI+6KuL5YGH6aGe5Z6LPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoUGFyYW1zLmxlYXZlX3R5cGV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFBhcmFtcyh7IC4uLnNlYXJjaFBhcmFtcywgbGVhdmVfdHlwZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS01MC84MCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQteGwgcHgtNCBweS0zIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXB1cnBsZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+5omA5pyJ6aGe5Z6LPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICB7T2JqZWN0LmVudHJpZXMobGVhdmVUeXBlTWFwKS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17a2V5fSB2YWx1ZT17a2V5fT57dmFsdWV9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIOaTjeS9nOaMiemIlSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB0LTYgYm9yZGVyLXQgYm9yZGVyLWdyYXktMTAwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwicHJpbWFyeVwiIG9uQ2xpY2s9e2hhbmRsZVNlYXJjaH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+5p+l6KmiPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9e2hhbmRsZVJlc2V0fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj7ph43nva48L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG5cblxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgIOWFseaJvuWIsCB7ZmlsdGVyZWRSZXF1ZXN0cy5sZW5ndGh9IOethueUs+iri1xuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog6KuL5YGH55Sz6KuL5YiX6KGoICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzYwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC0zeGwgc2hhZG93LXhsIGJvcmRlciBib3JkZXItd2hpdGUvMjAgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgey8qIOaomemhjOWNgOWfnyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MDAvNSB0by1ibHVlLTUwMC81IHB4LTggcHktNiBib3JkZXItYiBib3JkZXItZ3JheS0xMDAvNjBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmVlbi01MDAgdG8tYmx1ZS02MDAgcm91bmRlZC0yeGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+6KuL5YGH55Sz6KuL5YiX6KGoPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBtdC0xXCI+566h55CG5ZOh5bel55qE6KuL5YGH55Sz6KuLPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAge2RhdGFMb2FkaW5nID8gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweS0xNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYm9yZGVyLTQgYm9yZGVyLWJsdWUtMjAwIGJvcmRlci10LWJsdWUtNjAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gbXgtYXV0byBtYi00XCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPui8ieWFpeiri+WBh+eUs+iri+S4rS4uLjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApIDogZmlsdGVyZWRSZXF1ZXN0cy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgey8qIOahjOmdoueJiOihqOagvCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6YmxvY2sgb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MDAgdG8tYmx1ZS02MDAgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktNCB0ZXh0LWxlZnQgdGV4dC1zbSBmb250LWJvbGQgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+55Sz6KuL5Lq6PC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtbGVmdCB0ZXh0LXNtIGZvbnQtYm9sZCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj7oq4vlgYfpoZ7lnos8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1sZWZ0IHRleHQtc20gZm9udC1ib2xkIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPuiri+WBh+aXpeacnzwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktNCB0ZXh0LWxlZnQgdGV4dC1zbSBmb250LWJvbGQgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+5aSp5pW4PC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtbGVmdCB0ZXh0LXNtIGZvbnQtYm9sZCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj7ni4DmhYs8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1sZWZ0IHRleHQtc20gZm9udC1ib2xkIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPueUs+iri+aZgumWkzwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktNCB0ZXh0LWxlZnQgdGV4dC1zbSBmb250LWJvbGQgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+5pON5L2cPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZFJlcXVlc3RzLm1hcCgocmVxdWVzdCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDx0ciBrZXk9e3JlcXVlc3QuaWR9IGNsYXNzTmFtZT1cImhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNDAwIHRvLXB1cnBsZS01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3JlcXVlc3QuZW1wbG95ZWVfbmFtZT8uY2hhckF0KDApIHx8ICc/J31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e3JlcXVlc3QuZW1wbG95ZWVfbmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e3JlcXVlc3QuZW1wbG95ZWVfaWR9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPntyZXF1ZXN0LmRlcGFydG1lbnRfbmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWJsdWUtNTAgdGV4dC1ibHVlLTYwMCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2xlYXZlVHlwZU1hcFtyZXF1ZXN0LmxlYXZlX3R5cGVdIHx8IHJlcXVlc3QubGVhdmVfdHlwZSB8fCAn5pyq55+lJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+e25ldyBEYXRlKHJlcXVlc3Quc3RhcnRfZGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCd6aC1UVycpfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cmVxdWVzdC5zdGFydF9kYXRlICE9PSByZXF1ZXN0LmVuZF9kYXRlICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg6IezIHtuZXcgRGF0ZShyZXF1ZXN0LmVuZF9kYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoJ3poLVRXJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3JlcXVlc3QuZGF5c19jb3VudH0g5aSpXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJvcmRlciAke2dldFN0YXR1c0NvbG9yKHJlcXVlc3Quc3RhdHVzKX1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0U3RhdHVzSWNvbihyZXF1ZXN0LnN0YXR1cyl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMVwiPntnZXRTdGF0dXNUZXh0KHJlcXVlc3Quc3RhdHVzKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtc20gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUocmVxdWVzdC5jcmVhdGVkX2F0IHx8IHJlcXVlc3Quc3VibWl0dGVkX2F0KS50b0xvY2FsZURhdGVTdHJpbmcoJ3poLVRXJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge25ldyBEYXRlKHJlcXVlc3QuY3JlYXRlZF9hdCB8fCByZXF1ZXN0LnN1Ym1pdHRlZF9hdCkudG9Mb2NhbGVUaW1lU3RyaW5nKCd6aC1UVycsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbnV0ZTogJzItZGlnaXQnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRSZXF1ZXN0KHJlcXVlc3QpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3JlcXVlc3Quc3RhdHVzID09PSAncGVuZGluZycgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFJlcXVlc3QocmVxdWVzdClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEFwcHJvdmFsQWN0aW9uKCdhcHByb3ZlJylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dBcHByb3ZhbE1vZGFsKHRydWUpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVjayBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRSZXF1ZXN0KHJlcXVlc3QpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRBcHByb3ZhbEFjdGlvbigncmVqZWN0JylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dBcHByb3ZhbE1vZGFsKHRydWUpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1yZWQtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgICAgIDwvdGFibGU+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiDmiYvmqZ/niYjljaHniYcgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuIHNwYWNlLXktNCBwLTZcIj5cbiAgICAgICAgICAgICAgICB7ZmlsdGVyZWRSZXF1ZXN0cy5tYXAoKHJlcXVlc3QpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtyZXF1ZXN0LmlkfSBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLTJ4bCBwLTYgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBzaGFkb3ctc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNDAwIHRvLXB1cnBsZS01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW0gdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtyZXF1ZXN0LmVtcGxveWVlX25hbWU/LmNoYXJBdCgwKSB8fCAnPyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPntyZXF1ZXN0LmVtcGxveWVlX25hbWV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e3JlcXVlc3QuZGVwYXJ0bWVudF9uYW1lfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yLjUgcHktMC41IHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJvcmRlciAke2dldFN0YXR1c0NvbG9yKHJlcXVlc3Quc3RhdHVzKX1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNJY29uKHJlcXVlc3Quc3RhdHVzKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTFcIj57Z2V0U3RhdHVzVGV4dChyZXF1ZXN0LnN0YXR1cyl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00IHRleHQtc20gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+6aGe5Z6LOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPntsZWF2ZVR5cGVNYXBbcmVxdWVzdC5sZWF2ZV90eXBlXSB8fCByZXF1ZXN0LmxlYXZlX3R5cGUgfHwgJ+acquefpSd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+5aSp5pW4Ojwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPntyZXF1ZXN0LmRheXNfY291bnR9IOWkqTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPumWi+Wnizo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge25ldyBEYXRlKHJlcXVlc3Quc3RhcnRfZGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCd6aC1UVycpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+57WQ5p2fOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUocmVxdWVzdC5lbmRfZGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCd6aC1UVycpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTQgcC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbVwiPuWOn+WboDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMCB0ZXh0LXNtIG10LTFcIj57cmVxdWVzdC5yZWFzb259PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwdC00IGJvcmRlci10IGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICDnlLPoq4vmlrwge25ldyBEYXRlKHJlcXVlc3QuY3JlYXRlZF9hdCB8fCByZXF1ZXN0LnN1Ym1pdHRlZF9hdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCd6aC1UVycpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkUmVxdWVzdChyZXF1ZXN0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAge3JlcXVlc3Quc3RhdHVzID09PSAncGVuZGluZycgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFJlcXVlc3QocmVxdWVzdClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0QXBwcm92YWxBY3Rpb24oJ2FwcHJvdmUnKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93QXBwcm92YWxNb2RhbCh0cnVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2sgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkUmVxdWVzdChyZXF1ZXN0KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRBcHByb3ZhbEFjdGlvbigncmVqZWN0JylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2hvd0FwcHJvdmFsTW9kYWwodHJ1ZSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXJlZC02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTZcIj5cbiAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cInctMTYgaC0xNiB0ZXh0LWdyYXktMzAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPuafpeeEoeiri+WBh+eUs+irizwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7oq4voqr/mlbTmkJzlsIvmop3ku7bmiJbnrYnlvoXmlrDnmoTnlLPoq4s8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbWFpbj5cblxuICAgICAgey8qIOWvqeaguE1vZGFsICovfVxuICAgICAge3Nob3dBcHByb3ZhbE1vZGFsICYmIHNlbGVjdGVkUmVxdWVzdCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTAgcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLTN4bCBzaGFkb3cteGwgbWF4LXctMnhsIHctZnVsbCBtYXgtaC1bOTB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtOFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICB7YXBwcm92YWxBY3Rpb24gPT09ICdhcHByb3ZlJyA/ICfmoLjlh4boq4vlgYfnlLPoq4snIDogJ+aLkue1leiri+WBh+eUs+iriyd9XG4gICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dBcHByb3ZhbE1vZGFsKGZhbHNlKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIOeUs+iri+ips+aDhSAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQtMnhsIHAtNiBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+55Sz6KuL6Kmz5oOFPC9oND5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPueUs+iri+S6ujo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPntzZWxlY3RlZFJlcXVlc3QuZW1wbG95ZWVfbmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7pg6jploA6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS05MDBcIj57c2VsZWN0ZWRSZXF1ZXN0LmRlcGFydG1lbnRfbmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7oq4vlgYfpoZ7lnos6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS05MDBcIj57bGVhdmVUeXBlTWFwW3NlbGVjdGVkUmVxdWVzdC5sZWF2ZV90eXBlXSB8fCBzZWxlY3RlZFJlcXVlc3QubGVhdmVfdHlwZSB8fCAn5pyq55+lJ308L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7oq4vlgYflpKnmlbg6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS05MDBcIj57c2VsZWN0ZWRSZXF1ZXN0LmRheXNfY291bnR9IOWkqTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPumWi+Wni+aXpeacnzo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtuZXcgRGF0ZShzZWxlY3RlZFJlcXVlc3Quc3RhcnRfZGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCd6aC1UVycpfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7ntZDmnZ/ml6XmnJ86PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUoc2VsZWN0ZWRSZXF1ZXN0LmVuZF9kYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoJ3poLVRXJyl9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAge3NlbGVjdGVkUmVxdWVzdC5zdWJzdGl0dXRlX25hbWUgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7ku6PnkIbkuro6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPntzZWxlY3RlZFJlcXVlc3Quc3Vic3RpdHV0ZV9uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPue3iuaApeiBr+e1oTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPntzZWxlY3RlZFJlcXVlc3QuZW1lcmdlbmN5X2NvbnRhY3R9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00XCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc21cIj7oq4vlgYfljp/lm6A6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMCBtdC0xXCI+e3NlbGVjdGVkUmVxdWVzdC5yZWFzb259PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7Lyog5a+p5qC45oSP6KaLICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIHthcHByb3ZhbEFjdGlvbiA9PT0gJ2FwcHJvdmUnID8gJ+aguOWHhuaEj+imi++8iOWPr+mBuO+8iScgOiAn5ouS57WV5Y6f5Zug77yI5b+F5aGr77yJJ31cbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2FwcHJvdmFsQ29tbWVudH1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0QXBwcm92YWxDb21tZW50KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0zIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC14bCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDAgcmVzaXplLW5vbmVcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e1xuICAgICAgICAgICAgICAgICAgICBhcHByb3ZhbEFjdGlvbiA9PT0gJ2FwcHJvdmUnXG4gICAgICAgICAgICAgICAgICAgICAgPyAn6Ly45YWl5qC45YeG5oSP6KaLLi4uJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ+iri+iqquaYjuaLkue1leWOn+WboC4uLidcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkPXthcHByb3ZhbEFjdGlvbiA9PT0gJ3JlamVjdCd9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIOaTjeS9nOaMiemIlSAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWVuZCBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QXBwcm92YWxNb2RhbChmYWxzZSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAg5Y+W5raIXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD17YXBwcm92YWxBY3Rpb24gPT09ICdhcHByb3ZlJyA/ICdzdWNjZXNzJyA6ICdlcnJvcid9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBcHByb3ZhbH1cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXthcHByb3ZhbEFjdGlvbiA9PT0gJ3JlamVjdCcgJiYgIWFwcHJvdmFsQ29tbWVudC50cmltKCl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHthcHByb3ZhbEFjdGlvbiA9PT0gJ2FwcHJvdmUnID8gKFxuICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgIDxDaGVjayBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7moLjlh4bnlLPoq4s8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7mi5LntZXnlLPoq4s8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog6Kmz5oOFTW9kYWwgKi99XG4gICAgICB7c2VsZWN0ZWRSZXF1ZXN0ICYmICFzaG93QXBwcm92YWxNb2RhbCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTAgcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLTN4bCBzaGFkb3cteGwgbWF4LXctMnhsIHctZnVsbCBtYXgtaC1bOTB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtOFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+6KuL5YGH55Sz6KuL6Kmz5oOFPC9oMz5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkUmVxdWVzdChudWxsKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIOeUs+iri+S6uuizh+ioiiAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQtMnhsIHAtNiBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+55Sz6KuL5Lq66LOH6KiKPC9oND5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPuWnk+WQjTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPntzZWxlY3RlZFJlcXVlc3QuZW1wbG95ZWVfbmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7lk6Hlt6Xnt6jomZ86PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS05MDBcIj57c2VsZWN0ZWRSZXF1ZXN0LmVtcGxveWVlX2lkfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPumDqOmWgDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPntzZWxlY3RlZFJlcXVlc3QuZGVwYXJ0bWVudF9uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPue3iuaApeiBr+e1oTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPntzZWxlY3RlZFJlcXVlc3QuZW1lcmdlbmN5X2NvbnRhY3R9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiDoq4vlgYfos4foqIogKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCByb3VuZGVkLTJ4bCBwLTYgbWItNlwiPlxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPuiri+WBh+izh+ioijwvaDQ+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00IHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7oq4vlgYfpoZ7lnos6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS05MDBcIj57bGVhdmVUeXBlTWFwW3NlbGVjdGVkUmVxdWVzdC5sZWF2ZV90eXBlXSB8fCBzZWxlY3RlZFJlcXVlc3QubGVhdmVfdHlwZSB8fCAn5pyq55+lJ308L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7oq4vlgYflpKnmlbg6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS05MDBcIj57c2VsZWN0ZWRSZXF1ZXN0LmRheXNfY291bnR9IOWkqTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPumWi+Wni+aXpeacnzo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtuZXcgRGF0ZShzZWxlY3RlZFJlcXVlc3Quc3RhcnRfZGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCd6aC1UVycpfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7ntZDmnZ/ml6XmnJ86PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUoc2VsZWN0ZWRSZXF1ZXN0LmVuZF9kYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoJ3poLVRXJyl9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAge3NlbGVjdGVkUmVxdWVzdC5zdWJzdGl0dXRlX25hbWUgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+5Luj55CG5Lq6Ojwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS05MDBcIj57c2VsZWN0ZWRSZXF1ZXN0LnN1YnN0aXR1dGVfbmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbVwiPuiri+WBh+WOn+WboDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwIG10LTFcIj57c2VsZWN0ZWRSZXF1ZXN0LnJlYXNvbn08L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiDoq4vlgYfmlofku7bpmYTku7YgKi99XG4gICAgICAgICAgICAgIHtzZWxlY3RlZFJlcXVlc3QuYXR0YWNobWVudHMgJiYgc2VsZWN0ZWRSZXF1ZXN0LmF0dGFjaG1lbnRzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcHVycGxlLTUwIHJvdW5kZWQtMnhsIHAtNiBtYi02XCI+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1wdXJwbGUtNjAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICDoq4vlgYfmlofku7bpmYTku7ZcbiAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRSZXF1ZXN0LmF0dGFjaG1lbnRzLm1hcCgoYXR0YWNobWVudCwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9IGF0dGFjaG1lbnQuc3BsaXQoJy8nKS5wb3AoKSB8fCBg6ZmE5Lu2JHtpbmRleCArIDF9YFxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbGVFeHRlbnNpb24gPSBmaWxlTmFtZS5zcGxpdCgnLicpLnBvcCgpPy50b0xvd2VyQ2FzZSgpIHx8ICcnXG5cbiAgICAgICAgICAgICAgICAgICAgICAvLyDliKTmlrfmlofku7bpoZ7lnovlnJbmqJlcbiAgICAgICAgICAgICAgICAgICAgICBsZXQgZmlsZUljb24gPSAn8J+ThCdcbiAgICAgICAgICAgICAgICAgICAgICBpZiAoWydqcGcnLCAnanBlZycsICdwbmcnLCAnZ2lmJywgJ3dlYnAnXS5pbmNsdWRlcyhmaWxlRXh0ZW5zaW9uKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZmlsZUljb24gPSAn8J+WvO+4jydcbiAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGZpbGVFeHRlbnNpb24gPT09ICdwZGYnKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmaWxlSWNvbiA9ICfwn5OVJ1xuICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoWydkb2MnLCAnZG9jeCddLmluY2x1ZGVzKGZpbGVFeHRlbnNpb24pKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmaWxlSWNvbiA9ICfwn5OdJ1xuICAgICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBiZy13aGl0ZSByb3VuZGVkLWxnIHAtNCBib3JkZXIgYm9yZGVyLXB1cnBsZS0yMDAgaG92ZXI6Ym9yZGVyLXB1cnBsZS0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bFwiPntmaWxlSWNvbn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPntmaWxlTmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj7oq4vlgYforYnmmI7mlofku7Y8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cub3BlbihgaHR0cDovL2xvY2FsaG9zdDo3MDcyJHthdHRhY2htZW50fWAsICdfYmxhbmsnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTcwMCBob3ZlcjpiZy1ibHVlLTUwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDmn6XnnItcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsaW5rLmhyZWYgPSBgaHR0cDovL2xvY2FsaG9zdDo3MDcyJHthdHRhY2htZW50fWBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGluay5kb3dubG9hZCA9IGZpbGVOYW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluaylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGluay5jbGljaygpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluaylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMCBob3Zlcjp0ZXh0LWdyZWVuLTcwMCBob3ZlcjpiZy1ncmVlbi01MFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDkuIvovIlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgey8qIOWvqeaguOeLgOaFiyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2Byb3VuZGVkLTJ4bCBwLTYgbWItNiAke3NlbGVjdGVkUmVxdWVzdC5zdGF0dXMgPT09ICdhcHByb3ZlZCcgPyAnYmctZ3JlZW4tNTAnIDpcbiAgICAgICAgICAgICAgICBzZWxlY3RlZFJlcXVlc3Quc3RhdHVzID09PSAncmVqZWN0ZWQnID8gJ2JnLXJlZC01MCcgOiAnYmcteWVsbG93LTUwJ1xuICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj7lr6nmoLjni4DmhYs8L2g0PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQtc20gZm9udC1tZWRpdW0gYm9yZGVyICR7Z2V0U3RhdHVzQ29sb3Ioc2VsZWN0ZWRSZXF1ZXN0LnN0YXR1cyl9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNJY29uKHNlbGVjdGVkUmVxdWVzdC5zdGF0dXMpfVxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0xXCI+e2dldFN0YXR1c1RleHQoc2VsZWN0ZWRSZXF1ZXN0LnN0YXR1cyl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIOeUs+iri+aWvCB7bmV3IERhdGUoc2VsZWN0ZWRSZXF1ZXN0LmNyZWF0ZWRfYXQgfHwgc2VsZWN0ZWRSZXF1ZXN0LnN1Ym1pdHRlZF9hdCkudG9Mb2NhbGVTdHJpbmcoJ3poLVRXJyl9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAge3NlbGVjdGVkUmVxdWVzdC5hcHByb3ZlZF9ieSAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPuWvqeaguOS6ujo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTkwMFwiPntzZWxlY3RlZFJlcXVlc3QuYXBwcm92ZWRfYnl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC00IHRleHQtZ3JheS01MDBcIj7lr6nmoLjmmYLplpM6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRSZXF1ZXN0LmFwcHJvdmVkX2F0ICYmIG5ldyBEYXRlKHNlbGVjdGVkUmVxdWVzdC5hcHByb3ZlZF9hdCkudG9Mb2NhbGVTdHJpbmcoJ3poLVRXJyl9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAge3NlbGVjdGVkUmVxdWVzdC5yZWplY3Rpb25fcmVhc29uICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMyBwLTMgYmctcmVkLTEwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTcwMCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+5ouS57WV5Y6f5ZugOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtODAwIHRleHQtc20gbXQtMVwiPntzZWxlY3RlZFJlcXVlc3QucmVqZWN0aW9uX3JlYXNvbn08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7Lyog5pON5L2c5oyJ6YiVICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktZW5kIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZFJlcXVlc3Quc3RhdHVzID09PSAncGVuZGluZycgJiYgKFxuICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzdWNjZXNzXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRBcHByb3ZhbEFjdGlvbignYXBwcm92ZScpXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93QXBwcm92YWxNb2RhbCh0cnVlKVxuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxDaGVjayBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7moLjlh4Y8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImVycm9yXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRBcHByb3ZhbEFjdGlvbigncmVqZWN0JylcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dBcHByb3ZhbE1vZGFsKHRydWUpXG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5ouS57WVPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRSZXF1ZXN0KG51bGwpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIOmXnOmWiVxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gIClcbn0iXSwibmFtZXMiOlsidXNlQXV0aCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwiQnV0dG9uIiwiTGluayIsImxvZ2luIiwiQXJyb3dMZWZ0IiwiQ2xvY2siLCJQbHVzQ2lyY2xlIiwiQ2hlY2tDaXJjbGUiLCJYQ2lyY2xlIiwiQ2FsZW5kYXIiLCJVc2VyIiwiRmlsZVRleHQiLCJFeWUiLCJDaGVjayIsIlgiLCJBbGVydENpcmNsZSIsIlNlYXJjaCIsIkZpbHRlciIsIkRvd25sb2FkIiwiUmVmcmVzaEN3IiwiTGVhdmVBcHByb3ZhbFBhZ2UiLCJ1c2VyIiwiYXV0aExvZ2luIiwibG9hZGluZyIsInJvdXRlciIsImxlYXZlUmVxdWVzdHMiLCJzZXRMZWF2ZVJlcXVlc3RzIiwiZGF0YUxvYWRpbmciLCJzZXREYXRhTG9hZGluZyIsImF1dG9Mb2dpbkxvYWRpbmciLCJzZXRBdXRvTG9naW5Mb2FkaW5nIiwic2VsZWN0ZWRSZXF1ZXN0Iiwic2V0U2VsZWN0ZWRSZXF1ZXN0Iiwic2hvd0FwcHJvdmFsTW9kYWwiLCJzZXRTaG93QXBwcm92YWxNb2RhbCIsImFwcHJvdmFsQWN0aW9uIiwic2V0QXBwcm92YWxBY3Rpb24iLCJhcHByb3ZhbENvbW1lbnQiLCJzZXRBcHByb3ZhbENvbW1lbnQiLCJzZWFyY2hQYXJhbXMiLCJzZXRTZWFyY2hQYXJhbXMiLCJlbXBsb3llZV9uYW1lIiwic3RhdHVzIiwibGVhdmVfdHlwZSIsImZpbHRlcmVkUmVxdWVzdHMiLCJzZXRGaWx0ZXJlZFJlcXVlc3RzIiwic3RhdHMiLCJzZXRTdGF0cyIsInBlbmRpbmciLCJ0b2RheU5ldyIsImFwcHJvdmVkIiwicmVqZWN0ZWQiLCJsZWF2ZVR5cGVNYXAiLCJhbm51YWwiLCJzaWNrIiwicGVyc29uYWwiLCJtYXRlcm5pdHkiLCJwYXRlcm5pdHkiLCJtYXJyaWFnZSIsImZ1bmVyYWwiLCJjb21wZW5zYXRvcnkiLCJvdGhlciIsImhhbmRsZUF1dG9Mb2dpbiIsImNvbnNvbGUiLCJsb2ciLCJyZXNwb25zZSIsImVtcGxveWVlX2lkIiwicGFzc3dvcmQiLCJ1c2VyRGF0YSIsImlkIiwibmFtZSIsImVtcGxveWVlX2NvZGUiLCJkZXBhcnRtZW50X2lkIiwicG9zaXRpb24iLCJyb2xlX2lkIiwiZW1haWwiLCJkZXBhcnRtZW50X25hbWUiLCJlcnJvciIsImZldGNoTGVhdmVSZXF1ZXN0cyIsImFwaUJhc2VVcmwiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhvc3RuYW1lIiwiZmV0Y2giLCJvayIsIkVycm9yIiwiZGF0YSIsImpzb24iLCJyYXdSZXF1ZXN0cyIsInJlY29yZHMiLCJyZXF1ZXN0cyIsIm1hcCIsIml0ZW0iLCJkYXlzQ291bnQiLCJzdGFydERhdGUiLCJEYXRlIiwic3RhcnRfZGF0ZSIsImVuZERhdGUiLCJlbmRfZGF0ZSIsImlzTmFOIiwiZ2V0VGltZSIsIk1hdGgiLCJjZWlsIiwid2FybiIsImF0dGFjaG1lbnRzIiwiSlNPTiIsInBhcnNlIiwiZGF5c19jb3VudCIsInJlYXNvbiIsInN1Ym1pdHRlZF9hdCIsImNyZWF0ZWRfYXQiLCJhcHByb3ZlZF9ieSIsImFwcHJvdmVyX25hbWUiLCJhcHByb3ZlZF9hdCIsInJlamVjdGlvbl9yZWFzb24iLCJjb21tZW50IiwiZW1lcmdlbmN5X2NvbnRhY3QiLCJzdWJzdGl0dXRlX2lkIiwic3Vic3RpdHV0ZV9uYW1lIiwibGVuZ3RoIiwic2xpY2UiLCJuZXdTdGF0cyIsImZpbHRlciIsInIiLCJ0b0RhdGVTdHJpbmciLCJoYW5kbGVTZWFyY2giLCJmaWx0ZXJlZCIsInJlcXVlc3QiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwiaGFuZGxlUmVzZXQiLCJoYW5kbGVBcHByb3ZhbCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5Iiwic3RyaW5naWZ5IiwiYWN0aW9uIiwidW5kZWZpbmVkIiwiZmV0Y2hSZXNwb25zZSIsIm1hdGNoIiwiYWxlcnQiLCJnZXRTdGF0dXNDb2xvciIsImdldFN0YXR1c1RleHQiLCJnZXRTdGF0dXNJY29uIiwiY2xhc3NOYW1lIiwiZGl2IiwicCIsImgyIiwib25DbGljayIsInB1c2giLCJoMSIsImhyZWYiLCJzcGFuIiwibWFpbiIsImgzIiwidmFyaWFudCIsInJlbG9hZCIsImxhYmVsIiwiaW5wdXQiLCJ0eXBlIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsInNlbGVjdCIsIm9wdGlvbiIsIk9iamVjdCIsImVudHJpZXMiLCJrZXkiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwidGQiLCJjaGFyQXQiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJob3VyIiwibWludXRlIiwic2l6ZSIsImg0IiwidGV4dGFyZWEiLCJyb3dzIiwicmVxdWlyZWQiLCJkaXNhYmxlZCIsInRyaW0iLCJhdHRhY2htZW50IiwiaW5kZXgiLCJmaWxlTmFtZSIsInNwbGl0IiwicG9wIiwiZmlsZUV4dGVuc2lvbiIsImZpbGVJY29uIiwib3BlbiIsImxpbmsiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJkb3dubG9hZCIsImFwcGVuZENoaWxkIiwiY2xpY2siLCJyZW1vdmVDaGlsZCIsInRvTG9jYWxlU3RyaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/leave-approval/page.tsx\n"));

/***/ })

});