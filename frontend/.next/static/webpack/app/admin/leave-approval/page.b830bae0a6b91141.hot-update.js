"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/leave-approval/page",{

/***/ "(app-pages-browser)/./src/app/admin/leave-approval/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/admin/leave-approval/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeaveApprovalPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LeaveApprovalPage() {\n    _s();\n    const { user, login: authLogin, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [leaveRequests, setLeaveRequests] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dataLoading, setDataLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [autoLoginLoading, setAutoLoginLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedRequest, setSelectedRequest] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showApprovalModal, setShowApprovalModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [approvalAction, setApprovalAction] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"approve\");\n    const [approvalComment, setApprovalComment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [searchParams, setSearchParams] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        employee_name: \"\",\n        status: \"\",\n        leave_type: \"\"\n    });\n    const [filteredRequests, setFilteredRequests] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        pending: 0,\n        todayNew: 0,\n        approved: 0,\n        rejected: 0\n    });\n    // 請假類型對應\n    const leaveTypeMap = {\n        annual: \"年假\",\n        sick: \"病假\",\n        personal: \"事假\",\n        maternity: \"產假\",\n        paternity: \"陪產假\",\n        marriage: \"婚假\",\n        funeral: \"喪假\",\n        compensatory: \"補休\",\n        other: \"其他\"\n    };\n    // 自動登入功能 - 使用管理員測試帳號\n    const handleAutoLogin = async ()=>{\n        setAutoLoginLoading(true);\n        try {\n            console.log(\"開始自動登入管理員帳號\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_6__.login)({\n                employee_id: \"admin\",\n                password: \"admin123\"\n            });\n            if (response && response.user) {\n                const userData = response.user;\n                const user = {\n                    id: userData.employee_id,\n                    name: userData.employee_name,\n                    employee_id: userData.employee_code,\n                    department_id: userData.department_id,\n                    position: userData.role_id === 999 ? \"系統管理員\" : \"員工\",\n                    email: userData.email,\n                    role_id: userData.role_id,\n                    department_name: userData.department_name\n                };\n                authLogin(user);\n                console.log(\"自動登入成功:\", user);\n            } else {\n                console.error(\"自動登入失敗:\", response);\n            }\n        } catch (error) {\n            console.error(\"自動登入錯誤:\", error);\n        } finally{\n            setAutoLoginLoading(false);\n        }\n    };\n    // 頁面載入時自動登入（如果未登入）\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!loading && !user) {\n            console.log(\"檢測到未登入，執行自動登入\");\n            handleAutoLogin();\n        }\n    }, [\n        loading,\n        user\n    ]);\n    // 載入真實的請假申請數據\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchLeaveRequests = async ()=>{\n            try {\n                setDataLoading(true);\n                const apiBaseUrl = window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\" ? \"http://localhost:7072\" : \"http://\".concat(window.location.hostname, \":7072\");\n                const response = await fetch(\"\".concat(apiBaseUrl, \"/api/leave-requests\"));\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch leave requests\");\n                }\n                const data = await response.json();\n                const rawRequests = data.records || [];\n                // 映射 API 數據到前端期望的格式\n                const requests = rawRequests.map((item)=>{\n                    // 安全的日期計算\n                    let daysCount = 1;\n                    try {\n                        const startDate = new Date(item.start_date);\n                        const endDate = new Date(item.end_date);\n                        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {\n                            daysCount = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n                        }\n                    } catch (error) {\n                        console.warn(\"日期計算錯誤:\", item.start_date, item.end_date, error);\n                    }\n                    return {\n                        id: item.id,\n                        employee_id: item.employee_code || item.employee_id || \"\",\n                        employee_name: item.employee_name || \"未知員工\",\n                        department_name: item.department_name || \"未知部門\",\n                        leave_type: item.leave_type,\n                        start_date: item.start_date,\n                        end_date: item.end_date,\n                        days_count: daysCount,\n                        reason: item.reason || \"無\",\n                        status: item.status,\n                        submitted_at: item.created_at,\n                        created_at: item.created_at,\n                        approved_by: item.approver_name,\n                        approved_at: item.approved_at,\n                        rejection_reason: item.comment,\n                        emergency_contact: item.emergency_contact || \"無\",\n                        substitute_id: item.substitute_id,\n                        substitute_name: item.substitute_name || \"無\"\n                    };\n                });\n                console.log(\"API 原始數據筆數:\", rawRequests.length);\n                console.log(\"映射後數據筆數:\", requests.length);\n                console.log(\"前3筆映射後的數據:\", requests.slice(0, 3));\n                setLeaveRequests(requests);\n                setFilteredRequests(requests);\n                // 計算統計數據\n                const newStats = {\n                    pending: requests.filter((r)=>r.status === \"pending\").length,\n                    todayNew: requests.filter((r)=>new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()).length,\n                    approved: requests.filter((r)=>r.status === \"approved\").length,\n                    rejected: requests.filter((r)=>r.status === \"rejected\").length\n                };\n                setStats(newStats);\n            } catch (error) {\n                console.error(\"Error fetching leave requests:\", error);\n                // 如果API失敗，設置空數據\n                setLeaveRequests([]);\n                setFilteredRequests([]);\n                setStats({\n                    pending: 0,\n                    todayNew: 0,\n                    approved: 0,\n                    rejected: 0\n                });\n            } finally{\n                setDataLoading(false);\n            }\n        };\n        fetchLeaveRequests();\n    }, []);\n    // 搜索和篩選\n    const handleSearch = ()=>{\n        let filtered = leaveRequests;\n        if (searchParams.employee_name) {\n            filtered = filtered.filter((request)=>{\n                var _request_employee_name;\n                return (_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.toLowerCase().includes(searchParams.employee_name.toLowerCase());\n            });\n        }\n        if (searchParams.status) {\n            filtered = filtered.filter((request)=>request.status === searchParams.status);\n        }\n        if (searchParams.leave_type) {\n            filtered = filtered.filter((request)=>request.leave_type === searchParams.leave_type);\n        }\n        setFilteredRequests(filtered);\n    };\n    const handleReset = ()=>{\n        setSearchParams({\n            employee_name: \"\",\n            status: \"\",\n            leave_type: \"\"\n        });\n        setFilteredRequests(leaveRequests);\n    };\n    // 處理審核\n    const handleApproval = async ()=>{\n        if (!selectedRequest) return;\n        try {\n            // 調用真實的API\n            const apiBaseUrl = window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\" ? \"http://localhost:7072\" : \"http://\".concat(window.location.hostname, \":7072\");\n            const response = await fetch(\"\".concat(apiBaseUrl, \"/api/approval/leaves/\").concat(selectedRequest.id), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: approvalAction,\n                    comment: approvalAction === \"reject\" ? approvalComment : undefined\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update leave request\");\n            }\n            // 重新載入數據\n            const fetchResponse = await fetch(\"\".concat(apiBaseUrl, \"/api/leave-requests\"));\n            if (fetchResponse.ok) {\n                const data = await fetchResponse.json();\n                const rawRequests = data.records || [];\n                // 映射 API 數據到前端期望的格式\n                const requests = rawRequests.map((item)=>{\n                    // 安全的日期計算\n                    let daysCount = 1;\n                    try {\n                        const startDate = new Date(item.start_date);\n                        const endDate = new Date(item.end_date);\n                        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {\n                            daysCount = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n                        }\n                    } catch (error) {\n                        console.warn(\"日期計算錯誤:\", item.start_date, item.end_date, error);\n                    }\n                    return {\n                        id: item.id,\n                        employee_id: item.employee_code || item.employee_id || \"\",\n                        employee_name: item.employee_name || \"未知員工\",\n                        department_name: item.department_name || \"未知部門\",\n                        leave_type: item.leave_type,\n                        start_date: item.start_date,\n                        end_date: item.end_date,\n                        days_count: daysCount,\n                        reason: item.reason || \"無\",\n                        status: item.status,\n                        submitted_at: item.created_at,\n                        created_at: item.created_at,\n                        approved_by: item.approver_name,\n                        approved_at: item.approved_at,\n                        rejection_reason: item.comment,\n                        emergency_contact: item.emergency_contact || \"無\",\n                        substitute_id: item.substitute_id,\n                        substitute_name: item.substitute_name || \"無\"\n                    };\n                });\n                setLeaveRequests(requests);\n                setFilteredRequests(requests.filter((request)=>{\n                    let match = true;\n                    if (searchParams.employee_name) {\n                        var _request_employee_name;\n                        match = match && ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.toLowerCase().includes(searchParams.employee_name.toLowerCase()));\n                    }\n                    if (searchParams.status) {\n                        match = match && request.status === searchParams.status;\n                    }\n                    if (searchParams.leave_type) {\n                        match = match && request.leave_type === searchParams.leave_type;\n                    }\n                    return match;\n                }));\n                // 更新統計\n                const newStats = {\n                    pending: requests.filter((r)=>r.status === \"pending\").length,\n                    todayNew: requests.filter((r)=>new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()).length,\n                    approved: requests.filter((r)=>r.status === \"approved\").length,\n                    rejected: requests.filter((r)=>r.status === \"rejected\").length\n                };\n                setStats(newStats);\n            }\n            setShowApprovalModal(false);\n            setSelectedRequest(null);\n            setApprovalComment(\"\");\n        } catch (error) {\n            console.error(\"Error updating leave request:\", error);\n            alert(\"審核失敗，請稍後再試\");\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"text-warning-600 bg-warning-50 border-warning-200\";\n            case \"approved\":\n                return \"text-success-600 bg-success-50 border-success-200\";\n            case \"rejected\":\n                return \"text-error-600 bg-error-50 border-error-200\";\n            default:\n                return \"text-neutral-600 bg-neutral-50 border-neutral-200\";\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"待審核\";\n            case \"approved\":\n                return \"已核准\";\n            case \"rejected\":\n                return \"已拒絕\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 30\n                }, this);\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 31\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 31\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    if (loading || autoLoginLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: autoLoginLoading ? \"自動登入中...\" : \"載入中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 375,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n            lineNumber: 374,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user || user.role_id !== 999) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"權限不足\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"您需要管理員權限才能訪問此頁面\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>router.push(\"/admin\"),\n                        children: \"返回管理後台\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 388,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n            lineNumber: 387,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold mb-2 text-white\",\n                                    children: \"請假審核管理\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            href: \"/admin\",\n                                            className: \"inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white group-hover:text-indigo-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-white group-hover:text-indigo-100\",\n                                                    children: \"返回\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-base font-medium\",\n                                            children: \"管理待審核的請假申請與其他審核事項\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-white\",\n                                            children: \"管理員模式\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-indigo-100\",\n                                            children: \"請假審核\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: \"請假審核管理中心\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600\",\n                                children: \"處理員工請假申請，確保工作流程順暢\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-warning-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.pending\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"待審核\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.todayNew\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"今日新增\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6 text-success-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.approved\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"已核准\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-error-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-error-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.rejected\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"已拒絕\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-purple-500/5 to-pink-500/5 px-8 py-6 border-b border-gray-100/60\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-gray-900\",\n                                                            children: \"篩選條件\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 mt-1\",\n                                                            children: \"快速找到特定的請假申請\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: ()=>window.location.reload(),\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"重新載入\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"匯出報表\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-4 h-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"員工姓名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: searchParams.employee_name,\n                                                                onChange: (e)=>setSearchParams({\n                                                                        ...searchParams,\n                                                                        employee_name: e.target.value\n                                                                    }),\n                                                                placeholder: \"搜尋員工姓名...\",\n                                                                className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 pl-11 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-4 h-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"審核狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: searchParams.status,\n                                                        onChange: (e)=>setSearchParams({\n                                                                ...searchParams,\n                                                                status: e.target.value\n                                                            }),\n                                                        className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"所有狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"pending\",\n                                                                children: \"待審核\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"approved\",\n                                                                children: \"已核准\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"rejected\",\n                                                                children: \"已拒絕\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4 text-purple-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"請假類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: searchParams.leave_type,\n                                                        onChange: (e)=>setSearchParams({\n                                                                ...searchParams,\n                                                                leave_type: e.target.value\n                                                            }),\n                                                        className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"所有類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            Object.entries(leaveTypeMap).map((param)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: key,\n                                                                    children: value\n                                                                }, key, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 21\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between pt-6 border-t border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"primary\",\n                                                        onClick: handleSearch,\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"查詢\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"重置\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"共找到 \",\n                                                    filteredRequests.length,\n                                                    \" 筆申請\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-green-500/5 to-blue-500/5 px-8 py-6 border-b border-gray-100/60\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"請假申請列表\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: \"管理員工的請假申請\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 11\n                            }, this),\n                            dataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-16\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"載入請假申請中...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 13\n                            }, this) : filteredRequests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:block overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gradient-to-r from-green-500 to-blue-600 text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"申請人\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"請假類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"請假日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"天數\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"申請時間\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"操作\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: filteredRequests.map((request)=>{\n                                                        var _request_employee_name;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-white font-medium text-sm\",\n                                                                                    children: ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.charAt(0)) || \"?\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 653,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 652,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: request.employee_name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 658,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: request.employee_id\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 659,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-400\",\n                                                                                        children: request.department_name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 660,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 657,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 651,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-600 border border-blue-200\",\n                                                                        children: leaveTypeMap[request.leave_type] || request.leave_type || \"未知\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 665,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: new Date(request.start_date).toLocaleDateString(\"zh-TW\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 670,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        request.start_date !== request.end_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"至 \",\n                                                                                new Date(request.end_date).toLocaleDateString(\"zh-TW\")\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 672,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        request.days_count,\n                                                                        \" 天\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border \".concat(getStatusColor(request.status)),\n                                                                        children: [\n                                                                            getStatusIcon(request.status),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: getStatusText(request.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 683,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 681,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        new Date(request.created_at || request.submitted_at).toLocaleDateString(\"zh-TW\"),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: new Date(request.created_at || request.submitted_at).toLocaleTimeString(\"zh-TW\", {\n                                                                                hour: \"2-digit\",\n                                                                                minute: \"2-digit\"\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm font-medium\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>setSelectedRequest(request),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 702,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            request.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedRequest(request);\n                                                                                            setApprovalAction(\"approve\");\n                                                                                            setShowApprovalModal(true);\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-green-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                            lineNumber: 715,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 706,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedRequest(request);\n                                                                                            setApprovalAction(\"reject\");\n                                                                                            setShowApprovalModal(true);\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-red-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                            lineNumber: 726,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 717,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, request.id, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:hidden space-y-4 p-6\",\n                                        children: filteredRequests.map((request)=>{\n                                            var _request_employee_name;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-2xl p-6 border border-gray-200 shadow-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-medium text-sm\",\n                                                                            children: ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.charAt(0)) || \"?\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 745,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 744,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900\",\n                                                                                children: request.employee_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 750,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: request.department_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 751,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border \".concat(getStatusColor(request.status)),\n                                                                children: [\n                                                                    getStatusIcon(request.status),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-1\",\n                                                                        children: getStatusText(request.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-sm mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"類型:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: leaveTypeMap[request.leave_type] || request.leave_type || \"未知\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 763,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"天數:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 766,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: [\n                                                                            request.days_count,\n                                                                            \" 天\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 767,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"開始:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 770,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: new Date(request.start_date).toLocaleDateString(\"zh-TW\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 771,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"結束:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 776,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: new Date(request.end_date).toLocaleDateString(\"zh-TW\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 777,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: \"原因:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-900 text-sm mt-1\",\n                                                                children: request.reason\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"申請於 \",\n                                                                    new Date(request.created_at || request.submitted_at).toLocaleDateString(\"zh-TW\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>setSelectedRequest(request),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 798,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 793,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    request.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedRequest(request);\n                                                                                    setApprovalAction(\"approve\");\n                                                                                    setShowApprovalModal(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-green-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 811,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 802,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedRequest(request);\n                                                                                    setApprovalAction(\"reject\");\n                                                                                    setShowApprovalModal(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-red-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 822,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 813,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, request.id, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 834,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"查無請假申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"請調整搜尋條件或等待新的申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 833,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 608,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            showApprovalModal && selectedRequest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-3xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: approvalAction === \"approve\" ? \"核准請假申請\" : \"拒絕請假申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowApprovalModal(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 847,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"申請詳情\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"申請人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 865,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"部門:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.department_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 868,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假類型:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: leaveTypeMap[selectedRequest.leave_type] || selectedRequest.leave_type || \"未知\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 874,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假天數:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: [\n                                                            selectedRequest.days_count,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"開始日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.start_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"結束日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.end_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 886,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedRequest.substitute_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"代理人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.substitute_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"緊急聯絡:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.emergency_contact\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 898,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"請假原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900 mt-1\",\n                                                children: selectedRequest.reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 903,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 861,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: approvalAction === \"approve\" ? \"核准意見（可選）\" : \"拒絕原因（必填）\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 911,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: approvalComment,\n                                        onChange: (e)=>setApprovalComment(e.target.value),\n                                        rows: 4,\n                                        className: \"w-full px-3 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none\",\n                                        placeholder: approvalAction === \"approve\" ? \"輸入核准意見...\" : \"請說明拒絕原因...\",\n                                        required: approvalAction === \"reject\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 914,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 910,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setShowApprovalModal(false),\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: approvalAction === \"approve\" ? \"success\" : \"error\",\n                                        onClick: handleApproval,\n                                        disabled: approvalAction === \"reject\" && !approvalComment.trim(),\n                                        className: \"flex items-center space-x-2\",\n                                        children: approvalAction === \"approve\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 944,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"核准申請\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 945,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 949,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"拒絕申請\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 950,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 936,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 929,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 846,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 845,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 844,\n                columnNumber: 9\n            }, this),\n            selectedRequest && !showApprovalModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-3xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"請假申請詳情\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 966,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedRequest(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 972,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 967,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 965,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"申請人資訊\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"姓名:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 981,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"員工編號:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_id\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 986,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 984,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"部門:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 989,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.department_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 990,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 988,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"緊急聯絡:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.emergency_contact\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 992,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 979,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"請假資訊\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1001,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假類型:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: leaveTypeMap[selectedRequest.leave_type] || selectedRequest.leave_type || \"未知\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假天數:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: [\n                                                            selectedRequest.days_count,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1007,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"開始日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1012,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.start_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1011,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"結束日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.end_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1019,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1017,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedRequest.substitute_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"代理人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1025,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.substitute_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1026,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1024,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"請假原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900 mt-1\",\n                                                children: selectedRequest.reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1032,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1030,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1000,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-2xl p-6 mb-6 \".concat(selectedRequest.status === \"approved\" ? \"bg-green-50\" : selectedRequest.status === \"rejected\" ? \"bg-red-50\" : \"bg-yellow-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"審核狀態\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1040,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border \".concat(getStatusColor(selectedRequest.status)),\n                                                children: [\n                                                    getStatusIcon(selectedRequest.status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1\",\n                                                        children: getStatusText(selectedRequest.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1044,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"申請於 \",\n                                                    new Date(selectedRequest.created_at || selectedRequest.submitted_at).toLocaleString(\"zh-TW\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1041,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedRequest.approved_by && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"審核人:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1052,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-gray-900\",\n                                                children: selectedRequest.approved_by\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1053,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-4 text-gray-500\",\n                                                children: \"審核時間:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1054,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-gray-900\",\n                                                children: selectedRequest.approved_at && new Date(selectedRequest.approved_at).toLocaleString(\"zh-TW\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1055,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1051,\n                                        columnNumber: 19\n                                    }, this),\n                                    selectedRequest.rejection_reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 p-3 bg-red-100 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-700 text-sm font-medium\",\n                                                children: \"拒絕原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1062,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-800 text-sm mt-1\",\n                                                children: selectedRequest.rejection_reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1063,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1061,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1037,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end space-x-4\",\n                                children: [\n                                    selectedRequest.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"success\",\n                                                onClick: ()=>{\n                                                    setApprovalAction(\"approve\");\n                                                    setShowApprovalModal(true);\n                                                },\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1080,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"核准\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1072,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"error\",\n                                                onClick: ()=>{\n                                                    setApprovalAction(\"reject\");\n                                                    setShowApprovalModal(true);\n                                                },\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1091,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"拒絕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1083,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setSelectedRequest(null),\n                                        children: \"關閉\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1096,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1069,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 964,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 963,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 962,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n        lineNumber: 401,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaveApprovalPage, \"Ar0WQKmFVdQPYkPfgrKnB00pfUc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = LeaveApprovalPage;\nvar _c;\n$RefreshReg$(_c, \"LeaveApprovalPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/leave-approval/page.tsx\n"));

/***/ })

});