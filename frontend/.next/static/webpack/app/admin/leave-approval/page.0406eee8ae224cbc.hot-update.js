"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/leave-approval/page",{

/***/ "(app-pages-browser)/./src/app/admin/leave-approval/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/admin/leave-approval/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeaveApprovalPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LeaveApprovalPage() {\n    _s();\n    const { user, login: authLogin, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [leaveRequests, setLeaveRequests] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dataLoading, setDataLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [autoLoginLoading, setAutoLoginLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedRequest, setSelectedRequest] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showApprovalModal, setShowApprovalModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [approvalAction, setApprovalAction] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"approve\");\n    const [approvalComment, setApprovalComment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [searchParams, setSearchParams] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        employee_name: \"\",\n        status: \"\",\n        leave_type: \"\"\n    });\n    const [filteredRequests, setFilteredRequests] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        pending: 0,\n        todayNew: 0,\n        approved: 0,\n        rejected: 0\n    });\n    // 請假類型對應\n    const leaveTypeMap = {\n        annual: \"年假\",\n        sick: \"病假\",\n        personal: \"事假\",\n        maternity: \"產假\",\n        paternity: \"陪產假\",\n        marriage: \"婚假\",\n        funeral: \"喪假\",\n        compensatory: \"補休\",\n        other: \"其他\"\n    };\n    // 自動登入功能 - 使用管理員測試帳號\n    const handleAutoLogin = async ()=>{\n        setAutoLoginLoading(true);\n        try {\n            console.log(\"開始自動登入管理員帳號\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_6__.login)({\n                employee_id: \"admin\",\n                password: \"admin123\"\n            });\n            if (response && response.user) {\n                const userData = response.user;\n                const user = {\n                    id: userData.employee_id,\n                    name: userData.employee_name,\n                    employee_id: userData.employee_code,\n                    department_id: userData.department_id,\n                    position: userData.role_id === 999 ? \"系統管理員\" : \"員工\",\n                    email: userData.email,\n                    role_id: userData.role_id,\n                    department_name: userData.department_name\n                };\n                authLogin(user);\n                console.log(\"自動登入成功:\", user);\n            } else {\n                console.error(\"自動登入失敗:\", response);\n            }\n        } catch (error) {\n            console.error(\"自動登入錯誤:\", error);\n        } finally{\n            setAutoLoginLoading(false);\n        }\n    };\n    // 頁面載入時自動登入（如果未登入）\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!loading && !user) {\n            console.log(\"檢測到未登入，執行自動登入\");\n            handleAutoLogin();\n        }\n    }, [\n        loading,\n        user\n    ]);\n    // 載入真實的請假申請數據\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchLeaveRequests = async ()=>{\n            try {\n                setDataLoading(true);\n                const apiBaseUrl = window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\" ? \"http://localhost:7072\" : \"http://\".concat(window.location.hostname, \":7072\");\n                const response = await fetch(\"\".concat(apiBaseUrl, \"/api/leave-requests\"));\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch leave requests\");\n                }\n                const data = await response.json();\n                const rawRequests = data.records || [];\n                // 映射 API 數據到前端期望的格式\n                const requests = rawRequests.map((item)=>{\n                    // 安全的日期計算\n                    let daysCount = 1;\n                    try {\n                        const startDate = new Date(item.start_date);\n                        const endDate = new Date(item.end_date);\n                        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {\n                            daysCount = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n                        }\n                    } catch (error) {\n                        console.warn(\"日期計算錯誤:\", item.start_date, item.end_date, error);\n                    }\n                    // 處理附件數據\n                    let attachments = [];\n                    if (item.attachments) {\n                        try {\n                            attachments = typeof item.attachments === \"string\" ? JSON.parse(item.attachments) : item.attachments;\n                        } catch (error) {\n                            console.warn(\"解析附件數據失敗:\", item.attachments, error);\n                            attachments = [];\n                        }\n                    }\n                    return {\n                        id: item.id,\n                        employee_id: item.employee_code || item.employee_id || \"\",\n                        employee_name: item.employee_name || \"未知員工\",\n                        department_name: item.department_name || \"未知部門\",\n                        leave_type: item.leave_type,\n                        start_date: item.start_date,\n                        end_date: item.end_date,\n                        days_count: daysCount,\n                        reason: item.reason || \"無\",\n                        status: item.status,\n                        submitted_at: item.created_at,\n                        created_at: item.created_at,\n                        approved_by: item.approver_name,\n                        approved_at: item.approved_at,\n                        rejection_reason: item.comment,\n                        emergency_contact: item.emergency_contact || \"無\",\n                        substitute_id: item.substitute_id,\n                        substitute_name: item.substitute_name || \"無\",\n                        attachments: attachments\n                    };\n                });\n                console.log(\"API 原始數據筆數:\", rawRequests.length);\n                console.log(\"映射後數據筆數:\", requests.length);\n                console.log(\"前3筆映射後的數據:\", requests.slice(0, 3));\n                setLeaveRequests(requests);\n                setFilteredRequests(requests);\n                // 計算統計數據\n                const newStats = {\n                    pending: requests.filter((r)=>r.status === \"pending\").length,\n                    todayNew: requests.filter((r)=>new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()).length,\n                    approved: requests.filter((r)=>r.status === \"approved\").length,\n                    rejected: requests.filter((r)=>r.status === \"rejected\").length\n                };\n                setStats(newStats);\n            } catch (error) {\n                console.error(\"Error fetching leave requests:\", error);\n                // 如果API失敗，設置空數據\n                setLeaveRequests([]);\n                setFilteredRequests([]);\n                setStats({\n                    pending: 0,\n                    todayNew: 0,\n                    approved: 0,\n                    rejected: 0\n                });\n            } finally{\n                setDataLoading(false);\n            }\n        };\n        fetchLeaveRequests();\n    }, []);\n    // 搜索和篩選\n    const handleSearch = ()=>{\n        let filtered = leaveRequests;\n        if (searchParams.employee_name) {\n            filtered = filtered.filter((request)=>{\n                var _request_employee_name;\n                return (_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.toLowerCase().includes(searchParams.employee_name.toLowerCase());\n            });\n        }\n        if (searchParams.status) {\n            filtered = filtered.filter((request)=>request.status === searchParams.status);\n        }\n        if (searchParams.leave_type) {\n            filtered = filtered.filter((request)=>request.leave_type === searchParams.leave_type);\n        }\n        setFilteredRequests(filtered);\n    };\n    const handleReset = ()=>{\n        setSearchParams({\n            employee_name: \"\",\n            status: \"\",\n            leave_type: \"\"\n        });\n        setFilteredRequests(leaveRequests);\n    };\n    // 處理審核\n    const handleApproval = async ()=>{\n        if (!selectedRequest) return;\n        try {\n            // 調用真實的API\n            const apiBaseUrl = window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\" ? \"http://localhost:7072\" : \"http://\".concat(window.location.hostname, \":7072\");\n            const response = await fetch(\"\".concat(apiBaseUrl, \"/api/approval/leaves/\").concat(selectedRequest.id), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: approvalAction,\n                    comment: approvalAction === \"reject\" ? approvalComment : undefined\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update leave request\");\n            }\n            // 重新載入數據\n            const fetchResponse = await fetch(\"\".concat(apiBaseUrl, \"/api/leave-requests\"));\n            if (fetchResponse.ok) {\n                const data = await fetchResponse.json();\n                const rawRequests = data.records || [];\n                // 映射 API 數據到前端期望的格式\n                const requests = rawRequests.map((item)=>{\n                    // 安全的日期計算\n                    let daysCount = 1;\n                    try {\n                        const startDate = new Date(item.start_date);\n                        const endDate = new Date(item.end_date);\n                        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {\n                            daysCount = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n                        }\n                    } catch (error) {\n                        console.warn(\"日期計算錯誤:\", item.start_date, item.end_date, error);\n                    }\n                    // 處理附件數據\n                    let attachments = [];\n                    if (item.attachments) {\n                        try {\n                            attachments = typeof item.attachments === \"string\" ? JSON.parse(item.attachments) : item.attachments;\n                        } catch (error) {\n                            console.warn(\"解析附件數據失敗:\", item.attachments, error);\n                            attachments = [];\n                        }\n                    }\n                    return {\n                        id: item.id,\n                        employee_id: item.employee_code || item.employee_id || \"\",\n                        employee_name: item.employee_name || \"未知員工\",\n                        department_name: item.department_name || \"未知部門\",\n                        leave_type: item.leave_type,\n                        start_date: item.start_date,\n                        end_date: item.end_date,\n                        days_count: daysCount,\n                        reason: item.reason || \"無\",\n                        status: item.status,\n                        submitted_at: item.created_at,\n                        created_at: item.created_at,\n                        approved_by: item.approver_name,\n                        approved_at: item.approved_at,\n                        rejection_reason: item.comment,\n                        emergency_contact: item.emergency_contact || \"無\",\n                        substitute_id: item.substitute_id,\n                        substitute_name: item.substitute_name || \"無\",\n                        attachments: attachments\n                    };\n                });\n                setLeaveRequests(requests);\n                setFilteredRequests(requests.filter((request)=>{\n                    let match = true;\n                    if (searchParams.employee_name) {\n                        var _request_employee_name;\n                        match = match && ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.toLowerCase().includes(searchParams.employee_name.toLowerCase()));\n                    }\n                    if (searchParams.status) {\n                        match = match && request.status === searchParams.status;\n                    }\n                    if (searchParams.leave_type) {\n                        match = match && request.leave_type === searchParams.leave_type;\n                    }\n                    return match;\n                }));\n                // 更新統計\n                const newStats = {\n                    pending: requests.filter((r)=>r.status === \"pending\").length,\n                    todayNew: requests.filter((r)=>new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()).length,\n                    approved: requests.filter((r)=>r.status === \"approved\").length,\n                    rejected: requests.filter((r)=>r.status === \"rejected\").length\n                };\n                setStats(newStats);\n            }\n            setShowApprovalModal(false);\n            setSelectedRequest(null);\n            setApprovalComment(\"\");\n        } catch (error) {\n            console.error(\"Error updating leave request:\", error);\n            alert(\"審核失敗，請稍後再試\");\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"text-warning-600 bg-warning-50 border-warning-200\";\n            case \"approved\":\n                return \"text-success-600 bg-success-50 border-success-200\";\n            case \"rejected\":\n                return \"text-error-600 bg-error-50 border-error-200\";\n            default:\n                return \"text-neutral-600 bg-neutral-50 border-neutral-200\";\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"待審核\";\n            case \"approved\":\n                return \"已核准\";\n            case \"rejected\":\n                return \"已拒絕\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 30\n                }, this);\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 31\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 31\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    if (loading || autoLoginLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: autoLoginLoading ? \"自動登入中...\" : \"載入中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 403,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n            lineNumber: 402,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user || user.role_id !== 999) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"權限不足\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"您需要管理員權限才能訪問此頁面\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>router.push(\"/admin\"),\n                        children: \"返回管理後台\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 416,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n            lineNumber: 415,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold mb-2 text-white\",\n                                    children: \"請假審核管理\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            href: \"/admin\",\n                                            className: \"inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white group-hover:text-indigo-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-white group-hover:text-indigo-100\",\n                                                    children: \"返回\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-base font-medium\",\n                                            children: \"管理待審核的請假申請與其他審核事項\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-white\",\n                                            children: \"管理員模式\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-indigo-100\",\n                                            children: \"請假審核\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 432,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: \"請假審核管理中心\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600\",\n                                children: \"處理員工請假申請，確保工作流程順暢\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-warning-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.pending\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"待審核\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.todayNew\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"今日新增\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6 text-success-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.approved\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"已核准\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-error-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-error-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.rejected\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"已拒絕\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-purple-500/5 to-pink-500/5 px-8 py-6 border-b border-gray-100/60\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-gray-900\",\n                                                            children: \"篩選條件\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 mt-1\",\n                                                            children: \"快速找到特定的請假申請\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: ()=>window.location.reload(),\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"重新載入\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"匯出報表\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-4 h-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"員工姓名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: searchParams.employee_name,\n                                                                onChange: (e)=>setSearchParams({\n                                                                        ...searchParams,\n                                                                        employee_name: e.target.value\n                                                                    }),\n                                                                placeholder: \"搜尋員工姓名...\",\n                                                                className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 pl-11 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-4 h-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"審核狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: searchParams.status,\n                                                        onChange: (e)=>setSearchParams({\n                                                                ...searchParams,\n                                                                status: e.target.value\n                                                            }),\n                                                        className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"所有狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"pending\",\n                                                                children: \"待審核\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"approved\",\n                                                                children: \"已核准\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"rejected\",\n                                                                children: \"已拒絕\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4 text-purple-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"請假類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: searchParams.leave_type,\n                                                        onChange: (e)=>setSearchParams({\n                                                                ...searchParams,\n                                                                leave_type: e.target.value\n                                                            }),\n                                                        className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"所有類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            Object.entries(leaveTypeMap).map((param)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: key,\n                                                                    children: value\n                                                                }, key, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 21\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between pt-6 border-t border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"primary\",\n                                                        onClick: handleSearch,\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"查詢\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"重置\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"共找到 \",\n                                                    filteredRequests.length,\n                                                    \" 筆申請\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 555,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-green-500/5 to-blue-500/5 px-8 py-6 border-b border-gray-100/60\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"請假申請列表\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: \"管理員工的請假申請\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 11\n                            }, this),\n                            dataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-16\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"載入請假申請中...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 13\n                            }, this) : filteredRequests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:block overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gradient-to-r from-green-500 to-blue-600 text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"申請人\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"請假類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"請假日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"天數\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"申請時間\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"操作\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: filteredRequests.map((request)=>{\n                                                        var _request_employee_name;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-white font-medium text-sm\",\n                                                                                    children: ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.charAt(0)) || \"?\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 681,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 680,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: request.employee_name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 686,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: request.employee_id\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 687,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-400\",\n                                                                                        children: request.department_name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 688,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 685,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-600 border border-blue-200\",\n                                                                        children: leaveTypeMap[request.leave_type] || request.leave_type || \"未知\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 693,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: new Date(request.start_date).toLocaleDateString(\"zh-TW\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        request.start_date !== request.end_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"至 \",\n                                                                                new Date(request.end_date).toLocaleDateString(\"zh-TW\")\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        request.days_count,\n                                                                        \" 天\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 705,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border \".concat(getStatusColor(request.status)),\n                                                                        children: [\n                                                                            getStatusIcon(request.status),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: getStatusText(request.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 711,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 709,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        new Date(request.created_at || request.submitted_at).toLocaleDateString(\"zh-TW\"),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: new Date(request.created_at || request.submitted_at).toLocaleTimeString(\"zh-TW\", {\n                                                                                hour: \"2-digit\",\n                                                                                minute: \"2-digit\"\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 716,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm font-medium\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>setSelectedRequest(request),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 730,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 725,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            request.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedRequest(request);\n                                                                                            setApprovalAction(\"approve\");\n                                                                                            setShowApprovalModal(true);\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-green-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                            lineNumber: 743,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 734,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedRequest(request);\n                                                                                            setApprovalAction(\"reject\");\n                                                                                            setShowApprovalModal(true);\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-red-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                            lineNumber: 754,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 745,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 724,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 723,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, request.id, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:hidden space-y-4 p-6\",\n                                        children: filteredRequests.map((request)=>{\n                                            var _request_employee_name;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-2xl p-6 border border-gray-200 shadow-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-medium text-sm\",\n                                                                            children: ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.charAt(0)) || \"?\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 773,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 772,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900\",\n                                                                                children: request.employee_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 778,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: request.department_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 779,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 777,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border \".concat(getStatusColor(request.status)),\n                                                                children: [\n                                                                    getStatusIcon(request.status),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-1\",\n                                                                        children: getStatusText(request.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 784,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-sm mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"類型:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 790,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: leaveTypeMap[request.leave_type] || request.leave_type || \"未知\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"天數:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 794,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: [\n                                                                            request.days_count,\n                                                                            \" 天\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"開始:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 798,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: new Date(request.start_date).toLocaleDateString(\"zh-TW\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 799,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"結束:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 804,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: new Date(request.end_date).toLocaleDateString(\"zh-TW\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 805,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: \"原因:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-900 text-sm mt-1\",\n                                                                children: request.reason\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 813,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"申請於 \",\n                                                                    new Date(request.created_at || request.submitted_at).toLocaleDateString(\"zh-TW\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>setSelectedRequest(request),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 826,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 821,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    request.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedRequest(request);\n                                                                                    setApprovalAction(\"approve\");\n                                                                                    setShowApprovalModal(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-green-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 839,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 830,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedRequest(request);\n                                                                                    setApprovalAction(\"reject\");\n                                                                                    setShowApprovalModal(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-red-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 850,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 841,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 820,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, request.id, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"查無請假申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"請調整搜尋條件或等待新的申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 861,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 461,\n                columnNumber: 7\n            }, this),\n            showApprovalModal && selectedRequest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-3xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: approvalAction === \"approve\" ? \"核准請假申請\" : \"拒絕請假申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowApprovalModal(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 875,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"申請詳情\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 890,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"申請人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 892,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"部門:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.department_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 896,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假類型:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: leaveTypeMap[selectedRequest.leave_type] || selectedRequest.leave_type || \"未知\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假天數:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: [\n                                                            selectedRequest.days_count,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 906,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"開始日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.start_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 910,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 908,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"結束日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.end_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedRequest.substitute_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"代理人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.substitute_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"緊急聯絡:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.emergency_contact\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 928,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 891,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"請假原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900 mt-1\",\n                                                children: selectedRequest.reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 889,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: approvalAction === \"approve\" ? \"核准意見（可選）\" : \"拒絕原因（必填）\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 939,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: approvalComment,\n                                        onChange: (e)=>setApprovalComment(e.target.value),\n                                        rows: 4,\n                                        className: \"w-full px-3 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none\",\n                                        placeholder: approvalAction === \"approve\" ? \"輸入核准意見...\" : \"請說明拒絕原因...\",\n                                        required: approvalAction === \"reject\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 938,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setShowApprovalModal(false),\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: approvalAction === \"approve\" ? \"success\" : \"error\",\n                                        onClick: handleApproval,\n                                        disabled: approvalAction === \"reject\" && !approvalComment.trim(),\n                                        className: \"flex items-center space-x-2\",\n                                        children: approvalAction === \"approve\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 972,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"核准申請\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 973,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 977,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"拒絕申請\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 978,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 957,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 874,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 873,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 872,\n                columnNumber: 9\n            }, this),\n            selectedRequest && !showApprovalModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-3xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"請假申請詳情\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 994,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedRequest(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 1000,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 993,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"申請人資訊\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"姓名:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"員工編號:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_id\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1014,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1012,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"部門:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1017,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.department_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1016,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"緊急聯絡:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1021,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.emergency_contact\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1022,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1020,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1007,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1005,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"請假資訊\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假類型:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: leaveTypeMap[selectedRequest.leave_type] || selectedRequest.leave_type || \"未知\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假天數:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: [\n                                                            selectedRequest.days_count,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1037,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"開始日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1040,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.start_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1041,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"結束日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.end_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedRequest.substitute_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"代理人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1053,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.substitute_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1054,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1052,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1030,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"請假原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1059,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900 mt-1\",\n                                                children: selectedRequest.reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1060,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1058,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1028,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-2xl p-6 mb-6 \".concat(selectedRequest.status === \"approved\" ? \"bg-green-50\" : selectedRequest.status === \"rejected\" ? \"bg-red-50\" : \"bg-yellow-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"審核狀態\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1068,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border \".concat(getStatusColor(selectedRequest.status)),\n                                                children: [\n                                                    getStatusIcon(selectedRequest.status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1\",\n                                                        children: getStatusText(selectedRequest.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1072,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1070,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"申請於 \",\n                                                    new Date(selectedRequest.created_at || selectedRequest.submitted_at).toLocaleString(\"zh-TW\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1074,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1069,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedRequest.approved_by && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"審核人:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1080,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-gray-900\",\n                                                children: selectedRequest.approved_by\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1081,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-4 text-gray-500\",\n                                                children: \"審核時間:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-gray-900\",\n                                                children: selectedRequest.approved_at && new Date(selectedRequest.approved_at).toLocaleString(\"zh-TW\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1083,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1079,\n                                        columnNumber: 19\n                                    }, this),\n                                    selectedRequest.rejection_reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 p-3 bg-red-100 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-700 text-sm font-medium\",\n                                                children: \"拒絕原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1090,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-800 text-sm mt-1\",\n                                                children: selectedRequest.rejection_reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1091,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1089,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1065,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end space-x-4\",\n                                children: [\n                                    selectedRequest.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"success\",\n                                                onClick: ()=>{\n                                                    setApprovalAction(\"approve\");\n                                                    setShowApprovalModal(true);\n                                                },\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1108,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"核准\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1109,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1100,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"error\",\n                                                onClick: ()=>{\n                                                    setApprovalAction(\"reject\");\n                                                    setShowApprovalModal(true);\n                                                },\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1119,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"拒絕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1111,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setSelectedRequest(null),\n                                        children: \"關閉\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1124,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1097,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 992,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 991,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 990,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n        lineNumber: 429,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaveApprovalPage, \"Ar0WQKmFVdQPYkPfgrKnB00pfUc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = LeaveApprovalPage;\nvar _c;\n$RefreshReg$(_c, \"LeaveApprovalPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/leave-approval/page.tsx\n"));

/***/ })

});