"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/leave-approval/page",{

/***/ "(app-pages-browser)/./src/app/admin/leave-approval/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/admin/leave-approval/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeaveApprovalPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,CheckCircle,Clock,Download,Eye,FileText,Filter,PlusCircle,RefreshCw,Search,User,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LeaveApprovalPage() {\n    _s();\n    const { user, login: authLogin, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [leaveRequests, setLeaveRequests] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dataLoading, setDataLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [autoLoginLoading, setAutoLoginLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedRequest, setSelectedRequest] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showApprovalModal, setShowApprovalModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [approvalAction, setApprovalAction] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"approve\");\n    const [approvalComment, setApprovalComment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [searchParams, setSearchParams] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        employee_name: \"\",\n        status: \"\",\n        leave_type: \"\"\n    });\n    const [filteredRequests, setFilteredRequests] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        pending: 0,\n        todayNew: 0,\n        approved: 0,\n        rejected: 0\n    });\n    // 請假類型對應\n    const leaveTypeMap = {\n        annual: \"年假\",\n        sick: \"病假\",\n        personal: \"事假\",\n        maternity: \"產假\",\n        paternity: \"陪產假\",\n        marriage: \"婚假\",\n        funeral: \"喪假\",\n        compensatory: \"補休\",\n        other: \"其他\"\n    };\n    // 自動登入功能 - 使用管理員測試帳號\n    const handleAutoLogin = async ()=>{\n        setAutoLoginLoading(true);\n        try {\n            console.log(\"開始自動登入管理員帳號\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_6__.login)({\n                employee_id: \"admin\",\n                password: \"admin123\"\n            });\n            if (response && response.user) {\n                const userData = response.user;\n                const user = {\n                    id: userData.employee_id,\n                    name: userData.employee_name,\n                    employee_id: userData.employee_code,\n                    department_id: userData.department_id,\n                    position: userData.role_id === 999 ? \"系統管理員\" : \"員工\",\n                    email: userData.email,\n                    role_id: userData.role_id,\n                    department_name: userData.department_name\n                };\n                authLogin(user);\n                console.log(\"自動登入成功:\", user);\n            } else {\n                console.error(\"自動登入失敗:\", response);\n            }\n        } catch (error) {\n            console.error(\"自動登入錯誤:\", error);\n        } finally{\n            setAutoLoginLoading(false);\n        }\n    };\n    // 頁面載入時自動登入（如果未登入）\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!loading && !user) {\n            console.log(\"檢測到未登入，執行自動登入\");\n            handleAutoLogin();\n        }\n    }, [\n        loading,\n        user\n    ]);\n    // 載入真實的請假申請數據\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchLeaveRequests = async ()=>{\n            try {\n                setDataLoading(true);\n                const apiBaseUrl = window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\" ? \"http://localhost:7072\" : \"http://\".concat(window.location.hostname, \":7072\");\n                const response = await fetch(\"\".concat(apiBaseUrl, \"/api/leave-requests\"));\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch leave requests\");\n                }\n                const data = await response.json();\n                const rawRequests = data.records || [];\n                // 映射 API 數據到前端期望的格式\n                const requests = rawRequests.map((item)=>{\n                    // 安全的日期計算\n                    let daysCount = 1;\n                    try {\n                        const startDate = new Date(item.start_date);\n                        const endDate = new Date(item.end_date);\n                        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {\n                            daysCount = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n                        }\n                    } catch (error) {\n                        console.warn(\"日期計算錯誤:\", item.start_date, item.end_date, error);\n                    }\n                    // 處理附件數據\n                    let attachments = [];\n                    if (item.attachments) {\n                        try {\n                            attachments = typeof item.attachments === \"string\" ? JSON.parse(item.attachments) : item.attachments;\n                        } catch (error) {\n                            console.warn(\"解析附件數據失敗:\", item.attachments, error);\n                            attachments = [];\n                        }\n                    }\n                    return {\n                        id: item.id,\n                        employee_id: item.employee_code || item.employee_id || \"\",\n                        employee_name: item.employee_name || \"未知員工\",\n                        department_name: item.department_name || \"未知部門\",\n                        leave_type: item.leave_type,\n                        start_date: item.start_date,\n                        end_date: item.end_date,\n                        days_count: daysCount,\n                        reason: item.reason || \"無\",\n                        status: item.status,\n                        submitted_at: item.created_at,\n                        created_at: item.created_at,\n                        approved_by: item.approver_name,\n                        approved_at: item.approved_at,\n                        rejection_reason: item.comment,\n                        emergency_contact: item.emergency_contact || \"無\",\n                        substitute_id: item.substitute_id,\n                        substitute_name: item.substitute_name || \"無\",\n                        attachments: attachments\n                    };\n                });\n                console.log(\"API 原始數據筆數:\", rawRequests.length);\n                console.log(\"映射後數據筆數:\", requests.length);\n                console.log(\"前3筆映射後的數據:\", requests.slice(0, 3));\n                setLeaveRequests(requests);\n                setFilteredRequests(requests);\n                // 計算統計數據\n                const newStats = {\n                    pending: requests.filter((r)=>r.status === \"pending\").length,\n                    todayNew: requests.filter((r)=>new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()).length,\n                    approved: requests.filter((r)=>r.status === \"approved\").length,\n                    rejected: requests.filter((r)=>r.status === \"rejected\").length\n                };\n                setStats(newStats);\n            } catch (error) {\n                console.error(\"Error fetching leave requests:\", error);\n                // 如果API失敗，設置空數據\n                setLeaveRequests([]);\n                setFilteredRequests([]);\n                setStats({\n                    pending: 0,\n                    todayNew: 0,\n                    approved: 0,\n                    rejected: 0\n                });\n            } finally{\n                setDataLoading(false);\n            }\n        };\n        fetchLeaveRequests();\n    }, []);\n    // 搜索和篩選\n    const handleSearch = ()=>{\n        let filtered = leaveRequests;\n        if (searchParams.employee_name) {\n            filtered = filtered.filter((request)=>{\n                var _request_employee_name;\n                return (_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.toLowerCase().includes(searchParams.employee_name.toLowerCase());\n            });\n        }\n        if (searchParams.status) {\n            filtered = filtered.filter((request)=>request.status === searchParams.status);\n        }\n        if (searchParams.leave_type) {\n            filtered = filtered.filter((request)=>request.leave_type === searchParams.leave_type);\n        }\n        setFilteredRequests(filtered);\n    };\n    const handleReset = ()=>{\n        setSearchParams({\n            employee_name: \"\",\n            status: \"\",\n            leave_type: \"\"\n        });\n        setFilteredRequests(leaveRequests);\n    };\n    // 處理審核\n    const handleApproval = async ()=>{\n        if (!selectedRequest) return;\n        try {\n            // 調用真實的API\n            const apiBaseUrl = window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\" ? \"http://localhost:7072\" : \"http://\".concat(window.location.hostname, \":7072\");\n            const response = await fetch(\"\".concat(apiBaseUrl, \"/api/approval/leaves/\").concat(selectedRequest.id), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: approvalAction,\n                    comment: approvalAction === \"reject\" ? approvalComment : undefined\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update leave request\");\n            }\n            // 重新載入數據\n            const fetchResponse = await fetch(\"\".concat(apiBaseUrl, \"/api/leave-requests\"));\n            if (fetchResponse.ok) {\n                const data = await fetchResponse.json();\n                const rawRequests = data.records || [];\n                // 映射 API 數據到前端期望的格式\n                const requests = rawRequests.map((item)=>{\n                    // 安全的日期計算\n                    let daysCount = 1;\n                    try {\n                        const startDate = new Date(item.start_date);\n                        const endDate = new Date(item.end_date);\n                        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {\n                            daysCount = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n                        }\n                    } catch (error) {\n                        console.warn(\"日期計算錯誤:\", item.start_date, item.end_date, error);\n                    }\n                    return {\n                        id: item.id,\n                        employee_id: item.employee_code || item.employee_id || \"\",\n                        employee_name: item.employee_name || \"未知員工\",\n                        department_name: item.department_name || \"未知部門\",\n                        leave_type: item.leave_type,\n                        start_date: item.start_date,\n                        end_date: item.end_date,\n                        days_count: daysCount,\n                        reason: item.reason || \"無\",\n                        status: item.status,\n                        submitted_at: item.created_at,\n                        created_at: item.created_at,\n                        approved_by: item.approver_name,\n                        approved_at: item.approved_at,\n                        rejection_reason: item.comment,\n                        emergency_contact: item.emergency_contact || \"無\",\n                        substitute_id: item.substitute_id,\n                        substitute_name: item.substitute_name || \"無\"\n                    };\n                });\n                setLeaveRequests(requests);\n                setFilteredRequests(requests.filter((request)=>{\n                    let match = true;\n                    if (searchParams.employee_name) {\n                        var _request_employee_name;\n                        match = match && ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.toLowerCase().includes(searchParams.employee_name.toLowerCase()));\n                    }\n                    if (searchParams.status) {\n                        match = match && request.status === searchParams.status;\n                    }\n                    if (searchParams.leave_type) {\n                        match = match && request.leave_type === searchParams.leave_type;\n                    }\n                    return match;\n                }));\n                // 更新統計\n                const newStats = {\n                    pending: requests.filter((r)=>r.status === \"pending\").length,\n                    todayNew: requests.filter((r)=>new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()).length,\n                    approved: requests.filter((r)=>r.status === \"approved\").length,\n                    rejected: requests.filter((r)=>r.status === \"rejected\").length\n                };\n                setStats(newStats);\n            }\n            setShowApprovalModal(false);\n            setSelectedRequest(null);\n            setApprovalComment(\"\");\n        } catch (error) {\n            console.error(\"Error updating leave request:\", error);\n            alert(\"審核失敗，請稍後再試\");\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"text-warning-600 bg-warning-50 border-warning-200\";\n            case \"approved\":\n                return \"text-success-600 bg-success-50 border-success-200\";\n            case \"rejected\":\n                return \"text-error-600 bg-error-50 border-error-200\";\n            default:\n                return \"text-neutral-600 bg-neutral-50 border-neutral-200\";\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"待審核\";\n            case \"approved\":\n                return \"已核准\";\n            case \"rejected\":\n                return \"已拒絕\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 30\n                }, this);\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 31\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 31\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    if (loading || autoLoginLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: autoLoginLoading ? \"自動登入中...\" : \"載入中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 389,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n            lineNumber: 388,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user || user.role_id !== 999) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"權限不足\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"您需要管理員權限才能訪問此頁面\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>router.push(\"/admin\"),\n                        children: \"返回管理後台\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 402,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n            lineNumber: 401,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold mb-2 text-white\",\n                                    children: \"請假審核管理\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            href: \"/admin\",\n                                            className: \"inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white group-hover:text-indigo-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-white group-hover:text-indigo-100\",\n                                                    children: \"返回\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-base font-medium\",\n                                            children: \"管理待審核的請假申請與其他審核事項\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-white\",\n                                            children: \"管理員模式\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-indigo-100\",\n                                            children: \"請假審核\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: \"請假審核管理中心\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600\",\n                                children: \"處理員工請假申請，確保工作流程順暢\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-warning-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.pending\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"待審核\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.todayNew\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"今日新增\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6 text-success-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.approved\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"已核准\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-error-50 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-error-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.rejected\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"已拒絕\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-purple-500/5 to-pink-500/5 px-8 py-6 border-b border-gray-100/60\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-gray-900\",\n                                                            children: \"篩選條件\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 mt-1\",\n                                                            children: \"快速找到特定的請假申請\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: ()=>window.location.reload(),\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"重新載入\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"匯出報表\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-4 h-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"員工姓名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: searchParams.employee_name,\n                                                                onChange: (e)=>setSearchParams({\n                                                                        ...searchParams,\n                                                                        employee_name: e.target.value\n                                                                    }),\n                                                                placeholder: \"搜尋員工姓名...\",\n                                                                className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 pl-11 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-4 h-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"審核狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: searchParams.status,\n                                                        onChange: (e)=>setSearchParams({\n                                                                ...searchParams,\n                                                                status: e.target.value\n                                                            }),\n                                                        className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"所有狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"pending\",\n                                                                children: \"待審核\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"approved\",\n                                                                children: \"已核准\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"rejected\",\n                                                                children: \"已拒絕\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4 text-purple-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-semibold text-gray-700\",\n                                                                children: \"請假類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: searchParams.leave_type,\n                                                        onChange: (e)=>setSearchParams({\n                                                                ...searchParams,\n                                                                leave_type: e.target.value\n                                                            }),\n                                                        className: \"w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"所有類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            Object.entries(leaveTypeMap).map((param)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: key,\n                                                                    children: value\n                                                                }, key, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 21\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between pt-6 border-t border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"primary\",\n                                                        onClick: handleSearch,\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"查詢\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: handleReset,\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"重置\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"共找到 \",\n                                                    filteredRequests.length,\n                                                    \" 筆申請\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-green-500/5 to-blue-500/5 px-8 py-6 border-b border-gray-100/60\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"請假申請列表\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: \"管理員工的請假申請\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 11\n                            }, this),\n                            dataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-16\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"載入請假申請中...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 13\n                            }, this) : filteredRequests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:block overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gradient-to-r from-green-500 to-blue-600 text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"申請人\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"請假類型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"請假日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"天數\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"狀態\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 656,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"申請時間\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-bold uppercase tracking-wider\",\n                                                                children: \"操作\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: filteredRequests.map((request)=>{\n                                                        var _request_employee_name;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-white font-medium text-sm\",\n                                                                                    children: ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.charAt(0)) || \"?\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 667,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 666,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: request.employee_name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 672,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: request.employee_id\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 673,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-400\",\n                                                                                        children: request.department_name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 674,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 671,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 665,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-600 border border-blue-200\",\n                                                                        children: leaveTypeMap[request.leave_type] || request.leave_type || \"未知\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: new Date(request.start_date).toLocaleDateString(\"zh-TW\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        request.start_date !== request.end_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"至 \",\n                                                                                new Date(request.end_date).toLocaleDateString(\"zh-TW\")\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 686,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        request.days_count,\n                                                                        \" 天\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border \".concat(getStatusColor(request.status)),\n                                                                        children: [\n                                                                            getStatusIcon(request.status),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: getStatusText(request.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 695,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                    children: [\n                                                                        new Date(request.created_at || request.submitted_at).toLocaleDateString(\"zh-TW\"),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: new Date(request.created_at || request.submitted_at).toLocaleTimeString(\"zh-TW\", {\n                                                                                hour: \"2-digit\",\n                                                                                minute: \"2-digit\"\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 702,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 text-sm font-medium\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>setSelectedRequest(request),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 716,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 711,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            request.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedRequest(request);\n                                                                                            setApprovalAction(\"approve\");\n                                                                                            setShowApprovalModal(true);\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-green-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                            lineNumber: 729,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 720,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedRequest(request);\n                                                                                            setApprovalAction(\"reject\");\n                                                                                            setShowApprovalModal(true);\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-red-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                            lineNumber: 740,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                        lineNumber: 731,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, request.id, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:hidden space-y-4 p-6\",\n                                        children: filteredRequests.map((request)=>{\n                                            var _request_employee_name;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-2xl p-6 border border-gray-200 shadow-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-medium text-sm\",\n                                                                            children: ((_request_employee_name = request.employee_name) === null || _request_employee_name === void 0 ? void 0 : _request_employee_name.charAt(0)) || \"?\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 759,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 758,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900\",\n                                                                                children: request.employee_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 764,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: request.department_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 765,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 763,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border \".concat(getStatusColor(request.status)),\n                                                                children: [\n                                                                    getStatusIcon(request.status),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-1\",\n                                                                        children: getStatusText(request.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 770,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-sm mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"類型:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 776,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: leaveTypeMap[request.leave_type] || request.leave_type || \"未知\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 777,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"天數:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 780,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: [\n                                                                            request.days_count,\n                                                                            \" 天\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 781,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"開始:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 784,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: new Date(request.start_date).toLocaleDateString(\"zh-TW\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"結束:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 790,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-gray-900\",\n                                                                        children: new Date(request.end_date).toLocaleDateString(\"zh-TW\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: \"原因:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-900 text-sm mt-1\",\n                                                                children: request.reason\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 799,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 797,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"申請於 \",\n                                                                    new Date(request.created_at || request.submitted_at).toLocaleDateString(\"zh-TW\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>setSelectedRequest(request),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                            lineNumber: 812,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                        lineNumber: 807,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    request.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedRequest(request);\n                                                                                    setApprovalAction(\"approve\");\n                                                                                    setShowApprovalModal(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-green-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 825,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 816,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedRequest(request);\n                                                                                    setApprovalAction(\"reject\");\n                                                                                    setShowApprovalModal(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-red-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                    lineNumber: 836,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                                lineNumber: 827,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, request.id, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"查無請假申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 849,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"請調整搜尋條件或等待新的申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 847,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 622,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 447,\n                columnNumber: 7\n            }, this),\n            showApprovalModal && selectedRequest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-3xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: approvalAction === \"approve\" ? \"核准請假申請\" : \"拒絕請假申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowApprovalModal(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 870,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 861,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"申請詳情\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"申請人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 879,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 878,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"部門:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.department_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 882,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假類型:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: leaveTypeMap[selectedRequest.leave_type] || selectedRequest.leave_type || \"未知\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 886,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假天數:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 891,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: [\n                                                            selectedRequest.days_count,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 890,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"開始日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.start_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"結束日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.end_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedRequest.substitute_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"代理人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.substitute_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"緊急聯絡:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.emergency_contact\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"請假原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900 mt-1\",\n                                                children: selectedRequest.reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 917,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 875,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: approvalAction === \"approve\" ? \"核准意見（可選）\" : \"拒絕原因（必填）\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 925,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: approvalComment,\n                                        onChange: (e)=>setApprovalComment(e.target.value),\n                                        rows: 4,\n                                        className: \"w-full px-3 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none\",\n                                        placeholder: approvalAction === \"approve\" ? \"輸入核准意見...\" : \"請說明拒絕原因...\",\n                                        required: approvalAction === \"reject\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setShowApprovalModal(false),\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: approvalAction === \"approve\" ? \"success\" : \"error\",\n                                        onClick: handleApproval,\n                                        disabled: approvalAction === \"reject\" && !approvalComment.trim(),\n                                        className: \"flex items-center space-x-2\",\n                                        children: approvalAction === \"approve\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 958,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"核准申請\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 959,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 963,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"拒絕申請\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                    lineNumber: 964,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 950,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 943,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 860,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 859,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 858,\n                columnNumber: 9\n            }, this),\n            selectedRequest && !showApprovalModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-3xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"請假申請詳情\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 980,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedRequest(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 981,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 979,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"申請人資訊\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 992,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"姓名:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 995,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 994,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"員工編號:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 999,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.employee_id\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1000,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 998,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"部門:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1003,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.department_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"緊急聯絡:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1007,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.emergency_contact\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1006,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 993,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-2xl p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"請假資訊\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1015,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假類型:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: leaveTypeMap[selectedRequest.leave_type] || selectedRequest.leave_type || \"未知\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1019,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1017,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"請假天數:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1022,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: [\n                                                            selectedRequest.days_count,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1021,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"開始日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1026,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.start_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1027,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1025,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"結束日期:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: new Date(selectedRequest.end_date).toLocaleDateString(\"zh-TW\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedRequest.substitute_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"代理人:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-gray-900\",\n                                                        children: selectedRequest.substitute_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1040,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1016,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"請假原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900 mt-1\",\n                                                children: selectedRequest.reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1044,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1014,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-2xl p-6 mb-6 \".concat(selectedRequest.status === \"approved\" ? \"bg-green-50\" : selectedRequest.status === \"rejected\" ? \"bg-red-50\" : \"bg-yellow-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-4\",\n                                        children: \"審核狀態\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1054,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border \".concat(getStatusColor(selectedRequest.status)),\n                                                children: [\n                                                    getStatusIcon(selectedRequest.status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1\",\n                                                        children: getStatusText(selectedRequest.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"申請於 \",\n                                                    new Date(selectedRequest.created_at || selectedRequest.submitted_at).toLocaleString(\"zh-TW\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1060,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedRequest.approved_by && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"審核人:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1066,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-gray-900\",\n                                                children: selectedRequest.approved_by\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1067,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-4 text-gray-500\",\n                                                children: \"審核時間:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1068,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-gray-900\",\n                                                children: selectedRequest.approved_at && new Date(selectedRequest.approved_at).toLocaleString(\"zh-TW\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1069,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1065,\n                                        columnNumber: 19\n                                    }, this),\n                                    selectedRequest.rejection_reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 p-3 bg-red-100 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-700 text-sm font-medium\",\n                                                children: \"拒絕原因:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-800 text-sm mt-1\",\n                                                children: selectedRequest.rejection_reason\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1077,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1075,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1051,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end space-x-4\",\n                                children: [\n                                    selectedRequest.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"success\",\n                                                onClick: ()=>{\n                                                    setApprovalAction(\"approve\");\n                                                    setShowApprovalModal(true);\n                                                },\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1094,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"核准\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1095,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1086,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"error\",\n                                                onClick: ()=>{\n                                                    setApprovalAction(\"reject\");\n                                                    setShowApprovalModal(true);\n                                                },\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_CheckCircle_Clock_Download_Eye_FileText_Filter_PlusCircle_RefreshCw_Search_User_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1105,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"拒絕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                        lineNumber: 1106,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                                lineNumber: 1097,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setSelectedRequest(null),\n                                        children: \"關閉\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                        lineNumber: 1110,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                                lineNumber: 1083,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                        lineNumber: 978,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                    lineNumber: 977,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n                lineNumber: 976,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/leave-approval/page.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaveApprovalPage, \"Ar0WQKmFVdQPYkPfgrKnB00pfUc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = LeaveApprovalPage;\nvar _c;\n$RefreshReg$(_c, \"LeaveApprovalPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/leave-approval/page.tsx\n"));

/***/ })

});