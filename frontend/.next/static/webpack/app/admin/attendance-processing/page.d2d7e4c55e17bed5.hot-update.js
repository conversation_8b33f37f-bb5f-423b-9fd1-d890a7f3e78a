"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/attendance-processing/page",{

/***/ "(app-pages-browser)/./src/app/admin/attendance-processing/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/admin/attendance-processing/page.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AttendanceProcessingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AttendanceProcessingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 狀態管理\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start_date: \"\",\n        end_date: \"\"\n    });\n    const [employeeScope, setEmployeeScope] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"all\"\n    });\n    const [processingOptions, setProcessingOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        calculate_late_early: true,\n        calculate_overtime: true,\n        integrate_leaves: true,\n        overwrite_existing: false\n    });\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        pendingDays: 0,\n        pendingEmployees: 0,\n        estimatedTime: \"0秒\",\n        errorRecords: 0\n    });\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            type: \"info\",\n            message: \"系統就緒，等待處理指令\",\n            timestamp: new Date().toLocaleTimeString()\n        }\n    ]);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewData, setPreviewData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewLoading, setPreviewLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoMode, setAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化日期\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const formatDate = (date)=>date.toISOString().split(\"T\")[0];\n        setDateRange({\n            start_date: formatDate(yesterday),\n            end_date: formatDate(yesterday)\n        });\n    }, []);\n    // 更新統計資料\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        updateStatistics();\n    }, [\n        dateRange,\n        employeeScope\n    ]);\n    const updateStatistics = ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) return;\n        // 計算天數\n        const start = new Date(dateRange.start_date);\n        const end = new Date(dateRange.end_date);\n        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n        // 模擬計算員工數量\n        let employees = 156;\n        switch(employeeScope.type){\n            case \"department\":\n                employees = 45;\n                break;\n            case \"specific\":\n                employees = 12;\n                break;\n        }\n        // 估算時間\n        const estimatedSeconds = Math.ceil(days * employees * 0.5);\n        let timeText = \"\";\n        if (estimatedSeconds < 60) {\n            timeText = \"\".concat(estimatedSeconds, \"秒\");\n        } else if (estimatedSeconds < 3600) {\n            timeText = \"\".concat(Math.ceil(estimatedSeconds / 60), \"分鐘\");\n        } else {\n            timeText = \"\".concat(Math.ceil(estimatedSeconds / 3600), \"小時\");\n        }\n        setStatistics({\n            pendingDays: days,\n            pendingEmployees: employees,\n            estimatedTime: timeText,\n            errorRecords: Math.floor(Math.random() * 10)\n        });\n    };\n    const handleQuickDateSelect = (type)=>{\n        const today = new Date();\n        let startDate, endDate;\n        switch(type){\n            case \"1\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                break;\n            case \"7\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                startDate.setDate(startDate.getDate() - 6);\n                break;\n            case \"30\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                startDate.setDate(startDate.getDate() - 29);\n                break;\n            case \"current-month\":\n                startDate = new Date(today.getFullYear(), today.getMonth(), 1);\n                endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);\n                break;\n            default:\n                return;\n        }\n        setDateRange({\n            start_date: startDate.toISOString().split(\"T\")[0],\n            end_date: endDate.toISOString().split(\"T\")[0]\n        });\n    };\n    const addLog = (type, message)=>{\n        const newLog = {\n            type,\n            message,\n            timestamp: new Date().toLocaleTimeString()\n        };\n        setLogs((prev)=>[\n                newLog,\n                ...prev.slice(0, 19)\n            ]) // 保持最多20條記錄\n        ;\n    };\n    const clearLogs = ()=>{\n        setLogs([\n            {\n                type: \"info\",\n                message: \"系統就緒，等待處理指令\",\n                timestamp: new Date().toLocaleTimeString()\n            }\n        ]);\n    };\n    const startProcessing = async ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) {\n            alert(\"請選擇日期範圍\");\n            return;\n        }\n        setIsProcessing(true);\n        setProgress(0);\n        addLog(\"info\", \"開始處理考勤資料...\");\n        try {\n            // 準備API請求資料\n            const processingData = {\n                date_range: dateRange,\n                employee_scope: employeeScope,\n                processing_options: processingOptions\n            };\n            addLog(\"info\", \"發送處理請求到後端...\");\n            // 嘗試調用後端API，如果失敗則使用模擬數據\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/execute\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(processingData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"API請求失敗: \".concat(response.status));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"處理啟動失敗\");\n                }\n                const processingId = result.processing_id;\n                addLog(\"success\", \"處理已啟動，處理ID: \".concat(processingId));\n                addLog(\"info\", \"預計處理 \".concat(result.total_records, \" 條記錄\"));\n                // 開始監控處理進度\n                monitorProcessingProgress(processingId);\n            } catch (apiError) {\n                // API調用失敗，使用模擬處理\n                addLog(\"warning\", \"後端API不可用，使用模擬處理模式\");\n                simulateProcessing();\n            }\n        } catch (error) {\n            addLog(\"error\", \"處理失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const monitorProcessingProgress = async (processingId)=>{\n        const checkProgress = async ()=>{\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/status/\".concat(processingId));\n                if (!response.ok) {\n                    throw new Error(\"狀態查詢失敗: \".concat(response.status));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"狀態查詢失敗\");\n                }\n                const statusData = result.data;\n                // 更新進度\n                setProgress(statusData.progress);\n                addLog(\"info\", \"\".concat(statusData.current_step, \" (\").concat(statusData.progress, \"%)\"));\n                // 檢查是否完成\n                if (statusData.status === \"completed\") {\n                    addLog(\"success\", \"處理完成！\");\n                    addLog(\"info\", \"創建: \".concat(statusData.results.created, \", 更新: \").concat(statusData.results.updated, \", 失敗: \").concat(statusData.results.failed));\n                    // 顯示錯誤（如果有）\n                    if (statusData.errors.length > 0) {\n                        statusData.errors.forEach((error)=>{\n                            addLog(\"warning\", error);\n                        });\n                    }\n                    setTimeout(()=>{\n                        setIsProcessing(false);\n                        setProgress(0);\n                    }, 3000);\n                    return;\n                } else if (statusData.status === \"failed\") {\n                    addLog(\"error\", \"處理失敗\");\n                    statusData.errors.forEach((error)=>{\n                        addLog(\"error\", error);\n                    });\n                    setIsProcessing(false);\n                    setProgress(0);\n                    return;\n                }\n                // 繼續監控\n                setTimeout(checkProgress, 1000);\n            } catch (error) {\n                addLog(\"error\", \"監控進度失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n                setIsProcessing(false);\n                setProgress(0);\n            }\n        };\n        // 開始監控\n        setTimeout(checkProgress, 1000);\n    };\n    const handlePreview = async ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) {\n            alert(\"請選擇日期範圍\");\n            return;\n        }\n        setPreviewLoading(true);\n        try {\n            const previewRequest = {\n                date_range: dateRange,\n                employee_scope: employeeScope\n            };\n            const response = await fetch(\"http://localhost:7073/api/attendance/processing/preview\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(previewRequest)\n            });\n            if (!response.ok) {\n                throw new Error(\"預覽請求失敗: \".concat(response.status));\n            }\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.error || \"預覽失敗\");\n            }\n            setPreviewData(result.data);\n            setShowPreview(true);\n        } catch (error) {\n            addLog(\"error\", \"預覽失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n        } finally{\n            setPreviewLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/admin\",\n                                    className: \"flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"返回儀表板\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-px bg-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"考勤資料整理\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"自動處理模式：\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"autoMode\",\n                                            checked: autoMode,\n                                            onChange: (e)=>setAutoMode(e.target.checked),\n                                            className: \"sr-only\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"autoMode\",\n                                            className: \"relative inline-flex items-center cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-11 h-6 rounded-full transition-colors duration-200 \".concat(autoMode ? \"bg-blue-500\" : \"bg-gray-200\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-200 \".concat(autoMode ? \"translate-x-5\" : \"translate-x-0.5\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-green-600 font-medium\",\n                                            children: autoMode ? \"啟用\" : \"停用\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                lineNumber: 381,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"待處理天數\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: statistics.pendingDays\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-yellow-50 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"待處理員工\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: statistics.pendingEmployees\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"預估時間\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: statistics.estimatedTime\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-red-50 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"異常記錄\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: statistics.errorRecords\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-4 h-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"日期範圍設定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"開始日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: dateRange.start_date,\n                                                                onChange: (e)=>setDateRange((prev)=>({\n                                                                            ...prev,\n                                                                            start_date: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"結束日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: dateRange.end_date,\n                                                                onChange: (e)=>setDateRange((prev)=>({\n                                                                            ...prev,\n                                                                            end_date: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuickDateSelect(\"1\"),\n                                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                        children: \"昨天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuickDateSelect(\"7\"),\n                                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                        children: \"最近7天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuickDateSelect(\"30\"),\n                                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                        children: \"最近30天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuickDateSelect(\"current-month\"),\n                                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                        children: \"本月\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-yellow-50 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-4 h-4 text-yellow-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"員工範圍設定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"employeeScope\",\n                                                                value: \"all\",\n                                                                checked: employeeScope.type === \"all\",\n                                                                onChange: (e)=>setEmployeeScope({\n                                                                        type: \"all\"\n                                                                    }),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700\",\n                                                                children: \"所有員工\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"employeeScope\",\n                                                                value: \"department\",\n                                                                checked: employeeScope.type === \"department\",\n                                                                onChange: (e)=>setEmployeeScope({\n                                                                        type: \"department\",\n                                                                        department_ids: []\n                                                                    }),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700\",\n                                                                children: \"指定部門\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"employeeScope\",\n                                                                value: \"specific\",\n                                                                checked: employeeScope.type === \"specific\",\n                                                                onChange: (e)=>setEmployeeScope({\n                                                                        type: \"specific\",\n                                                                        employee_ids: []\n                                                                    }),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700\",\n                                                                children: \"指定員工\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"處理選項設定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 41\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-700\",\n                                                                        children: \"計算遲到時間\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 41\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: processingOptions.calculate_late_early,\n                                                                onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                            ...prev,\n                                                                            calculate_late_early: e.target.checked\n                                                                        })),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 604,\n                                                                        columnNumber: 41\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-700\",\n                                                                        children: \"計算加班時間\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 41\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: processingOptions.calculate_overtime,\n                                                                onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                            ...prev,\n                                                                            calculate_overtime: e.target.checked\n                                                                        })),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 617,\n                                                                        columnNumber: 41\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-700\",\n                                                                        children: \"整合請假資料\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 41\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: processingOptions.integrate_leaves,\n                                                                onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                            ...prev,\n                                                                            integrate_leaves: e.target.checked\n                                                                        })),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 630,\n                                                                        columnNumber: 41\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-700\",\n                                                                        children: \"覆蓋已存在的計算結果\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 41\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: processingOptions.overwrite_existing,\n                                                                onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                            ...prev,\n                                                                            overwrite_existing: e.target.checked\n                                                                        })),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: startProcessing,\n                                                            disabled: isProcessing,\n                                                            className: \"flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 \".concat(isProcessing ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl\"),\n                                                            children: [\n                                                                isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-5 h-5 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 45\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: isProcessing ? \"處理中...\" : \"開始處理\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handlePreview,\n                                                            disabled: previewLoading,\n                                                            className: \"flex items-center space-x-2 px-4 py-3 rounded-xl transition-colors \".concat(previewLoading ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                                                            children: [\n                                                                previewLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-4 h-4 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 45\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 676,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: previewLoading ? \"載入中...\" : \"預覽資料\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 33\n                                                }, this),\n                                                isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-32 bg-gray-200 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: \"\".concat(progress, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                Math.round(progress),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-purple-50 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-4 h-4 text-purple-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                            children: \"處理日誌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: clearLogs,\n                                                    className: \"flex items-center space-x-1 px-3 py-1 text-sm text-gray-500 hover:text-gray-700 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"清空\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                            children: logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3 p-3 rounded-lg bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full mt-2 flex-shrink-0 \".concat(log.type === \"success\" ? \"bg-green-500\" : log.type === \"error\" ? \"bg-red-500\" : log.type === \"warning\" ? \"bg-yellow-500\" : \"bg-blue-500\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-900\",\n                                                                    children: log.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                                    children: log.timestamp\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 37\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 698,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 17\n                    }, this),\n                    showPreview && previewData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-200 p-6 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"處理預覽\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowPreview(false),\n                                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 overflow-y-auto\",\n                                    style: {\n                                        maxHeight: \"calc(90vh - 140px)\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-blue-900 mb-3\",\n                                                        children: \"處理範圍預覽\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-700\",\n                                                                        children: \"日期範圍：\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            previewData.date_range.start_date,\n                                                                            \" 至 \",\n                                                                            previewData.date_range.end_date\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 758,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-700\",\n                                                                        children: \"員工數量：\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 761,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            previewData.total_employees,\n                                                                            \" 人\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-700\",\n                                                                        children: \"現有記錄：\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 765,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            previewData.existing_records,\n                                                                            \" 條\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 766,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-700\",\n                                                                        children: \"預計新增：\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 769,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            previewData.estimated_new_records,\n                                                                            \" 條\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 770,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 37\n                                            }, this),\n                                            previewData.employees && previewData.employees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"員工列表（前10名）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"w-full text-sm border border-gray-200 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    className: \"bg-gray-50\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left border-b\",\n                                                                                children: \"員工編號\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 783,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left border-b\",\n                                                                                children: \"員工姓名\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 784,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left border-b\",\n                                                                                children: \"部門\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 785,\n                                                                                columnNumber: 61\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 782,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 781,\n                                                                    columnNumber: 53\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-200\",\n                                                                    children: previewData.employees.map((employee, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            className: \"hover:bg-gray-50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-2\",\n                                                                                    children: employee.employee_id\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                    lineNumber: 791,\n                                                                                    columnNumber: 65\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-2\",\n                                                                                    children: employee.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                    lineNumber: 792,\n                                                                                    columnNumber: 65\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-2\",\n                                                                                    children: employee.department_name || \"未分配\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                    lineNumber: 793,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 61\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-yellow-900 mb-2\",\n                                                        children: \"⚠️ 處理注意事項\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-sm text-yellow-800 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• 處理過程中請勿關閉瀏覽器\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• 已計算的資料將被覆蓋（如有勾選覆蓋選項）\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• 建議在非高峰時段進行大量資料處理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 808,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• 處理完成後將自動生成處理報告\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 805,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 803,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 p-6 flex justify-end space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowPreview(false),\n                                            className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowPreview(false);\n                                                startProcessing();\n                                            },\n                                            className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"確認處理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 815,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 739,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                        lineNumber: 738,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                lineNumber: 417,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n        lineNumber: 379,\n        columnNumber: 9\n    }, this);\n}\n_s(AttendanceProcessingPage, \"e+4HUAret00qSiHjWTMzRCFCDvg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AttendanceProcessingPage;\nvar _c;\n$RefreshReg$(_c, \"AttendanceProcessingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/attendance-processing/page.tsx\n"));

/***/ })

});