"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/attendance-processing/page",{

/***/ "(app-pages-browser)/./src/app/admin/attendance-processing/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/admin/attendance-processing/page.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AttendanceProcessingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AttendanceProcessingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 狀態管理\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start_date: \"\",\n        end_date: \"\"\n    });\n    const [employeeScope, setEmployeeScope] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"all\"\n    });\n    const [processingOptions, setProcessingOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        calculate_late_early: true,\n        calculate_overtime: true,\n        integrate_leaves: true,\n        overwrite_existing: false\n    });\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        pendingDays: 0,\n        pendingEmployees: 0,\n        estimatedTime: \"0秒\",\n        errorRecords: 0\n    });\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            type: \"info\",\n            message: \"系統就緒，等待處理指令\",\n            timestamp: new Date().toLocaleTimeString()\n        }\n    ]);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewData, setPreviewData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewLoading, setPreviewLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoMode, setAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化日期\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const formatDate = (date)=>date.toISOString().split(\"T\")[0];\n        setDateRange({\n            start_date: formatDate(yesterday),\n            end_date: formatDate(yesterday)\n        });\n    }, []);\n    // 更新統計資料\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        updateStatistics();\n    }, [\n        dateRange,\n        employeeScope\n    ]);\n    const updateStatistics = ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) return;\n        // 計算天數\n        const start = new Date(dateRange.start_date);\n        const end = new Date(dateRange.end_date);\n        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n        // 模擬計算員工數量\n        let employees = 156;\n        switch(employeeScope.type){\n            case \"department\":\n                employees = 45;\n                break;\n            case \"specific\":\n                employees = 12;\n                break;\n        }\n        // 估算時間\n        const estimatedSeconds = Math.ceil(days * employees * 0.5);\n        let timeText = \"\";\n        if (estimatedSeconds < 60) {\n            timeText = \"\".concat(estimatedSeconds, \"秒\");\n        } else if (estimatedSeconds < 3600) {\n            timeText = \"\".concat(Math.ceil(estimatedSeconds / 60), \"分鐘\");\n        } else {\n            timeText = \"\".concat(Math.ceil(estimatedSeconds / 3600), \"小時\");\n        }\n        setStatistics({\n            pendingDays: days,\n            pendingEmployees: employees,\n            estimatedTime: timeText,\n            errorRecords: Math.floor(Math.random() * 10)\n        });\n    };\n    const handleQuickDateSelect = (type)=>{\n        const today = new Date();\n        let startDate, endDate;\n        switch(type){\n            case \"1\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                break;\n            case \"7\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                startDate.setDate(startDate.getDate() - 6);\n                break;\n            case \"30\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                startDate.setDate(startDate.getDate() - 29);\n                break;\n            case \"current-month\":\n                startDate = new Date(today.getFullYear(), today.getMonth(), 1);\n                endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);\n                break;\n            default:\n                return;\n        }\n        setDateRange({\n            start_date: startDate.toISOString().split(\"T\")[0],\n            end_date: endDate.toISOString().split(\"T\")[0]\n        });\n    };\n    const addLog = (type, message)=>{\n        const newLog = {\n            type,\n            message,\n            timestamp: new Date().toLocaleTimeString()\n        };\n        setLogs((prev)=>[\n                newLog,\n                ...prev.slice(0, 19)\n            ]) // 保持最多20條記錄\n        ;\n    };\n    const clearLogs = ()=>{\n        setLogs([\n            {\n                type: \"info\",\n                message: \"系統就緒，等待處理指令\",\n                timestamp: new Date().toLocaleTimeString()\n            }\n        ]);\n    };\n    const startProcessing = async ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) {\n            alert(\"請選擇日期範圍\");\n            return;\n        }\n        setIsProcessing(true);\n        setProgress(0);\n        addLog(\"info\", \"開始處理考勤資料...\");\n        try {\n            // 準備API請求資料\n            const processingData = {\n                date_range: dateRange,\n                employee_scope: employeeScope,\n                processing_options: processingOptions\n            };\n            addLog(\"info\", \"發送處理請求到後端...\");\n            // 嘗試調用後端API，如果失敗則使用模擬數據\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/execute\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(processingData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"API請求失敗: \".concat(response.status));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"處理啟動失敗\");\n                }\n                const processingId = result.processing_id;\n                addLog(\"success\", \"處理已啟動，處理ID: \".concat(processingId));\n                addLog(\"info\", \"預計處理 \".concat(result.total_records, \" 條記錄\"));\n                // 開始監控處理進度\n                monitorProcessingProgress(processingId);\n            } catch (apiError) {\n                // API調用失敗，使用模擬處理\n                addLog(\"warning\", \"後端API不可用，使用模擬處理模式\");\n                simulateProcessing();\n            }\n        } catch (error) {\n            addLog(\"error\", \"處理失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const monitorProcessingProgress = async (processingId)=>{\n        const checkProgress = async ()=>{\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/status/\".concat(processingId));\n                if (!response.ok) {\n                    throw new Error(\"狀態查詢失敗: \".concat(response.status));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"狀態查詢失敗\");\n                }\n                const statusData = result.data;\n                // 更新進度\n                setProgress(statusData.progress);\n                addLog(\"info\", \"\".concat(statusData.current_step, \" (\").concat(statusData.progress, \"%)\"));\n                // 檢查是否完成\n                if (statusData.status === \"completed\") {\n                    addLog(\"success\", \"處理完成！\");\n                    addLog(\"info\", \"創建: \".concat(statusData.results.created, \", 更新: \").concat(statusData.results.updated, \", 失敗: \").concat(statusData.results.failed));\n                    // 顯示錯誤（如果有）\n                    if (statusData.errors.length > 0) {\n                        statusData.errors.forEach((error)=>{\n                            addLog(\"warning\", error);\n                        });\n                    }\n                    setTimeout(()=>{\n                        setIsProcessing(false);\n                        setProgress(0);\n                    }, 3000);\n                    return;\n                } else if (statusData.status === \"failed\") {\n                    addLog(\"error\", \"處理失敗\");\n                    statusData.errors.forEach((error)=>{\n                        addLog(\"error\", error);\n                    });\n                    setIsProcessing(false);\n                    setProgress(0);\n                    return;\n                }\n                // 繼續監控\n                setTimeout(checkProgress, 1000);\n            } catch (error) {\n                addLog(\"error\", \"監控進度失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n                setIsProcessing(false);\n                setProgress(0);\n            }\n        };\n        // 開始監控\n        setTimeout(checkProgress, 1000);\n    };\n    const simulateProcessing = ()=>{\n        addLog(\"info\", \"開始模擬處理流程...\");\n        const steps = [\n            \"資料載入\",\n            \"時間計算\",\n            \"資料整合\",\n            \"儲存結果\"\n        ];\n        let currentStep = 0;\n        const interval = setInterval(()=>{\n            setProgress((prev)=>{\n                const newProgress = prev + Math.random() * 15 + 5;\n                if (newProgress >= 100) {\n                    clearInterval(interval);\n                    setProgress(100);\n                    addLog(\"success\", \"模擬處理完成！\");\n                    addLog(\"info\", \"成功處理 \".concat(statistics.pendingEmployees, \" 名員工的考勤資料\"));\n                    addLog(\"info\", \"創建: 15, 更新: 8, 失敗: 0\");\n                    setTimeout(()=>{\n                        setIsProcessing(false);\n                        setProgress(0);\n                    }, 3000);\n                    return 100;\n                }\n                // 更新步驟狀態\n                const stepIndex = Math.floor(newProgress / 25);\n                if (stepIndex > currentStep && stepIndex < steps.length) {\n                    addLog(\"info\", \"正在執行：\".concat(steps[stepIndex], \" (\").concat(Math.round(newProgress), \"%)\"));\n                    currentStep = stepIndex;\n                }\n                return newProgress;\n            });\n        }, 300);\n    };\n    const handlePreview = async ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) {\n            alert(\"請選擇日期範圍\");\n            return;\n        }\n        setPreviewLoading(true);\n        try {\n            const previewRequest = {\n                date_range: dateRange,\n                employee_scope: employeeScope\n            };\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/preview\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(previewRequest)\n                });\n                if (!response.ok) {\n                    throw new Error(\"預覽請求失敗: \".concat(response.status));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"預覽失敗\");\n                }\n                setPreviewData(result.data);\n                setShowPreview(true);\n            } catch (apiError) {\n                // API調用失敗，使用模擬數據\n                addLog(\"warning\", \"後端API不可用，使用模擬預覽數據\");\n                const mockPreviewData = {\n                    employees: [\n                        {\n                            id: 1,\n                            employee_id: \"E001\",\n                            name: \"黎麗玲\",\n                            department_name: \"技術部\"\n                        },\n                        {\n                            id: 2,\n                            employee_id: \"E002\",\n                            name: \"蔡秀娟\",\n                            department_name: \"技術部\"\n                        },\n                        {\n                            id: 3,\n                            employee_id: \"E003\",\n                            name: \"劉志偉\",\n                            department_name: \"業務部\"\n                        },\n                        {\n                            id: 4,\n                            employee_id: \"admin\",\n                            name: \"系統管理員\",\n                            department_name: \"管理部\"\n                        }\n                    ],\n                    total_employees: 4,\n                    date_range: dateRange,\n                    existing_records: 2,\n                    estimated_new_records: statistics.pendingEmployees - 2\n                };\n                setPreviewData(mockPreviewData);\n                setShowPreview(true);\n            }\n        } catch (error) {\n            addLog(\"error\", \"預覽失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n        } finally{\n            setPreviewLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold mb-2 text-white\",\n                                            children: \"考勤資料整理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/admin\",\n                                                    className: \"inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white group-hover:text-indigo-100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-white group-hover:text-indigo-100\",\n                                                            children: \"返回\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-indigo-100 text-base font-medium\",\n                                                    children: \"自動計算遲到、早退、加班時間，整合請假資料並更新考勤狀態\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-white\",\n                                                    children: \"管理員模式\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"考勤整理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex items-center justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-white\",\n                                        children: \"自動處理模式：\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoMode\",\n                                                checked: autoMode,\n                                                onChange: (e)=>setAutoMode(e.target.checked),\n                                                className: \"sr-only\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoMode\",\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-11 h-6 rounded-full transition-colors duration-200 \".concat(autoMode ? \"bg-green-500\" : \"bg-white/30\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-200 \".concat(autoMode ? \"translate-x-5\" : \"translate-x-0.5\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm text-white font-medium\",\n                                                children: autoMode ? \"啟用\" : \"停用\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"待處理天數\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                children: statistics.pendingDays\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"待處理員工\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent\",\n                                                children: statistics.pendingEmployees\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"預估時間\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent\",\n                                                children: statistics.estimatedTime\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"異常記錄\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent\",\n                                                children: statistics.errorRecords\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 495,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                    children: \"日期範圍設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"開始日期\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            value: dateRange.start_date,\n                                                            onChange: (e)=>setDateRange((prev)=>({\n                                                                        ...prev,\n                                                                        start_date: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"結束日期\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            value: dateRange.end_date,\n                                                            onChange: (e)=>setDateRange((prev)=>({\n                                                                        ...prev,\n                                                                        end_date: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"1\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"昨天\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"7\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"最近7天\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"30\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"最近30天\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"current-month\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"本月\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent\",\n                                                    children: \"員工範圍設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"employeeScope\",\n                                                            value: \"all\",\n                                                            checked: employeeScope.type === \"all\",\n                                                            onChange: (e)=>setEmployeeScope({\n                                                                    type: \"all\"\n                                                                }),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"所有員工\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"employeeScope\",\n                                                            value: \"department\",\n                                                            checked: employeeScope.type === \"department\",\n                                                            onChange: (e)=>setEmployeeScope({\n                                                                    type: \"department\",\n                                                                    department_ids: []\n                                                                }),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"指定部門\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"employeeScope\",\n                                                            value: \"specific\",\n                                                            checked: employeeScope.type === \"specific\",\n                                                            onChange: (e)=>setEmployeeScope({\n                                                                    type: \"specific\",\n                                                                    employee_ids: []\n                                                                }),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"指定員工\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent\",\n                                                    children: \"處理選項設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"計算遲到時間\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.calculate_late_early,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        calculate_late_early: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"計算加班時間\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.calculate_overtime,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        calculate_overtime: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"整合請假資料\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.integrate_leaves,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        integrate_leaves: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"覆蓋已存在的計算結果\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.overwrite_existing,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        overwrite_existing: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: startProcessing,\n                                                        disabled: isProcessing,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 \".concat(isProcessing ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700 shadow-lg hover:shadow-xl transform hover:scale-105\"),\n                                                        children: [\n                                                            isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-5 h-5 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 45\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isProcessing ? \"處理中...\" : \"開始處理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handlePreview,\n                                                        disabled: previewLoading,\n                                                        className: \"flex items-center space-x-2 px-4 py-3 rounded-xl transition-all duration-200 \".concat(previewLoading ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 hover:from-gray-200 hover:to-gray-300 shadow-sm hover:shadow-md transform hover:scale-105\"),\n                                                        children: [\n                                                            previewLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 45\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: previewLoading ? \"載入中...\" : \"預覽資料\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 33\n                                            }, this),\n                                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-32 bg-gray-200 rounded-full h-3 shadow-inner\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-3 rounded-full transition-all duration-300 shadow-sm\",\n                                                            style: {\n                                                                width: \"\".concat(progress, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            Math.round(progress),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\",\n                                                        children: \"處理日誌\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearLogs,\n                                                className: \"flex items-center space-x-1 px-3 py-1.5 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-600 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"清空\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3 p-3 rounded-lg bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-100 shadow-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full mt-2 flex-shrink-0 shadow-sm \".concat(log.type === \"success\" ? \"bg-gradient-to-r from-green-400 to-green-500\" : log.type === \"error\" ? \"bg-gradient-to-r from-red-400 to-red-500\" : log.type === \"warning\" ? \"bg-gradient-to-r from-yellow-400 to-orange-500\" : \"bg-gradient-to-r from-blue-400 to-indigo-500\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: log.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: log.timestamp\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 775,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 546,\n                    columnNumber: 17\n                }, this),\n                showPreview && previewData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 p-6 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"處理預覽\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPreview(false),\n                                        className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-5 h-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 overflow-y-auto\",\n                                style: {\n                                    maxHeight: \"calc(90vh - 140px)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-blue-900 mb-3\",\n                                                    children: \"處理範圍預覽\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700\",\n                                                                    children: \"日期範圍：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        previewData.date_range.start_date,\n                                                                        \" 至 \",\n                                                                        previewData.date_range.end_date\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700\",\n                                                                    children: \"員工數量：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 837,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        previewData.total_employees,\n                                                                        \" 人\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 838,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 836,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700\",\n                                                                    children: \"現有記錄：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        previewData.existing_records,\n                                                                        \" 條\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 840,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700\",\n                                                                    children: \"預計新增：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        previewData.estimated_new_records,\n                                                                        \" 條\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 829,\n                                            columnNumber: 37\n                                        }, this),\n                                        previewData.employees && previewData.employees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-3\",\n                                                    children: \"員工列表（前10名）\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"overflow-x-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                        className: \"w-full text-sm border border-gray-200 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                className: \"bg-gray-50\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-2 text-left border-b\",\n                                                                            children: \"員工編號\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                            lineNumber: 859,\n                                                                            columnNumber: 61\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-2 text-left border-b\",\n                                                                            children: \"員工姓名\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                            lineNumber: 860,\n                                                                            columnNumber: 61\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-2 text-left border-b\",\n                                                                            children: \"部門\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                            lineNumber: 861,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 57\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 857,\n                                                                columnNumber: 53\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                className: \"divide-y divide-gray-200\",\n                                                                children: previewData.employees.map((employee, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"hover:bg-gray-50\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-2\",\n                                                                                children: employee.employee_id\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 867,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-2\",\n                                                                                children: employee.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 868,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-2\",\n                                                                                children: employee.department_name || \"未分配\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 869,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 866,\n                                                                        columnNumber: 61\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 853,\n                                            columnNumber: 41\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-yellow-900 mb-2\",\n                                                    children: \"⚠️ 處理注意事項\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-yellow-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• 處理過程中請勿關閉瀏覽器\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• 已計算的資料將被覆蓋（如有勾選覆蓋選項）\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• 建議在非高峰時段進行大量資料處理\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 884,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• 處理完成後將自動生成處理報告\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 885,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 881,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 879,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 827,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 p-6 flex justify-end space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPreview(false),\n                                        className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowPreview(false);\n                                            startProcessing();\n                                        },\n                                        className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: \"確認處理\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 891,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                        lineNumber: 815,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 814,\n                    columnNumber: 21\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n            lineNumber: 440,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n        lineNumber: 438,\n        columnNumber: 9\n    }, this);\n}\n_s(AttendanceProcessingPage, \"e+4HUAret00qSiHjWTMzRCFCDvg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AttendanceProcessingPage;\nvar _c;\n$RefreshReg$(_c, \"AttendanceProcessingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/attendance-processing/page.tsx\n"));

/***/ })

});