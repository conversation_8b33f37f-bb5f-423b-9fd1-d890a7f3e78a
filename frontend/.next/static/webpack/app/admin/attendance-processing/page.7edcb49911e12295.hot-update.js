"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/attendance-processing/page",{

/***/ "(app-pages-browser)/./src/app/admin/attendance-processing/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/admin/attendance-processing/page.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AttendanceProcessingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AttendanceProcessingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 狀態管理\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start_date: \"\",\n        end_date: \"\"\n    });\n    const [employeeScope, setEmployeeScope] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"all\"\n    });\n    const [processingOptions, setProcessingOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        calculate_late_early: true,\n        calculate_overtime: true,\n        integrate_leaves: true,\n        overwrite_existing: false\n    });\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        pendingDays: 0,\n        pendingEmployees: 0,\n        estimatedTime: \"0秒\",\n        errorRecords: 0\n    });\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            type: \"info\",\n            message: \"系統就緒，等待處理指令\",\n            timestamp: new Date().toLocaleTimeString()\n        }\n    ]);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewData, setPreviewData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewLoading, setPreviewLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoMode, setAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化日期\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const formatDate = (date)=>date.toISOString().split(\"T\")[0];\n        setDateRange({\n            start_date: formatDate(yesterday),\n            end_date: formatDate(yesterday)\n        });\n    }, []);\n    // 更新統計資料\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        updateStatistics();\n    }, [\n        dateRange,\n        employeeScope\n    ]);\n    const updateStatistics = ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) return;\n        // 計算天數\n        const start = new Date(dateRange.start_date);\n        const end = new Date(dateRange.end_date);\n        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n        // 模擬計算員工數量\n        let employees = 156;\n        switch(employeeScope.type){\n            case \"department\":\n                employees = 45;\n                break;\n            case \"specific\":\n                employees = 12;\n                break;\n        }\n        // 估算時間\n        const estimatedSeconds = Math.ceil(days * employees * 0.5);\n        let timeText = \"\";\n        if (estimatedSeconds < 60) {\n            timeText = \"\".concat(estimatedSeconds, \"秒\");\n        } else if (estimatedSeconds < 3600) {\n            timeText = \"\".concat(Math.ceil(estimatedSeconds / 60), \"分鐘\");\n        } else {\n            timeText = \"\".concat(Math.ceil(estimatedSeconds / 3600), \"小時\");\n        }\n        setStatistics({\n            pendingDays: days,\n            pendingEmployees: employees,\n            estimatedTime: timeText,\n            errorRecords: Math.floor(Math.random() * 10)\n        });\n    };\n    const handleQuickDateSelect = (type)=>{\n        const today = new Date();\n        let startDate, endDate;\n        switch(type){\n            case \"1\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                break;\n            case \"7\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                startDate.setDate(startDate.getDate() - 6);\n                break;\n            case \"30\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                startDate.setDate(startDate.getDate() - 29);\n                break;\n            case \"current-month\":\n                startDate = new Date(today.getFullYear(), today.getMonth(), 1);\n                endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);\n                break;\n            default:\n                return;\n        }\n        setDateRange({\n            start_date: startDate.toISOString().split(\"T\")[0],\n            end_date: endDate.toISOString().split(\"T\")[0]\n        });\n    };\n    const addLog = (type, message)=>{\n        const newLog = {\n            type,\n            message,\n            timestamp: new Date().toLocaleTimeString()\n        };\n        setLogs((prev)=>[\n                newLog,\n                ...prev.slice(0, 19)\n            ]) // 保持最多20條記錄\n        ;\n    };\n    const clearLogs = ()=>{\n        setLogs([\n            {\n                type: \"info\",\n                message: \"系統就緒，等待處理指令\",\n                timestamp: new Date().toLocaleTimeString()\n            }\n        ]);\n    };\n    const startProcessing = async ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) {\n            alert(\"請選擇日期範圍\");\n            return;\n        }\n        setIsProcessing(true);\n        setProgress(0);\n        addLog(\"info\", \"開始處理考勤資料...\");\n        try {\n            // 準備API請求資料\n            const processingData = {\n                date_range: dateRange,\n                employee_scope: employeeScope,\n                processing_options: processingOptions\n            };\n            addLog(\"info\", \"發送處理請求到後端...\");\n            // 嘗試調用後端API，如果失敗則使用模擬數據\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/execute\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(processingData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"API請求失敗: \".concat(response.status));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"處理啟動失敗\");\n                }\n                const processingId = result.processing_id;\n                addLog(\"success\", \"處理已啟動，處理ID: \".concat(processingId));\n                addLog(\"info\", \"預計處理 \".concat(result.total_records, \" 條記錄\"));\n                // 開始監控處理進度\n                monitorProcessingProgress(processingId);\n            } catch (apiError) {\n                // API調用失敗，使用模擬處理\n                addLog(\"warning\", \"後端API不可用，使用模擬處理模式\");\n                simulateProcessing();\n            }\n        } catch (error) {\n            addLog(\"error\", \"處理失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const monitorProcessingProgress = async (processingId)=>{\n        const checkProgress = async ()=>{\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/status/\".concat(processingId));\n                if (!response.ok) {\n                    throw new Error(\"狀態查詢失敗: \".concat(response.status));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"狀態查詢失敗\");\n                }\n                const statusData = result.data;\n                // 更新進度\n                setProgress(statusData.progress);\n                addLog(\"info\", \"\".concat(statusData.current_step, \" (\").concat(statusData.progress, \"%)\"));\n                // 檢查是否完成\n                if (statusData.status === \"completed\") {\n                    addLog(\"success\", \"處理完成！\");\n                    addLog(\"info\", \"創建: \".concat(statusData.results.created, \", 更新: \").concat(statusData.results.updated, \", 失敗: \").concat(statusData.results.failed));\n                    // 顯示錯誤（如果有）\n                    if (statusData.errors.length > 0) {\n                        statusData.errors.forEach((error)=>{\n                            addLog(\"warning\", error);\n                        });\n                    }\n                    setTimeout(()=>{\n                        setIsProcessing(false);\n                        setProgress(0);\n                    }, 3000);\n                    return;\n                } else if (statusData.status === \"failed\") {\n                    addLog(\"error\", \"處理失敗\");\n                    statusData.errors.forEach((error)=>{\n                        addLog(\"error\", error);\n                    });\n                    setIsProcessing(false);\n                    setProgress(0);\n                    return;\n                }\n                // 繼續監控\n                setTimeout(checkProgress, 1000);\n            } catch (error) {\n                addLog(\"error\", \"監控進度失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n                setIsProcessing(false);\n                setProgress(0);\n            }\n        };\n        // 開始監控\n        setTimeout(checkProgress, 1000);\n    };\n    const simulateProcessing = ()=>{\n        addLog(\"info\", \"開始模擬處理流程...\");\n        const steps = [\n            \"資料載入\",\n            \"時間計算\",\n            \"資料整合\",\n            \"儲存結果\"\n        ];\n        let currentStep = 0;\n        const interval = setInterval(()=>{\n            setProgress((prev)=>{\n                const newProgress = prev + Math.random() * 15 + 5;\n                if (newProgress >= 100) {\n                    clearInterval(interval);\n                    setProgress(100);\n                    addLog(\"success\", \"模擬處理完成！\");\n                    addLog(\"info\", \"成功處理 \".concat(statistics.pendingEmployees, \" 名員工的考勤資料\"));\n                    addLog(\"info\", \"創建: 15, 更新: 8, 失敗: 0\");\n                    setTimeout(()=>{\n                        setIsProcessing(false);\n                        setProgress(0);\n                    }, 3000);\n                    return 100;\n                }\n                // 更新步驟狀態\n                const stepIndex = Math.floor(newProgress / 25);\n                if (stepIndex > currentStep && stepIndex < steps.length) {\n                    addLog(\"info\", \"正在執行：\".concat(steps[stepIndex], \" (\").concat(Math.round(newProgress), \"%)\"));\n                    currentStep = stepIndex;\n                }\n                return newProgress;\n            });\n        }, 300);\n    };\n    const handlePreview = async ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) {\n            alert(\"請選擇日期範圍\");\n            return;\n        }\n        setPreviewLoading(true);\n        try {\n            const previewRequest = {\n                date_range: dateRange,\n                employee_scope: employeeScope\n            };\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/preview\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(previewRequest)\n                });\n                if (!response.ok) {\n                    throw new Error(\"預覽請求失敗: \".concat(response.status));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"預覽失敗\");\n                }\n                setPreviewData(result.data);\n                setShowPreview(true);\n            } catch (apiError) {\n                // API調用失敗，使用模擬數據\n                addLog(\"warning\", \"後端API不可用，使用模擬預覽數據\");\n                const mockPreviewData = {\n                    employees: [\n                        {\n                            id: 1,\n                            employee_id: \"E001\",\n                            name: \"黎麗玲\",\n                            department_name: \"技術部\"\n                        },\n                        {\n                            id: 2,\n                            employee_id: \"E002\",\n                            name: \"蔡秀娟\",\n                            department_name: \"技術部\"\n                        },\n                        {\n                            id: 3,\n                            employee_id: \"E003\",\n                            name: \"劉志偉\",\n                            department_name: \"業務部\"\n                        },\n                        {\n                            id: 4,\n                            employee_id: \"admin\",\n                            name: \"系統管理員\",\n                            department_name: \"管理部\"\n                        }\n                    ],\n                    total_employees: 4,\n                    date_range: dateRange,\n                    existing_records: 2,\n                    estimated_new_records: statistics.pendingEmployees - 2\n                };\n                setPreviewData(mockPreviewData);\n                setShowPreview(true);\n            }\n        } catch (error) {\n            addLog(\"error\", \"預覽失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n        } finally{\n            setPreviewLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold mb-2 text-white\",\n                                            children: \"考勤資料整理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/admin\",\n                                                    className: \"inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white group-hover:text-indigo-100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-white group-hover:text-indigo-100\",\n                                                            children: \"返回\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-indigo-100 text-base font-medium\",\n                                                    children: \"自動計算遲到、早退、加班時間，整合請假資料並更新考勤狀態\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-white\",\n                                                    children: \"管理員模式\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"考勤整理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex items-center justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-white\",\n                                        children: \"自動處理模式：\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoMode\",\n                                                checked: autoMode,\n                                                onChange: (e)=>setAutoMode(e.target.checked),\n                                                className: \"sr-only\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoMode\",\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-11 h-6 rounded-full transition-colors duration-200 \".concat(autoMode ? \"bg-green-500\" : \"bg-white/30\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-200 \".concat(autoMode ? \"translate-x-5\" : \"translate-x-0.5\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm text-white font-medium\",\n                                                children: autoMode ? \"啟用\" : \"停用\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"待處理天數\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                children: statistics.pendingDays\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"待處理員工\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent\",\n                                                children: statistics.pendingEmployees\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"預估時間\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent\",\n                                                children: statistics.estimatedTime\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"異常記錄\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent\",\n                                                children: statistics.errorRecords\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 495,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                    children: \"日期範圍設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"開始日期\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            value: dateRange.start_date,\n                                                            onChange: (e)=>setDateRange((prev)=>({\n                                                                        ...prev,\n                                                                        start_date: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"結束日期\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            value: dateRange.end_date,\n                                                            onChange: (e)=>setDateRange((prev)=>({\n                                                                        ...prev,\n                                                                        end_date: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"1\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"昨天\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"7\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"最近7天\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"30\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"最近30天\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleQuickDateSelect(\"current-month\"),\n                                                    className: \"px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105\",\n                                                    children: \"本月\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent\",\n                                                    children: \"員工範圍設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"employeeScope\",\n                                                            value: \"all\",\n                                                            checked: employeeScope.type === \"all\",\n                                                            onChange: (e)=>setEmployeeScope({\n                                                                    type: \"all\"\n                                                                }),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"所有員工\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"employeeScope\",\n                                                            value: \"department\",\n                                                            checked: employeeScope.type === \"department\",\n                                                            onChange: (e)=>setEmployeeScope({\n                                                                    type: \"department\",\n                                                                    department_ids: []\n                                                                }),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"指定部門\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"employeeScope\",\n                                                            value: \"specific\",\n                                                            checked: employeeScope.type === \"specific\",\n                                                            onChange: (e)=>setEmployeeScope({\n                                                                    type: \"specific\",\n                                                                    employee_ids: []\n                                                                }),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"指定員工\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent\",\n                                                    children: \"處理選項設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"計算遲到時間\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.calculate_late_early,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        calculate_late_early: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"計算加班時間\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.calculate_overtime,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        calculate_overtime: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"整合請假資料\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.integrate_leaves,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        integrate_leaves: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: \"覆蓋已存在的計算結果\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: processingOptions.overwrite_existing,\n                                                            onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                        ...prev,\n                                                                        overwrite_existing: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: startProcessing,\n                                                        disabled: isProcessing,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 \".concat(isProcessing ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700 shadow-lg hover:shadow-xl transform hover:scale-105\"),\n                                                        children: [\n                                                            isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-5 h-5 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 45\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isProcessing ? \"處理中...\" : \"開始處理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handlePreview,\n                                                        disabled: previewLoading,\n                                                        className: \"flex items-center space-x-2 px-4 py-3 rounded-xl transition-all duration-200 \".concat(previewLoading ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 hover:from-gray-200 hover:to-gray-300 shadow-sm hover:shadow-md transform hover:scale-105\"),\n                                                        children: [\n                                                            previewLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 45\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: previewLoading ? \"載入中...\" : \"預覽資料\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 33\n                                            }, this),\n                                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-32 bg-gray-200 rounded-full h-3 shadow-inner\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-3 rounded-full transition-all duration-300 shadow-sm\",\n                                                            style: {\n                                                                width: \"\".concat(progress, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            Math.round(progress),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\",\n                                                        children: \"處理日誌\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearLogs,\n                                                className: \"flex items-center space-x-1 px-3 py-1.5 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-600 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"清空\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3 p-3 rounded-lg bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-100 shadow-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full mt-2 flex-shrink-0 shadow-sm \".concat(log.type === \"success\" ? \"bg-gradient-to-r from-green-400 to-green-500\" : log.type === \"error\" ? \"bg-gradient-to-r from-red-400 to-red-500\" : log.type === \"warning\" ? \"bg-gradient-to-r from-yellow-400 to-orange-500\" : \"bg-gradient-to-r from-blue-400 to-indigo-500\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: log.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: log.timestamp\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 775,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 546,\n                    columnNumber: 17\n                }, this),\n                showPreview && previewData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center p-4 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden border border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-6 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"處理預覽\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-indigo-100 text-sm mt-1\",\n                                                    children: \"檢視即將處理的考勤資料範圍和統計資訊\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowPreview(false),\n                                            className: \"p-2 hover:bg-white/20 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 41\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 overflow-y-auto\",\n                                style: {\n                                    maxHeight: \"calc(90vh - 140px)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-blue-900 mb-4 flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 836,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"處理範圍預覽\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 835,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-blue-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 font-medium\",\n                                                                    children: \"日期範圍：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold text-blue-900 block mt-1\",\n                                                                    children: [\n                                                                        previewData.date_range.start_date,\n                                                                        \" 至 \",\n                                                                        previewData.date_range.end_date\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 840,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-blue-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 font-medium\",\n                                                                    children: \"員工數量：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold text-blue-900 block mt-1\",\n                                                                    children: [\n                                                                        previewData.total_employees,\n                                                                        \" 人\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-blue-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 font-medium\",\n                                                                    children: \"現有記錄：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 849,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold text-blue-900 block mt-1\",\n                                                                    children: [\n                                                                        previewData.existing_records,\n                                                                        \" 條\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 848,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-blue-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 font-medium\",\n                                                                    children: \"預計新增：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold text-blue-900 block mt-1\",\n                                                                    children: [\n                                                                        previewData.estimated_new_records,\n                                                                        \" 條\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 854,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 852,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 839,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 37\n                                        }, this),\n                                        previewData.employees && previewData.employees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-gray-900 mb-4 flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5 text-indigo-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"員工列表（前10名）\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"overflow-x-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                        className: \"w-full text-sm border border-gray-200 rounded-xl overflow-hidden shadow-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                className: \"bg-gradient-to-r from-indigo-500 to-purple-500 text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left font-bold\",\n                                                                            children: \"員工編號\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                            lineNumber: 870,\n                                                                            columnNumber: 61\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left font-bold\",\n                                                                            children: \"員工姓名\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                            lineNumber: 871,\n                                                                            columnNumber: 61\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left font-bold\",\n                                                                            children: \"部門\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                            lineNumber: 872,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 57\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 53\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                className: \"divide-y divide-gray-200\",\n                                                                children: previewData.employees.map((employee, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"hover:bg-gradient-to-r hover:from-slate-50 hover:to-blue-50 transition-all duration-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 font-medium text-gray-900\",\n                                                                                children: employee.employee_id\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 878,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 font-medium text-gray-900\",\n                                                                                children: employee.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 879,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-gray-700\",\n                                                                                children: employee.department_name || \"未分配\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 880,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 877,\n                                                                        columnNumber: 61\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 41\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-6 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-yellow-900 mb-3 flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 892,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"處理注意事項\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-yellow-800 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-1.5 h-1.5 bg-yellow-600 rounded-full mt-2 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"處理過程中請勿關閉瀏覽器\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-1.5 h-1.5 bg-yellow-600 rounded-full mt-2 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"已計算的資料將被覆蓋（如有勾選覆蓋選項）\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 900,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-1.5 h-1.5 bg-yellow-600 rounded-full mt-2 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"建議在非高峰時段進行大量資料處理\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-1.5 h-1.5 bg-yellow-600 rounded-full mt-2 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 909,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"處理完成後將自動生成處理報告\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 910,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 908,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 832,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 831,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 p-6 flex justify-end space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPreview(false),\n                                        className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowPreview(false);\n                                            startProcessing();\n                                        },\n                                        className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: \"確認處理\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 924,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                        lineNumber: 815,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 814,\n                    columnNumber: 21\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n            lineNumber: 440,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n        lineNumber: 438,\n        columnNumber: 9\n    }, this);\n}\n_s(AttendanceProcessingPage, \"e+4HUAret00qSiHjWTMzRCFCDvg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AttendanceProcessingPage;\nvar _c;\n$RefreshReg$(_c, \"AttendanceProcessingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/attendance-processing/page.tsx\n"));

/***/ })

});