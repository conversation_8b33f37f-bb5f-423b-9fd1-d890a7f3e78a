"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/attendance-processing/page",{

/***/ "(app-pages-browser)/./src/app/admin/attendance-processing/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/admin/attendance-processing/page.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AttendanceProcessingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calculator,Calendar,Clock,Database,Eye,Loader2,Merge,Play,Save,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AttendanceProcessingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 狀態管理\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        start_date: \"\",\n        end_date: \"\"\n    });\n    const [employeeScope, setEmployeeScope] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"all\"\n    });\n    const [processingOptions, setProcessingOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        calculate_late_early: true,\n        calculate_overtime: true,\n        integrate_leaves: true,\n        overwrite_existing: false\n    });\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        pendingDays: 0,\n        pendingEmployees: 0,\n        estimatedTime: \"0秒\",\n        errorRecords: 0\n    });\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            type: \"info\",\n            message: \"系統就緒，等待處理指令\",\n            timestamp: new Date().toLocaleTimeString()\n        }\n    ]);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewData, setPreviewData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewLoading, setPreviewLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoMode, setAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化日期\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const formatDate = (date)=>date.toISOString().split(\"T\")[0];\n        setDateRange({\n            start_date: formatDate(yesterday),\n            end_date: formatDate(yesterday)\n        });\n    }, []);\n    // 更新統計資料\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        updateStatistics();\n    }, [\n        dateRange,\n        employeeScope\n    ]);\n    const updateStatistics = ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) return;\n        // 計算天數\n        const start = new Date(dateRange.start_date);\n        const end = new Date(dateRange.end_date);\n        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;\n        // 模擬計算員工數量\n        let employees = 156;\n        switch(employeeScope.type){\n            case \"department\":\n                employees = 45;\n                break;\n            case \"specific\":\n                employees = 12;\n                break;\n        }\n        // 估算時間\n        const estimatedSeconds = Math.ceil(days * employees * 0.5);\n        let timeText = \"\";\n        if (estimatedSeconds < 60) {\n            timeText = \"\".concat(estimatedSeconds, \"秒\");\n        } else if (estimatedSeconds < 3600) {\n            timeText = \"\".concat(Math.ceil(estimatedSeconds / 60), \"分鐘\");\n        } else {\n            timeText = \"\".concat(Math.ceil(estimatedSeconds / 3600), \"小時\");\n        }\n        setStatistics({\n            pendingDays: days,\n            pendingEmployees: employees,\n            estimatedTime: timeText,\n            errorRecords: Math.floor(Math.random() * 10)\n        });\n    };\n    const handleQuickDateSelect = (type)=>{\n        const today = new Date();\n        let startDate, endDate;\n        switch(type){\n            case \"1\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                break;\n            case \"7\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                startDate.setDate(startDate.getDate() - 6);\n                break;\n            case \"30\":\n                endDate = new Date(today);\n                endDate.setDate(endDate.getDate() - 1);\n                startDate = new Date(endDate);\n                startDate.setDate(startDate.getDate() - 29);\n                break;\n            case \"current-month\":\n                startDate = new Date(today.getFullYear(), today.getMonth(), 1);\n                endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);\n                break;\n            default:\n                return;\n        }\n        setDateRange({\n            start_date: startDate.toISOString().split(\"T\")[0],\n            end_date: endDate.toISOString().split(\"T\")[0]\n        });\n    };\n    const addLog = (type, message)=>{\n        const newLog = {\n            type,\n            message,\n            timestamp: new Date().toLocaleTimeString()\n        };\n        setLogs((prev)=>[\n                newLog,\n                ...prev.slice(0, 19)\n            ]) // 保持最多20條記錄\n        ;\n    };\n    const clearLogs = ()=>{\n        setLogs([\n            {\n                type: \"info\",\n                message: \"系統就緒，等待處理指令\",\n                timestamp: new Date().toLocaleTimeString()\n            }\n        ]);\n    };\n    const startProcessing = async ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) {\n            alert(\"請選擇日期範圍\");\n            return;\n        }\n        setIsProcessing(true);\n        setProgress(0);\n        addLog(\"info\", \"開始處理考勤資料...\");\n        try {\n            // 準備API請求資料\n            const processingData = {\n                date_range: dateRange,\n                employee_scope: employeeScope,\n                processing_options: processingOptions\n            };\n            addLog(\"info\", \"發送處理請求到後端...\");\n            // 嘗試調用後端API，如果失敗則使用模擬數據\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/execute\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(processingData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"API請求失敗: \".concat(response.status));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"處理啟動失敗\");\n                }\n                const processingId = result.processing_id;\n                addLog(\"success\", \"處理已啟動，處理ID: \".concat(processingId));\n                addLog(\"info\", \"預計處理 \".concat(result.total_records, \" 條記錄\"));\n                // 開始監控處理進度\n                monitorProcessingProgress(processingId);\n            } catch (apiError) {\n                // API調用失敗，使用模擬處理\n                addLog(\"warning\", \"後端API不可用，使用模擬處理模式\");\n                simulateProcessing();\n            }\n        } catch (error) {\n            addLog(\"error\", \"處理失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const monitorProcessingProgress = async (processingId)=>{\n        const checkProgress = async ()=>{\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/status/\".concat(processingId));\n                if (!response.ok) {\n                    throw new Error(\"狀態查詢失敗: \".concat(response.status));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"狀態查詢失敗\");\n                }\n                const statusData = result.data;\n                // 更新進度\n                setProgress(statusData.progress);\n                addLog(\"info\", \"\".concat(statusData.current_step, \" (\").concat(statusData.progress, \"%)\"));\n                // 檢查是否完成\n                if (statusData.status === \"completed\") {\n                    addLog(\"success\", \"處理完成！\");\n                    addLog(\"info\", \"創建: \".concat(statusData.results.created, \", 更新: \").concat(statusData.results.updated, \", 失敗: \").concat(statusData.results.failed));\n                    // 顯示錯誤（如果有）\n                    if (statusData.errors.length > 0) {\n                        statusData.errors.forEach((error)=>{\n                            addLog(\"warning\", error);\n                        });\n                    }\n                    setTimeout(()=>{\n                        setIsProcessing(false);\n                        setProgress(0);\n                    }, 3000);\n                    return;\n                } else if (statusData.status === \"failed\") {\n                    addLog(\"error\", \"處理失敗\");\n                    statusData.errors.forEach((error)=>{\n                        addLog(\"error\", error);\n                    });\n                    setIsProcessing(false);\n                    setProgress(0);\n                    return;\n                }\n                // 繼續監控\n                setTimeout(checkProgress, 1000);\n            } catch (error) {\n                addLog(\"error\", \"監控進度失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n                setIsProcessing(false);\n                setProgress(0);\n            }\n        };\n        // 開始監控\n        setTimeout(checkProgress, 1000);\n    };\n    const simulateProcessing = ()=>{\n        addLog(\"info\", \"開始模擬處理流程...\");\n        const steps = [\n            \"資料載入\",\n            \"時間計算\",\n            \"資料整合\",\n            \"儲存結果\"\n        ];\n        let currentStep = 0;\n        const interval = setInterval(()=>{\n            setProgress((prev)=>{\n                const newProgress = prev + Math.random() * 15 + 5;\n                if (newProgress >= 100) {\n                    clearInterval(interval);\n                    setProgress(100);\n                    addLog(\"success\", \"模擬處理完成！\");\n                    addLog(\"info\", \"成功處理 \".concat(statistics.pendingEmployees, \" 名員工的考勤資料\"));\n                    addLog(\"info\", \"創建: 15, 更新: 8, 失敗: 0\");\n                    setTimeout(()=>{\n                        setIsProcessing(false);\n                        setProgress(0);\n                    }, 3000);\n                    return 100;\n                }\n                // 更新步驟狀態\n                const stepIndex = Math.floor(newProgress / 25);\n                if (stepIndex > currentStep && stepIndex < steps.length) {\n                    addLog(\"info\", \"正在執行：\".concat(steps[stepIndex], \" (\").concat(Math.round(newProgress), \"%)\"));\n                    currentStep = stepIndex;\n                }\n                return newProgress;\n            });\n        }, 300);\n    };\n    const handlePreview = async ()=>{\n        if (!dateRange.start_date || !dateRange.end_date) {\n            alert(\"請選擇日期範圍\");\n            return;\n        }\n        setPreviewLoading(true);\n        try {\n            const previewRequest = {\n                date_range: dateRange,\n                employee_scope: employeeScope\n            };\n            try {\n                const response = await fetch(\"http://localhost:7073/api/attendance/processing/preview\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(previewRequest)\n                });\n                if (!response.ok) {\n                    throw new Error(\"預覽請求失敗: \".concat(response.status));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"預覽失敗\");\n                }\n                setPreviewData(result.data);\n                setShowPreview(true);\n            } catch (apiError) {\n                // API調用失敗，使用模擬數據\n                addLog(\"warning\", \"後端API不可用，使用模擬預覽數據\");\n                const mockPreviewData = {\n                    employees: [\n                        {\n                            id: 1,\n                            employee_id: \"E001\",\n                            name: \"黎麗玲\",\n                            department_name: \"技術部\"\n                        },\n                        {\n                            id: 2,\n                            employee_id: \"E002\",\n                            name: \"蔡秀娟\",\n                            department_name: \"技術部\"\n                        },\n                        {\n                            id: 3,\n                            employee_id: \"E003\",\n                            name: \"劉志偉\",\n                            department_name: \"業務部\"\n                        },\n                        {\n                            id: 4,\n                            employee_id: \"admin\",\n                            name: \"系統管理員\",\n                            department_name: \"管理部\"\n                        }\n                    ],\n                    total_employees: 4,\n                    date_range: dateRange,\n                    existing_records: 2,\n                    estimated_new_records: statistics.pendingEmployees - 2\n                };\n                setPreviewData(mockPreviewData);\n                setShowPreview(true);\n            }\n        } catch (error) {\n            addLog(\"error\", \"預覽失敗: \".concat(error instanceof Error ? error.message : \"未知錯誤\"));\n        } finally{\n            setPreviewLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/admin\",\n                                    className: \"flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"返回儀表板\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-px bg-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"考勤資料整理\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"自動處理模式：\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"autoMode\",\n                                            checked: autoMode,\n                                            onChange: (e)=>setAutoMode(e.target.checked),\n                                            className: \"sr-only\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"autoMode\",\n                                            className: \"relative inline-flex items-center cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-11 h-6 rounded-full transition-colors duration-200 \".concat(autoMode ? \"bg-blue-500\" : \"bg-gray-200\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-200 \".concat(autoMode ? \"translate-x-5\" : \"translate-x-0.5\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-green-600 font-medium\",\n                                            children: autoMode ? \"啟用\" : \"停用\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                lineNumber: 440,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"待處理天數\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: statistics.pendingDays\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-yellow-50 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"待處理員工\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: statistics.pendingEmployees\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"預估時間\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: statistics.estimatedTime\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-red-50 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"異常記錄\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: statistics.errorRecords\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-4 h-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"日期範圍設定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"開始日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: dateRange.start_date,\n                                                                onChange: (e)=>setDateRange((prev)=>({\n                                                                            ...prev,\n                                                                            start_date: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"結束日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: dateRange.end_date,\n                                                                onChange: (e)=>setDateRange((prev)=>({\n                                                                            ...prev,\n                                                                            end_date: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuickDateSelect(\"1\"),\n                                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                        children: \"昨天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuickDateSelect(\"7\"),\n                                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                        children: \"最近7天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuickDateSelect(\"30\"),\n                                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                        children: \"最近30天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuickDateSelect(\"current-month\"),\n                                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                        children: \"本月\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-yellow-50 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-4 h-4 text-yellow-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"員工範圍設定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"employeeScope\",\n                                                                value: \"all\",\n                                                                checked: employeeScope.type === \"all\",\n                                                                onChange: (e)=>setEmployeeScope({\n                                                                        type: \"all\"\n                                                                    }),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700\",\n                                                                children: \"所有員工\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 609,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"employeeScope\",\n                                                                value: \"department\",\n                                                                checked: employeeScope.type === \"department\",\n                                                                onChange: (e)=>setEmployeeScope({\n                                                                        type: \"department\",\n                                                                        department_ids: []\n                                                                    }),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700\",\n                                                                children: \"指定部門\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"employeeScope\",\n                                                                value: \"specific\",\n                                                                checked: employeeScope.type === \"specific\",\n                                                                onChange: (e)=>setEmployeeScope({\n                                                                        type: \"specific\",\n                                                                        employee_ids: []\n                                                                    }),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700\",\n                                                                children: \"指定員工\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"處理選項設定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 650,\n                                                                        columnNumber: 41\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-700\",\n                                                                        children: \"計算遲到時間\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 651,\n                                                                        columnNumber: 41\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: processingOptions.calculate_late_early,\n                                                                onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                            ...prev,\n                                                                            calculate_late_early: e.target.checked\n                                                                        })),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 41\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-700\",\n                                                                        children: \"計算加班時間\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 664,\n                                                                        columnNumber: 41\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: processingOptions.calculate_overtime,\n                                                                onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                            ...prev,\n                                                                            calculate_overtime: e.target.checked\n                                                                        })),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 41\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-700\",\n                                                                        children: \"整合請假資料\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 677,\n                                                                        columnNumber: 41\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: processingOptions.integrate_leaves,\n                                                                onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                            ...prev,\n                                                                            integrate_leaves: e.target.checked\n                                                                        })),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 689,\n                                                                        columnNumber: 41\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-700\",\n                                                                        children: \"覆蓋已存在的計算結果\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 41\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 37\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: processingOptions.overwrite_existing,\n                                                                onChange: (e)=>setProcessingOptions((prev)=>({\n                                                                            ...prev,\n                                                                            overwrite_existing: e.target.checked\n                                                                        })),\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: startProcessing,\n                                                            disabled: isProcessing,\n                                                            className: \"flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 \".concat(isProcessing ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl\"),\n                                                            children: [\n                                                                isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-5 h-5 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 716,\n                                                                    columnNumber: 45\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: isProcessing ? \"處理中...\" : \"開始處理\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 720,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handlePreview,\n                                                            disabled: previewLoading,\n                                                            className: \"flex items-center space-x-2 px-4 py-3 rounded-xl transition-colors \".concat(previewLoading ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                                                            children: [\n                                                                previewLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-4 h-4 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 733,\n                                                                    columnNumber: 45\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: previewLoading ? \"載入中...\" : \"預覽資料\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 723,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 33\n                                                }, this),\n                                                isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-32 bg-gray-200 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: \"\".concat(progress, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                Math.round(progress),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 749,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 742,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-purple-50 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-4 h-4 text-purple-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                            children: \"處理日誌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: clearLogs,\n                                                    className: \"flex items-center space-x-1 px-3 py-1 text-sm text-gray-500 hover:text-gray-700 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"清空\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                            children: logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3 p-3 rounded-lg bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full mt-2 flex-shrink-0 \".concat(log.type === \"success\" ? \"bg-green-500\" : log.type === \"error\" ? \"bg-red-500\" : log.type === \"warning\" ? \"bg-yellow-500\" : \"bg-blue-500\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-900\",\n                                                                    children: log.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 785,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                                    children: log.timestamp\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 786,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 37\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 758,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                lineNumber: 757,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 17\n                    }, this),\n                    showPreview && previewData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-200 p-6 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"處理預覽\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowPreview(false),\n                                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calculator_Calendar_Clock_Database_Eye_Loader2_Merge_Play_Save_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 799,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 overflow-y-auto\",\n                                    style: {\n                                        maxHeight: \"calc(90vh - 140px)\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-blue-900 mb-3\",\n                                                        children: \"處理範圍預覽\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-700\",\n                                                                        children: \"日期範圍：\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            previewData.date_range.start_date,\n                                                                            \" 至 \",\n                                                                            previewData.date_range.end_date\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 817,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 815,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-700\",\n                                                                        children: \"員工數量：\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 820,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            previewData.total_employees,\n                                                                            \" 人\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 821,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 819,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-700\",\n                                                                        children: \"現有記錄：\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 824,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            previewData.existing_records,\n                                                                            \" 條\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 825,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-700\",\n                                                                        children: \"預計新增：\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 828,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            previewData.estimated_new_records,\n                                                                            \" 條\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 829,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 814,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 37\n                                            }, this),\n                                            previewData.employees && previewData.employees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-3\",\n                                                        children: \"員工列表（前10名）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"w-full text-sm border border-gray-200 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    className: \"bg-gray-50\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left border-b\",\n                                                                                children: \"員工編號\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 842,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left border-b\",\n                                                                                children: \"員工姓名\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 843,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left border-b\",\n                                                                                children: \"部門\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                lineNumber: 844,\n                                                                                columnNumber: 61\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                        lineNumber: 841,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 53\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-200\",\n                                                                    children: previewData.employees.map((employee, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            className: \"hover:bg-gray-50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-2\",\n                                                                                    children: employee.employee_id\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                    lineNumber: 850,\n                                                                                    columnNumber: 65\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-2\",\n                                                                                    children: employee.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                    lineNumber: 851,\n                                                                                    columnNumber: 65\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-2\",\n                                                                                    children: employee.department_name || \"未分配\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                                    lineNumber: 852,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                            lineNumber: 849,\n                                                                            columnNumber: 61\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 838,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-yellow-900 mb-2\",\n                                                        children: \"⚠️ 處理注意事項\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-sm text-yellow-800 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• 處理過程中請勿關閉瀏覽器\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• 已計算的資料將被覆蓋（如有勾選覆蓋選項）\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 866,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• 建議在非高峰時段進行大量資料處理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 867,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• 處理完成後將自動生成處理報告\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                                lineNumber: 862,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 809,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 p-6 flex justify-end space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowPreview(false),\n                                            className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowPreview(false);\n                                                startProcessing();\n                                            },\n                                            className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"確認處理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                                    lineNumber: 874,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                        lineNumber: 797,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n                lineNumber: 476,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/attendance-processing/page.tsx\",\n        lineNumber: 438,\n        columnNumber: 9\n    }, this);\n}\n_s(AttendanceProcessingPage, \"e+4HUAret00qSiHjWTMzRCFCDvg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AttendanceProcessingPage;\nvar _c;\n$RefreshReg$(_c, \"AttendanceProcessingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/attendance-processing/page.tsx\n"));

/***/ })

});