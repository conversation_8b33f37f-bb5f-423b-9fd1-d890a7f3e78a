/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstallPrompt.tsx */ \"(app-pages-browser)/./src/components/PWAInstallPrompt.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ServiceWorkerRegistration.tsx */ \"(app-pages-browser)/./src/components/ServiceWorkerRegistration.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js":
/*!****************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/Icon.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Icon; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\");\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_c = (param, ref)=>{\n    let { color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = \"\", children, iconNode, ...rest } = param;\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n        ref,\n        ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide\", className),\n        ...!children && !(0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasA11yProp)(rest) && {\n            \"aria-hidden\": \"true\"\n        },\n        ...rest\n    }, [\n        ...iconNode.map((param)=>{\n            let [tag, attrs] = param;\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs);\n        }),\n        ...Array.isArray(children) ? children : [\n            children\n        ]\n    ]);\n});\n_c1 = Icon;\n //# sourceMappingURL=Icon.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Icon$forwardRef\");\n$RefreshReg$(_c1, \"Icon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ createLucideIcon; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/* harmony import */ var _Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Icon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js\");\n/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((param, ref)=>{\n        let { className, ...props } = param;\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            ref,\n            iconNode,\n            className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide-\".concat((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toKebabCase)((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName))), \"lucide-\".concat(iconName), className),\n            ...props\n        });\n    });\n    Component.displayName = (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName);\n    return Component;\n};\n //# sourceMappingURL=createLucideIcon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ defaultAttributes; }\n/* harmony export */ });\n/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vZGVmYXVsdEF0dHJpYnV0ZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0lBQUEsSUFBZUEsb0JBQUE7SUFDYkMsT0FBTztJQUNQQyxPQUFPO0lBQ1BDLFFBQVE7SUFDUkMsU0FBUztJQUNUQyxNQUFNO0lBQ05DLFFBQVE7SUFDUkMsYUFBYTtJQUNiQyxlQUFlO0lBQ2ZDLGdCQUFnQjtBQUNsQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2RlZmF1bHRBdHRyaWJ1dGVzLnRzPzM3MGMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1xuICB4bWxuczogJ2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyxcbiAgd2lkdGg6IDI0LFxuICBoZWlnaHQ6IDI0LFxuICB2aWV3Qm94OiAnMCAwIDI0IDI0JyxcbiAgZmlsbDogJ25vbmUnLFxuICBzdHJva2U6ICdjdXJyZW50Q29sb3InLFxuICBzdHJva2VXaWR0aDogMixcbiAgc3Ryb2tlTGluZWNhcDogJ3JvdW5kJyxcbiAgc3Ryb2tlTGluZWpvaW46ICdyb3VuZCcsXG59O1xuIl0sIm5hbWVzIjpbImRlZmF1bHRBdHRyaWJ1dGVzIiwieG1sbnMiLCJ3aWR0aCIsImhlaWdodCIsInZpZXdCb3giLCJmaWxsIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/download.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Download; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 15V3\",\n            key: \"m9g1x1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\",\n            key: \"ih7n3h\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m7 10 5 5 5-5\",\n            key: \"brsn70\"\n        }\n    ]\n];\nconst Download = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"download\", __iconNode);\n //# sourceMappingURL=download.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/monitor.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Monitor; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"14\",\n            x: \"2\",\n            y: \"3\",\n            rx: \"2\",\n            key: \"48i651\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"8\",\n            x2: \"16\",\n            y1: \"21\",\n            y2: \"21\",\n            key: \"1svkeh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"17\",\n            y2: \"21\",\n            key: \"vw1qmm\"\n        }\n    ]\n];\nconst Monitor = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"monitor\", __iconNode);\n //# sourceMappingURL=monitor.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbW9uaXRvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNQSxhQUF1QjtJQUNsQztRQUFDO1FBQVE7WUFBRUMsT0FBTztZQUFNQyxRQUFRO1lBQU1DLEdBQUc7WUFBS0MsR0FBRztZQUFLQyxJQUFJO1lBQUtDLEtBQUs7UUFBQTtLQUFVO0lBQzlFO1FBQUM7UUFBUTtZQUFFQyxJQUFJO1lBQUtDLElBQUk7WUFBTUMsSUFBSTtZQUFNQyxJQUFJO1lBQU1KLEtBQUs7UUFBQTtLQUFVO0lBQ2pFO1FBQUM7UUFBUTtZQUFFQyxJQUFJO1lBQU1DLElBQUk7WUFBTUMsSUFBSTtZQUFNQyxJQUFJO1lBQU1KLEtBQUs7UUFBVTtLQUFBO0NBQ3BFO0FBYU0sTUFBQUssVUFBVUMsZ0VBQWdCQSxDQUFDLFdBQVdaIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvaWNvbnMvbW9uaXRvci50cz85MjlhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtcbiAgWydyZWN0JywgeyB3aWR0aDogJzIwJywgaGVpZ2h0OiAnMTQnLCB4OiAnMicsIHk6ICczJywgcng6ICcyJywga2V5OiAnNDhpNjUxJyB9XSxcbiAgWydsaW5lJywgeyB4MTogJzgnLCB4MjogJzE2JywgeTE6ICcyMScsIHkyOiAnMjEnLCBrZXk6ICcxc3ZrZWgnIH1dLFxuICBbJ2xpbmUnLCB7IHgxOiAnMTInLCB4MjogJzEyJywgeTE6ICcxNycsIHkyOiAnMjEnLCBrZXk6ICd2dzFxbW0nIH1dLFxuXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIE1vbml0b3JcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNtVmpkQ0IzYVdSMGFEMGlNakFpSUdobGFXZG9kRDBpTVRRaUlIZzlJaklpSUhrOUlqTWlJSEo0UFNJeUlpQXZQZ29nSUR4c2FXNWxJSGd4UFNJNElpQjRNajBpTVRZaUlIa3hQU0l5TVNJZ2VUSTlJakl4SWlBdlBnb2dJRHhzYVc1bElIZ3hQU0l4TWlJZ2VESTlJakV5SWlCNU1UMGlNVGNpSUhreVBTSXlNU0lnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvbW9uaXRvclxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IE1vbml0b3IgPSBjcmVhdGVMdWNpZGVJY29uKCdtb25pdG9yJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IE1vbml0b3I7XG4iXSwibmFtZXMiOlsiX19pY29uTm9kZSIsIndpZHRoIiwiaGVpZ2h0IiwieCIsInkiLCJyeCIsImtleSIsIngxIiwieDIiLCJ5MSIsInkyIiwiTW9uaXRvciIsImNyZWF0ZUx1Y2lkZUljb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/smartphone.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Smartphone; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"20\",\n            x: \"5\",\n            y: \"2\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"1yt0o3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 18h.01\",\n            key: \"mhygvu\"\n        }\n    ]\n];\nconst Smartphone = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"smartphone\", __iconNode);\n //# sourceMappingURL=smartphone.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js":
/*!*******************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ X; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M18 6 6 18\",\n            key: \"1bl5f8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6 6 12 12\",\n            key: \"d8bk6v\"\n        }\n    ]\n];\nconst X = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"x\", __iconNode);\n //# sourceMappingURL=x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/shared/src/utils.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasA11yProp: function() { return /* binding */ hasA11yProp; },\n/* harmony export */   mergeClasses: function() { return /* binding */ mergeClasses; },\n/* harmony export */   toCamelCase: function() { return /* binding */ toCamelCase; },\n/* harmony export */   toKebabCase: function() { return /* binding */ toKebabCase; },\n/* harmony export */   toPascalCase: function() { return /* binding */ toPascalCase; }\n/* harmony export */ });\n/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string)=>string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2)=>p2 ? p2.toUpperCase() : p1.toLowerCase());\nconst toPascalCase = (string)=>{\n    const camelCase = toCamelCase(string);\n    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = function() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter((className, index, array)=>{\n        return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n    }).join(\" \").trim();\n};\nconst hasA11yProp = (props)=>{\n    for(const prop in props){\n        if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n            return true;\n        }\n    }\n};\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzPzNjZDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb25cIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={592:(e,r,t)=>{var n=t(722);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},722:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(592);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"e07cec9c54f0\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTZlMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImUwN2NlYzljNTRmMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/PWAInstallPrompt.tsx":
/*!*********************************************!*\
  !*** ./src/components/PWAInstallPrompt.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PWAInstallPrompt; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction PWAInstallPrompt() {\n    _s();\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPrompt, setShowPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isIOS, setIsIOS] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStandalone, setIsStandalone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 檢測是否為iOS設備\n        const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);\n        setIsIOS(iOS);\n        // 檢測是否已經是獨立模式（已安裝）\n        const standalone = window.matchMedia(\"(display-mode: standalone)\").matches;\n        setIsStandalone(standalone);\n        // 監聽beforeinstallprompt事件\n        const handleBeforeInstallPrompt = (e)=>{\n            e.preventDefault();\n            setDeferredPrompt(e);\n            // 延遲顯示提示，讓用戶先體驗應用\n            setTimeout(()=>{\n                if (!standalone) {\n                    setShowPrompt(true);\n                }\n            }, 3000);\n        };\n        // 監聽應用安裝事件\n        const handleAppInstalled = ()=>{\n            setShowPrompt(false);\n            setDeferredPrompt(null);\n            console.log(\"PWA 已安裝\");\n        };\n        window.addEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n        window.addEventListener(\"appinstalled\", handleAppInstalled);\n        return ()=>{\n            window.removeEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n            window.removeEventListener(\"appinstalled\", handleAppInstalled);\n        };\n    }, []);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        try {\n            await deferredPrompt.prompt();\n            const { outcome } = await deferredPrompt.userChoice;\n            if (outcome === \"accepted\") {\n                console.log(\"用戶接受安裝\");\n            } else {\n                console.log(\"用戶拒絕安裝\");\n            }\n            setDeferredPrompt(null);\n            setShowPrompt(false);\n        } catch (error) {\n            console.error(\"安裝過程中發生錯誤:\", error);\n        }\n    };\n    const handleDismiss = ()=>{\n        setShowPrompt(false);\n        // 24小時後再次顯示\n        if (true) {\n            localStorage.setItem(\"pwa-prompt-dismissed\", Date.now().toString());\n        }\n    };\n    // 如果已經是獨立模式，不顯示提示\n    if (isStandalone) return null;\n    // 檢查是否在24小時內被拒絕過（僅在客戶端執行）\n    if (true) {\n        const lastDismissed = localStorage.getItem(\"pwa-prompt-dismissed\");\n        if (lastDismissed && Date.now() - parseInt(lastDismissed) < 24 * 60 * 60 * 1000) {\n            return null;\n        }\n    }\n    // iOS設備顯示手動安裝指引\n    if (isIOS && showPrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-4 left-4 right-4 z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-w-sm mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold text-gray-900 mb-1\",\n                                children: \"安裝考勤系統\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-3\",\n                                children: \"點擊 Safari 底部的分享按鈕 \\uD83D\\uDCE4，然後選擇「加入主畫面」\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleDismiss,\n                                    className: \"px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 transition-colors\",\n                                    children: \"稍後再說\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDismiss,\n                        className: \"w-6 h-6 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                lineNumber: 102,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n            lineNumber: 101,\n            columnNumber: 13\n        }, this);\n    }\n    // Android/Desktop 顯示安裝按鈕\n    if (deferredPrompt && showPrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-4 left-4 right-4 z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-w-sm mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold text-gray-900 mb-1\",\n                                children: \"安裝考勤系統\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-3\",\n                                children: \"安裝到您的設備，享受更好的使用體驗\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleInstallClick,\n                                        className: \"flex items-center space-x-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-indigo-600 hover:to-purple-700 transition-all duration-200 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"安裝\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDismiss,\n                                        className: \"px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors\",\n                                        children: \"稍後再說\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDismiss,\n                        className: \"w-6 h-6 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                lineNumber: 135,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n            lineNumber: 134,\n            columnNumber: 13\n        }, this);\n    }\n    return null;\n}\n_s(PWAInstallPrompt, \"uhrM+U/m9sNA4X3V7eJ2IaPkzmc=\");\n_c = PWAInstallPrompt;\nvar _c;\n$RefreshReg$(_c, \"PWAInstallPrompt\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PWAInstallPrompt.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ServiceWorkerRegistration.tsx":
/*!******************************************************!*\
  !*** ./src/components/ServiceWorkerRegistration.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ServiceWorkerRegistration; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\nfunction ServiceWorkerRegistration() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (\"serviceWorker\" in navigator) {\n            navigator.serviceWorker.register(\"/sw.js\").then((registration)=>{\n                console.log(\"Service Worker 註冊成功:\", registration.scope);\n                // 檢查更新\n                registration.addEventListener(\"updatefound\", ()=>{\n                    const newWorker = registration.installing;\n                    if (newWorker) {\n                        newWorker.addEventListener(\"statechange\", ()=>{\n                            if (newWorker.state === \"installed\" && navigator.serviceWorker.controller) {\n                                // 有新版本可用\n                                console.log(\"新版本可用，建議重新載入頁面\");\n                                // 可以在這裡顯示更新提示\n                                if (confirm(\"發現新版本，是否立即更新？\")) {\n                                    newWorker.postMessage({\n                                        type: \"SKIP_WAITING\"\n                                    });\n                                    window.location.reload();\n                                }\n                            }\n                        });\n                    }\n                });\n            }).catch((error)=>{\n                console.error(\"Service Worker 註冊失敗:\", error);\n            });\n            // 監聽Service Worker控制權變更\n            navigator.serviceWorker.addEventListener(\"controllerchange\", ()=>{\n                console.log(\"Service Worker 控制權已變更\");\n                window.location.reload();\n            });\n        } else {\n            console.log(\"此瀏覽器不支援 Service Worker\");\n        }\n    }, []);\n    return null;\n}\n_s(ServiceWorkerRegistration, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = ServiceWorkerRegistration;\nvar _c;\n$RefreshReg$(_c, \"ServiceWorkerRegistration\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ServiceWorkerRegistration.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   withAuth: function() { return /* binding */ withAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 確保組件已掛載，避免 hydration 錯誤\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 檢查本地存儲中的用戶資訊\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return; // 只有在組件掛載後才執行\n        const initAuth = async ()=>{\n            try {\n                console.log(\"開始認證初始化\");\n                // 首先檢查 localStorage\n                const storedUser = localStorage.getItem(\"user\");\n                console.log(\"本地存儲的用戶資料:\", storedUser);\n                if (storedUser) {\n                    const userData = JSON.parse(storedUser);\n                    console.log(\"解析的用戶資料:\", userData);\n                    setUser(userData);\n                    // 簡化驗證 - 暫時跳過會話驗證，專注解決跳轉問題\n                    console.log(\"用戶已設定，跳過會話驗證\");\n                } else {\n                    console.log(\"沒有本地用戶資料\");\n                }\n            } catch (error) {\n                console.error(\"認證初始化失敗:\", error);\n                if (true) {\n                    localStorage.removeItem(\"user\");\n                }\n                setUser(null);\n            } finally{\n                console.log(\"認證初始化完成，設定 loading = false\");\n                setLoading(false);\n            }\n        };\n        initAuth();\n    }, [\n        mounted\n    ]);\n    const login = (userData)=>{\n        console.log(\"AuthContext login 被調用:\", userData);\n        setUser(userData);\n        if (true) {\n            localStorage.setItem(\"user\", JSON.stringify(userData));\n        }\n        console.log(\"AuthContext 用戶狀態已更新\");\n    };\n    const logout = ()=>{\n        setUser(null);\n        if (true) {\n            localStorage.removeItem(\"user\");\n        }\n        router.push(\"/login\");\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        isAuthenticated: !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"OZTG+WLjn41Piu+e9V9DDAAVZk4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// 保護路由的 HOC\nfunction withAuth(Component) {\n    var _s = $RefreshSig$();\n    return _s(function AuthenticatedComponent(props) {\n        _s();\n        const { isAuthenticated, loading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        console.log(\"withAuth 檢查:\", {\n            loading,\n            isAuthenticated\n        });\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!loading && !isAuthenticated) {\n                console.log(\"withAuth: 未認證，導向登入頁面\");\n                router.push(\"/login\");\n            }\n        }, [\n            isAuthenticated,\n            loading,\n            router\n        ]);\n        if (loading) {\n            console.log(\"withAuth: 載入中\");\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            console.log(\"withAuth: 未認證，返回 null\");\n            return null;\n        }\n        console.log(\"withAuth: 已認證，渲染組件\");\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n            lineNumber: 137,\n            columnNumber: 12\n        }, this);\n    }, \"hfJApfKdkCGjZCs1NVF94JuC2xc=\", false, function() {\n        return [\n            useAuth,\n            next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n        ];\n    });\n}\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\"};\n    if(true) {\n      // 1750058179031\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjL2FwcC9sYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkludGVyXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImludGVyXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsZ0ZBQWdGO0FBQzNHLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUFrSSxjQUFjLHNEQUFzRDtBQUNwTyxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/Y2IxYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wic3R5bGVcIjp7XCJmb250RmFtaWx5XCI6XCInX19JbnRlcl9lOGNlMGMnLCAnX19JbnRlcl9GYWxsYmFja19lOGNlMGMnXCIsXCJmb250U3R5bGVcIjpcIm5vcm1hbFwifSxcImNsYXNzTmFtZVwiOlwiX19jbGFzc05hbWVfZThjZTBjXCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NTAwNTgxNzkwMzFcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiL1VzZXJzL2tldmluLzIwMjRuZXdkZXYvYXR0ZW5kX25leHQvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9taW5pLWNzcy1leHRyYWN0LXBsdWdpbi9obXIvaG90TW9kdWxlUmVwbGFjZW1lbnQuanNcIikobW9kdWxlLmlkLCB7XCJwdWJsaWNQYXRoXCI6XCIvX25leHQvXCIsXCJlc01vZHVsZVwiOmZhbHNlLFwibG9jYWxzXCI6dHJ1ZX0pO1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGNzc1JlbG9hZCk7XG4gICAgICBcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/ODNiNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);