"use client"

import { useState, FormEvent } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import Link from 'next/link'

export default function MobileLoginPage() {
    const [formData, setFormData] = useState({
        employee_id: '',
        password: ''
    })
    const [loading, setLoading] = useState(false)
    const [message, setMessage] = useState('')
    const [messageType, setMessageType] = useState<'success' | 'error' | ''>('')

    const router = useRouter()
    const { login: authLogin } = useAuth()

    // 🔧 自動清除快取功能
    useEffect(() => {
        const clearBrowserCache = async () => {
            try {
                // 清除 localStorage 中的舊資料
                localStorage.removeItem('user')

                console.log('🧹 快取已清除')
            } catch (error) {
                console.log('⚠️ 清除快取時發生錯誤:', error)
            }
        }

        clearBrowserCache()
    }, [])

    const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        setLoading(true)
        setMessage('')
        setMessageType('')

        try {
            console.log('🚀 開始登錄流程')

            // 動態檢測API基礎URL
            const getApiBaseUrl = () => {
                if (typeof window !== 'undefined') {
                    const hostname = window.location.hostname
                    const port = window.location.port

                    // 🔧 修復：在開發環境中強制使用 localhost
                    const isDevelopment = port === '7075' || port === '3000' ||
                                         hostname === 'localhost' || hostname === '127.0.0.1' ||
                                         process.env.NODE_ENV === 'development'

                    if (isDevelopment) {
                        console.log('🔧 開發環境檢測，強制使用 localhost:7072')
                        return 'http://localhost:7072'
                    }

                    // 生產環境邏輯
                    if (hostname === 'localhost' || hostname === '127.0.0.1') {
                        return 'http://localhost:7072'
                    } else {
                        return `http://${hostname}:7072`
                    }
                }
                return 'http://localhost:7072'
            }

            const apiUrl = `${getApiBaseUrl()}/api/login`
            console.log('🌐 使用API URL:', apiUrl)

            // 使用原生fetch，遵循Next.js 14最佳實踐，添加防快取標頭
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                },
                credentials: 'include',
                cache: 'no-store', // Next.js 特有的防快取設定
                body: JSON.stringify({
                    username: formData.employee_id,  // 修正參數名稱
                    password: formData.password
                }),
            })

            console.log('📥 響應狀態:', response.status, response.statusText)

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`)
            }

            const data = await response.json()
            console.log('📊 響應數據:', data)

            if (data.success && data.data?.user) {
                const userData = data.data.user

                // 檢查是否為一般員工
                if (userData.role_id === 999) {
                    setMessage('管理員請使用管理後台登錄')
                    setMessageType('error')
                    return
                }

                // 保存用戶資料到localStorage，確保格式與AuthContext一致
                const user = {
                    id: userData.employee_id,
                    name: userData.employee_name,
                    employee_id: userData.employee_code, // 這是員工編號，如E001
                    department_id: userData.department_id,
                    position: '員工',
                    email: userData.email,
                    role_id: userData.role_id,
                    department_name: userData.department_name,
                    allow_online_clock: true
                }

                console.log('💾 保存用戶資料到localStorage:', user)
                localStorage.setItem('user', JSON.stringify(user))

                // 使用AuthContext的login方法更新狀態
                console.log('🔄 調用AuthContext.login更新認證狀態')
                authLogin(user)

                setMessage('登錄成功！正在進入系統...')
                setMessageType('success')

                // 延遲跳轉，讓用戶看到成功訊息
                setTimeout(() => {
                    console.log('🚀 開始跳轉到主頁')
                    router.push('/m')
                }, 1500)

            } else {
                const errorMessage = data.error || data.message || '登錄失敗，請檢查帳號密碼'
                setMessage(errorMessage)
                setMessageType('error')
            }
        } catch (error) {
            console.error('💥 登錄錯誤:', error)

            let errorMessage = '網路連接失敗'
            if (error instanceof Error) {
                if (error.message.includes('Failed to fetch')) {
                    errorMessage = '無法連接到服務器，請檢查網路連接'
                } else {
                    errorMessage = error.message
                }
            }

            setMessage(errorMessage)
            setMessageType('error')
        } finally {
            setLoading(false)
        }
    }

    // 🔧 手動清除快取功能
    const handleClearCache = async () => {
        try {
            setMessage('正在清除快取...')
            setMessageType('')

            // 清除 localStorage
            localStorage.clear()

            // 清除 sessionStorage
            sessionStorage.clear()

            // 呼叫後端清除快取 API
            const getApiBaseUrl = () => {
                if (typeof window !== 'undefined') {
                    const hostname = window.location.hostname
                    const port = window.location.port

                    // 🔧 修復：在開發環境中強制使用 localhost
                    const isDevelopment = port === '7075' || port === '3000' ||
                                         hostname === 'localhost' || hostname === '127.0.0.1' ||
                                         process.env.NODE_ENV === 'development'

                    if (isDevelopment) {
                        return 'http://localhost:7072'
                    }

                    // 生產環境邏輯
                    if (hostname === 'localhost' || hostname === '127.0.0.1') {
                        return 'http://localhost:7072'
                    } else {
                        return `http://${hostname}:7072`
                    }
                }
                return 'http://localhost:7072'
            }

            const apiUrl = `${getApiBaseUrl()}/api/clear-cache`
            await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache'
                },
                credentials: 'include',
                cache: 'no-store'
            })

            setMessage('快取已清除！請重新整理頁面或重新登錄')
            setMessageType('success')

            // 3秒後自動重新整理頁面
            setTimeout(() => {
                window.location.reload()
            }, 3000)

        } catch (error) {
            console.error('清除快取失敗:', error)
            setMessage('清除快取失敗，請手動重新整理頁面 (Ctrl+Shift+R)')
            setMessageType('error')
        }
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData(prev => ({
            ...prev,
            [name]: value
        }))
        if (message) {
            setMessage('')
            setMessageType('')
        }
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
            {/* 返回首頁按鈕 */}
            <div className="absolute top-4 left-4">
                <Link
                    href="/"
                    className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 hover:text-blue-600 transition-colors"
                >
                    ← 返回首頁
                </Link>
            </div>

            <div className="w-full max-w-md">
                {/* Logo區域 */}
                <div className="text-center mb-8">
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10" />
                        </svg>
                    </div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">員工登入</h1>
                    <p className="text-gray-600">Han AttendanceOS</p>
                </div>

                {/* 登錄表單 */}
                <div className="bg-white rounded-2xl p-6 shadow-xl border border-gray-100">
                    <form onSubmit={handleSubmit} className="space-y-4">
                        {/* 員工編號 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                員工編號
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <input
                                    type="text"
                                    name="employee_id"
                                    value={formData.employee_id}
                                    onChange={handleInputChange}
                                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                    placeholder="請輸入員工編號"
                                    required
                                />
                            </div>
                        </div>

                        {/* 密碼 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                密碼
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                </div>
                                <input
                                    type="password"
                                    name="password"
                                    value={formData.password}
                                    onChange={handleInputChange}
                                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                    placeholder="請輸入密碼"
                                    required
                                />
                            </div>
                        </div>

                        {/* 訊息顯示 */}
                        {message && (
                            <div className={`p-3 rounded-lg text-sm ${messageType === 'success'
                                ? 'bg-green-50 text-green-700 border border-green-200'
                                : 'bg-red-50 text-red-700 border border-red-200'
                                }`}>
                                {message}
                            </div>
                        )}

                        {/* 登錄按鈕 */}
                        <button
                            type="submit"
                            disabled={loading}
                            className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-medium py-3 px-4 rounded-lg hover:from-blue-600 hover:to-indigo-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                        >
                            {loading ? (
                                <div className="flex items-center justify-center">
                                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                                    登入中...
                                </div>
                            ) : (
                                '登入系統'
                            )}
                        </button>

                        {/* 清除快取按鈕 */}
                        <button
                            type="button"
                            onClick={handleClearCache}
                            className="w-full bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 text-sm"
                        >
                            🧹 清除快取並重新整理
                        </button>
                    </form>

                    {/* 測試帳號 */}
                    <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <h4 className="text-sm font-medium text-blue-800 mb-2">測試帳號</h4>
                        <div className="text-sm text-blue-700 space-y-1">
                            <div>帳號：E001</div>
                            <div>密碼：password123</div>
                        </div>
                    </div>

                    {/* 管理員登入連結 */}
                    <div className="mt-4 text-center space-y-2">
                        <Link
                            href="/admin/login"
                            className="block text-sm text-gray-600 hover:text-blue-600 transition-colors"
                        >
                            管理員登入
                        </Link>
                        <Link
                            href="/clear-cache"
                            className="block text-sm text-orange-600 hover:text-orange-700 transition-colors"
                        >
                            🔧 登錄有問題？點此清除快取
                        </Link>
                    </div>
                </div>

                {/* 功能特色 */}
                <div className="mt-8 grid grid-cols-3 gap-4 text-center">
                    <div className="group">
                        <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center mx-auto mb-2 shadow-md group-hover:shadow-lg transition-shadow">
                            <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <p className="text-sm text-gray-600">線上打卡</p>
                    </div>
                    <div className="group">
                        <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center mx-auto mb-2 shadow-md group-hover:shadow-lg transition-shadow">
                            <svg className="w-6 h-6 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <p className="text-sm text-gray-600">請假申請</p>
                    </div>
                    <div className="group">
                        <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center mx-auto mb-2 shadow-md group-hover:shadow-lg transition-shadow">
                            <svg className="w-6 h-6 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <p className="text-sm text-gray-600">考勤記錄</p>
                    </div>
                </div>

                {/* 版本信息 */}
                <div className="mt-6 text-center text-sm text-gray-500">
                    遠漢科技考勤系統 ver 2005.6.12
                </div>
            </div>
        </div>
    )
} 