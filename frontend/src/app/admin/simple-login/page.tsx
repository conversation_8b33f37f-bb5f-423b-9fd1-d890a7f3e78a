"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

export default function SimpleAdminLogin() {
    const router = useRouter()
    const { login: authLogin } = useAuth()
    const [credentials, setCredentials] = useState({
        employee_id: 'admin',
        password: 'admin123'
    })
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState('')

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setLoading(true)
        setError('')

        try {
            // 直接調用 API，不使用複雜的 api-client
            const response = await fetch('http://localhost:7072/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    employee_id: credentials.employee_id,
                    password: credentials.password
                }),
            })

            const data = await response.json()
            console.log('API 響應:', data)

            if (data.success && data.data?.user?.role_id === 999) {
                // 直接創建用戶對象並登入
                const user = {
                    id: data.data.user.employee_id,
                    name: data.data.user.employee_name,
                    employee_id: data.data.user.employee_code,
                    department_id: data.data.user.department_id,
                    position: '系統管理員',
                    email: data.data.user.email,
                    role_id: data.data.user.role_id,
                    department_name: data.data.user.department_name
                }

                authLogin(user)
                router.push('/admin')
            } else {
                setError('登入失敗')
            }
        } catch (err) {
            console.error('登入錯誤:', err)
            setError('系統錯誤')
        } finally {
            setLoading(false)
        }
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full space-y-8">
                <div>
                    <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        簡單管理員登入
                    </h2>
                </div>
                <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            帳號
                        </label>
                        <input
                            type="text"
                            value={credentials.employee_id}
                            onChange={(e) => setCredentials({...credentials, employee_id: e.target.value})}
                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            密碼
                        </label>
                        <input
                            type="password"
                            value={credentials.password}
                            onChange={(e) => setCredentials({...credentials, password: e.target.value})}
                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                        />
                    </div>
                    {error && (
                        <div className="text-red-600 text-sm">{error}</div>
                    )}
                    <button
                        type="submit"
                        disabled={loading}
                        className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50"
                    >
                        {loading ? '登入中...' : '登入'}
                    </button>
                </form>
                
                <div className="text-center">
                    <p className="text-sm text-gray-600">
                        這是簡化版登入頁面，直接調用 API
                    </p>
                    <a href="/admin/login" className="text-indigo-600 hover:text-indigo-500">
                        返回原登入頁面
                    </a>
                </div>
            </div>
        </div>
    )
}
