"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { login } from '@/lib/api-client'
import { useAuth } from '@/contexts/AuthContext'
import type { LoginCredentials } from '@/types'
import { Clock, User, Lock, AlertCircle, Shield } from 'lucide-react'
import Link from 'next/link'

export default function AdminLoginPage() {
    const router = useRouter()
    const { login: authLogin } = useAuth()
    const [credentials, setCredentials] = useState<LoginCredentials>({
        employee_id: '',
        password: ''
    })
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    // 🔧 自動清除快取功能
    useEffect(() => {
        const clearBrowserCache = async () => {
            try {
                // 清除 localStorage 中的舊資料
                localStorage.removeItem('user')

                console.log('🧹 管理員登錄頁面快取已清除')
            } catch (error) {
                console.log('⚠️ 清除快取時發生錯誤:', error)
            }
        }

        clearBrowserCache()
    }, [])

    // 🔧 手動清除快取功能
    const handleClearCache = async () => {
        try {
            setError('正在清除快取...')

            // 清除 localStorage
            localStorage.clear()

            // 清除 sessionStorage
            sessionStorage.clear()

            // 呼叫後端清除快取 API
            const apiUrl = 'http://localhost:7072/api/clear-cache'
            await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache'
                },
                credentials: 'include',
                cache: 'no-store'
            })

            setError('快取已清除！請重新整理頁面或重新登錄')

            // 3秒後自動重新整理頁面
            setTimeout(() => {
                window.location.reload()
            }, 3000)

        } catch (error) {
            console.error('清除快取失敗:', error)
            setError('清除快取失敗，請手動重新整理頁面 (Ctrl+Shift+R)')
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setLoading(true)
        setError('')

        try {
            console.log('開始管理員登入:', credentials)
            const response = await login(credentials)
            console.log('登入響應:', response)

            // 🔧 修復：正確處理API響應結構
            console.log('🔍 完整響應結構:', JSON.stringify(response, null, 2))

            let userData = null

            // 檢查響應是否成功
            if (response?.success && response?.data) {
                // 後端返回格式：{success: true, data: {user: {...}}}
                // api-client 包裝後：{success: true, data: {success: true, data: {user: {...}}}}
                if (response.data.data?.user) {
                    userData = response.data.data.user  // 雙重包裝
                    console.log('✅ 找到用戶數據 (雙重包裝):', userData)
                } else if (response.data.user) {
                    userData = response.data.user  // 單層包裝
                    console.log('✅ 找到用戶數據 (單層包裝):', userData)
                } else {
                    console.log('⚠️ 響應結構異常:', response.data)
                }
            } else {
                console.log('❌ API 響應失敗:', response)
                setError(response?.error || '登入失敗')
                return
            }

            if (userData && userData.role_id === 999) {
                console.log('✅ 管理員驗證通過:', userData)

                // 將 Flask API 格式轉換為前端期望格式
                const user = {
                    id: userData.employee_id,
                    name: userData.employee_name,
                    employee_id: userData.employee_code,
                    department_id: userData.department_id,
                    position: '系統管理員',
                    email: userData.email,
                    role_id: userData.role_id,
                    department_name: userData.department_name
                }

                console.log('轉換後的用戶資料:', user)

                // 使用 AuthContext 的登入方法更新認證狀態
                authLogin(user)
                console.log('已更新認證狀態')

                // 跳轉到管理後台
                console.log('準備跳轉到 /admin')
                router.push('/admin')
            } else if (userData && userData.role_id !== 999) {
                setError('此帳號沒有管理員權限')
            } else {
                console.error('❌ 無法找到用戶數據，響應結構:', response)
                setError('登入失敗，請檢查帳號和密碼')
            }
        } catch (err) {
            console.error('登入錯誤:', err)
            setError('系統錯誤，請稍後再試')
        } finally {
            setLoading(false)
        }
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 relative overflow-hidden">
            {/* 🎨 現代化背景系統 */}
            <div className="absolute inset-0 overflow-hidden">
                {/* 主要漸層背景 */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-white/50 to-indigo-100/30"></div>

                {/* 精緻光效圓圈 */}
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/15 to-cyan-400/15 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-400/15 to-purple-400/15 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-300/10 to-blue-300/10 rounded-full blur-3xl"></div>

                {/* 網格背景 */}
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(59,130,246,0.15)_1px,transparent_0)] bg-[length:24px_24px]"></div>
            </div>

            {/* 主要內容 */}
            <div className="relative z-10 flex items-center justify-center min-h-screen p-6">
                <div className="w-full max-w-md">
                    {/* 🏢 Logo 和品牌區域 */}
                    <div className="text-center mb-8">
                        <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl mx-auto mb-6 flex items-center justify-center shadow-2xl transform hover:scale-105 transition-transform duration-300">
                            <Shield className="w-10 h-10 text-white" />
                        </div>
                        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
                            管理員登入
                        </h1>
                        <p className="text-gray-600 text-lg font-medium">Han AttendanceOS 管理後台</p>
                        <div className="w-16 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mx-auto mt-4"></div>
                    </div>

                    {/* 🎯 登入表單卡片 */}
                    <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 p-8 transform hover:shadow-3xl transition-all duration-300">
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* 管理員帳號 */}
                            <div className="space-y-2">
                                <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700">
                                    <User className="w-4 h-4 text-blue-500" />
                                    <span>管理員帳號</span>
                                </label>
                                <div className="relative">
                                    <input
                                        type="text"
                                        value={credentials.employee_id}
                                        onChange={(e) => setCredentials({ ...credentials, employee_id: e.target.value })}
                                        className="w-full px-4 py-3 bg-gray-50/80 border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200"
                                        placeholder="請輸入管理員帳號"
                                        required
                                    />
                                </div>
                            </div>

                            {/* 密碼 */}
                            <div className="space-y-2">
                                <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700">
                                    <Lock className="w-4 h-4 text-blue-500" />
                                    <span>密碼</span>
                                </label>
                                <div className="relative">
                                    <input
                                        type="password"
                                        value={credentials.password}
                                        onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
                                        className="w-full px-4 py-3 bg-gray-50/80 border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200"
                                        placeholder="請輸入密碼"
                                        required
                                    />
                                </div>
                            </div>

                            {/* 錯誤訊息 */}
                            {error && (
                                <div className="flex items-center space-x-3 p-4 bg-red-50/80 border border-red-200 rounded-xl backdrop-blur-sm">
                                    <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
                                    <span className="text-sm text-red-700 font-medium">{error}</span>
                                </div>
                            )}

                            {/* 登入按鈕 */}
                            <Button
                                type="submit"
                                disabled={loading}
                                className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                            >
                                {loading ? (
                                    <div className="flex items-center justify-center space-x-2">
                                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                        <span>登入中...</span>
                                    </div>
                                ) : (
                                    <div className="flex items-center justify-center space-x-2">
                                        <Shield className="w-4 h-4" />
                                        <span>登入管理後台</span>
                                    </div>
                                )}
                            </Button>

                            {/* 清除快取按鈕 */}
                            <button
                                type="button"
                                onClick={handleClearCache}
                                className="w-full bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded-xl hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 text-sm"
                            >
                                🧹 清除快取並重新整理
                            </button>
                        </form>

                        {/* 🧪 測試帳號提示 */}
                        <div className="mt-6 p-4 bg-blue-50/80 border border-blue-200 rounded-xl backdrop-blur-sm">
                            <div className="flex items-center space-x-2 mb-2">
                                <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                                    <User className="w-3 h-3 text-white" />
                                </div>
                                <h4 className="text-sm font-semibold text-blue-800">測試管理員帳號</h4>
                            </div>
                            <div className="text-sm text-blue-700 space-y-1">
                                <div className="flex items-center space-x-2">
                                    <span className="font-medium">帳號：</span>
                                    <code className="bg-blue-100 px-2 py-1 rounded text-blue-800">admin</code>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <span className="font-medium">密碼：</span>
                                    <code className="bg-blue-100 px-2 py-1 rounded text-blue-800">admin123</code>
                                </div>
                            </div>
                        </div>

                        {/* 🔗 員工登入連結 */}
                        <div className="mt-6 text-center space-y-2">
                            <a
                                href="/m/login"
                                className="inline-flex items-center space-x-2 text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200 group"
                            >
                                <span>員工登入</span>
                                <div className="w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-200">
                                    →
                                </div>
                            </a>
                            <div>
                                <Link
                                    href="/clear-cache"
                                    className="text-sm text-orange-600 hover:text-orange-700 transition-colors"
                                >
                                    🔧 登錄有問題？點此清除快取
                                </Link>
                            </div>
                        </div>
                    </div>

                    {/* 📄 版權資訊 */}
                    <div className="text-center mt-8">
                        <p className="text-sm text-gray-500">
                            © 2025 遠漢科技. Han AttendanceOS v2025.6.12 - 管理後台
                        </p>
                    </div>
                </div>
            </div>
        </div>
    )
} 