"use client"

import React, { useEffect, useState } from 'react'
import Link from 'next/link'

export default function ClearCachePage() {
    const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
    const [message, setMessage] = useState('')

    useEffect(() => {
        const clearAllCache = async () => {
            try {
                setMessage('正在清除所有快取...')
                
                // 1. 清除 localStorage
                localStorage.clear()
                
                // 2. 清除 sessionStorage
                sessionStorage.clear()
                
                // 3. 清除 IndexedDB (如果有的話)
                if ('indexedDB' in window) {
                    try {
                        const databases = await indexedDB.databases()
                        await Promise.all(
                            databases.map(db => {
                                if (db.name) {
                                    return new Promise((resolve, reject) => {
                                        const deleteReq = indexedDB.deleteDatabase(db.name!)
                                        deleteReq.onsuccess = () => resolve(undefined)
                                        deleteReq.onerror = () => reject(deleteReq.error)
                                    })
                                }
                            })
                        )
                    } catch (e) {
                        console.log('清除 IndexedDB 時發生錯誤:', e)
                    }
                }
                
                // 4. 呼叫後端清除 session
                try {
                    const apiUrl = 'http://localhost:7072/api/clear-cache'
                    await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache'
                        },
                        credentials: 'include',
                        cache: 'no-store'
                    })
                } catch (e) {
                    console.log('清除後端快取時發生錯誤:', e)
                }
                
                setStatus('success')
                setMessage('所有快取已成功清除！')
                
                // 3秒後自動跳轉到登錄頁面
                setTimeout(() => {
                    window.location.href = '/m/login'
                }, 3000)
                
            } catch (error) {
                console.error('清除快取失敗:', error)
                setStatus('error')
                setMessage('清除快取時發生錯誤，請手動重新整理頁面')
            }
        }

        clearAllCache()
    }, [])

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                {/* Logo區域 */}
                <div className="text-center mb-8">
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                    </div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">清除快取</h1>
                    <p className="text-gray-600">Han AttendanceOS</p>
                </div>

                {/* 狀態顯示 */}
                <div className="bg-white rounded-2xl p-6 shadow-xl border border-gray-100">
                    <div className="text-center">
                        {status === 'loading' && (
                            <div className="space-y-4">
                                <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto"></div>
                                <p className="text-gray-600">{message}</p>
                            </div>
                        )}

                        {status === 'success' && (
                            <div className="space-y-4">
                                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                                    <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div>
                                    <p className="text-green-700 font-medium">{message}</p>
                                    <p className="text-sm text-gray-500 mt-2">3秒後自動跳轉到登錄頁面...</p>
                                </div>
                            </div>
                        )}

                        {status === 'error' && (
                            <div className="space-y-4">
                                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                                    <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </div>
                                <div>
                                    <p className="text-red-700 font-medium">{message}</p>
                                    <div className="mt-4 space-y-2">
                                        <button
                                            onClick={() => window.location.reload()}
                                            className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
                                        >
                                            重新整理頁面
                                        </button>
                                        <Link
                                            href="/m/login"
                                            className="block w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors text-center"
                                        >
                                            返回登錄頁面
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* 說明文字 */}
                <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="text-sm font-medium text-blue-800 mb-2">快取清除說明</h4>
                    <div className="text-sm text-blue-700 space-y-1">
                        <div>• 清除瀏覽器本地儲存</div>
                        <div>• 清除會話資料</div>
                        <div>• 重置登錄狀態</div>
                        <div>• 解決登錄問題</div>
                    </div>
                </div>

                {/* 手動清除說明 */}
                <div className="mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                    <h4 className="text-sm font-medium text-yellow-800 mb-2">手動清除方法</h4>
                    <div className="text-sm text-yellow-700 space-y-1">
                        <div>• Windows: 按 Ctrl + Shift + R</div>
                        <div>• Mac: 按 Cmd + Shift + R</div>
                        <div>• 或關閉瀏覽器重新開啟</div>
                    </div>
                </div>
            </div>
        </div>
    )
}
