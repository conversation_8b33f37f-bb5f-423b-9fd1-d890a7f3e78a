<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>強制清除快取 - Han AttendanceOS</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
        }
        .status {
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .loading {
            background: #e3f2fd;
            color: #1976d2;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1976d2;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .instructions {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: left;
        }
        .instructions h3 {
            margin-top: 0;
            color: #333;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🧹</div>
        <h1>強制清除快取</h1>
        <p class="subtitle">Han AttendanceOS 快取清除工具</p>
        
        <div id="status" class="status loading">
            <div class="spinner"></div>
            <div>正在清除所有快取...</div>
        </div>

        <div class="instructions">
            <h3>🔧 手動清除方法</h3>
            <ul>
                <li><strong>Windows:</strong> 按 Ctrl + Shift + R</li>
                <li><strong>Mac:</strong> 按 Cmd + Shift + R</li>
                <li><strong>或:</strong> 關閉瀏覽器重新開啟</li>
            </ul>
        </div>

        <div style="margin-top: 20px;">
            <button class="btn" onclick="forceReload()">強制重新載入</button>
            <button class="btn" onclick="goToLogin()">返回登錄頁面</button>
        </div>
    </div>

    <script>
        async function clearAllCache() {
            try {
                // 1. 清除 localStorage
                localStorage.clear();
                
                // 2. 清除 sessionStorage
                sessionStorage.clear();
                
                // 3. 清除 IndexedDB
                if ('indexedDB' in window) {
                    try {
                        const databases = await indexedDB.databases();
                        await Promise.all(
                            databases.map(db => {
                                if (db.name) {
                                    return new Promise((resolve, reject) => {
                                        const deleteReq = indexedDB.deleteDatabase(db.name);
                                        deleteReq.onsuccess = () => resolve();
                                        deleteReq.onerror = () => reject(deleteReq.error);
                                    });
                                }
                            })
                        );
                    } catch (e) {
                        console.log('清除 IndexedDB 時發生錯誤:', e);
                    }
                }
                
                // 4. 清除 Service Worker 快取
                if ('serviceWorker' in navigator) {
                    try {
                        const registrations = await navigator.serviceWorker.getRegistrations();
                        await Promise.all(registrations.map(registration => registration.unregister()));
                    } catch (e) {
                        console.log('清除 Service Worker 時發生錯誤:', e);
                    }
                }
                
                // 5. 清除 Cache API
                if ('caches' in window) {
                    try {
                        const cacheNames = await caches.keys();
                        await Promise.all(cacheNames.map(name => caches.delete(name)));
                    } catch (e) {
                        console.log('清除 Cache API 時發生錯誤:', e);
                    }
                }
                
                // 6. 呼叫後端清除 session
                try {
                    await fetch('http://localhost:7072/api/clear-cache', {
                        method: 'POST',
                        headers: {
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache'
                        },
                        credentials: 'include',
                        cache: 'no-store'
                    });
                } catch (e) {
                    console.log('清除後端快取時發生錯誤:', e);
                }
                
                // 顯示成功訊息
                document.getElementById('status').className = 'status success';
                document.getElementById('status').innerHTML = `
                    <div>✅ 所有快取已成功清除！</div>
                    <div style="margin-top: 10px; font-size: 14px;">2秒後自動跳轉到登錄頁面...</div>
                `;
                
                // 2秒後跳轉
                setTimeout(() => {
                    const timestamp = new Date().getTime();
                    window.location.href = `/m/login?t=${timestamp}`;
                }, 2000);
                
            } catch (error) {
                console.error('清除快取失敗:', error);
                document.getElementById('status').className = 'status error';
                document.getElementById('status').innerHTML = `
                    <div>❌ 清除快取時發生錯誤</div>
                    <div style="margin-top: 10px; font-size: 14px;">請手動重新整理頁面</div>
                `;
            }
        }
        
        function forceReload() {
            // 強制重新載入，忽略快取
            window.location.reload(true);
        }
        
        function goToLogin() {
            const timestamp = new Date().getTime();
            window.location.href = `/m/login?t=${timestamp}`;
        }
        
        // 頁面載入時自動開始清除快取
        window.addEventListener('load', clearAllCache);
    </script>
</body>
</html>
