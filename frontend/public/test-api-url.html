<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API URL 測試 - Han AttendanceOS</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }
        .btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #1976d2;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API URL 檢測測試</h1>
        <p>這個頁面用來測試前端的 API URL 檢測邏輯是否正確。</p>
        
        <div class="info-box">
            <h3>當前環境資訊</h3>
            <div id="env-info"></div>
        </div>
        
        <div class="info-box">
            <h3>API URL 檢測結果</h3>
            <div id="api-url-result"></div>
        </div>
        
        <div>
            <button class="btn" onclick="testApiConnection()">測試 API 連接</button>
            <button class="btn" onclick="clearAllCache()">清除所有快取</button>
            <button class="btn" onclick="location.reload(true)">強制重新載入</button>
        </div>
        
        <div id="test-result"></div>
    </div>

    <script>
        // API URL 檢測邏輯（與前端相同）
        function getApiBaseUrl() {
            if (typeof window !== 'undefined') {
                const hostname = window.location.hostname;
                const port = window.location.port;

                console.log('🌐 檢測到的主機信息:', { hostname, port, href: window.location.href });

                // 🔧 修復：在開發環境中強制使用 localhost
                const isDevelopment = port === '7075' || port === '3000' || 
                                     hostname === 'localhost' || hostname === '127.0.0.1' ||
                                     process.env.NODE_ENV === 'development';

                if (isDevelopment) {
                    console.log('🔧 開發環境檢測，強制使用 localhost:7072');
                    return 'http://localhost:7072';
                }

                // 生產環境邏輯
                if (hostname === 'localhost' || hostname === '127.0.0.1') {
                    return 'http://localhost:7072';
                } else {
                    return `http://${hostname}:7072`;
                }
            }
            return 'http://localhost:7072';
        }

        // 顯示環境資訊
        function showEnvironmentInfo() {
            const info = {
                hostname: window.location.hostname,
                port: window.location.port,
                protocol: window.location.protocol,
                href: window.location.href,
                userAgent: navigator.userAgent,
                nodeEnv: process?.env?.NODE_ENV || 'undefined'
            };
            
            document.getElementById('env-info').innerHTML = `
                <pre>${JSON.stringify(info, null, 2)}</pre>
            `;
        }

        // 顯示 API URL 檢測結果
        function showApiUrlResult() {
            const apiUrl = getApiBaseUrl();
            const isDevelopment = window.location.port === '7075' || window.location.port === '3000' || 
                                 window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            
            document.getElementById('api-url-result').innerHTML = `
                <p><strong>檢測到的 API URL:</strong> <code>${apiUrl}</code></p>
                <p><strong>是否為開發環境:</strong> ${isDevelopment ? '✅ 是' : '❌ 否'}</p>
                <p><strong>完整 API 登錄 URL:</strong> <code>${apiUrl}/api/login</code></p>
            `;
        }

        // 測試 API 連接
        async function testApiConnection() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="info-box">🔄 正在測試 API 連接...</div>';
            
            try {
                const apiUrl = getApiBaseUrl();
                const testUrl = `${apiUrl}/api/health`;
                
                console.log('🧪 測試 API URL:', testUrl);
                
                const response = await fetch(testUrl, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache'
                    },
                    cache: 'no-store'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="info-box success">
                            <h3>✅ API 連接成功！</h3>
                            <p><strong>URL:</strong> ${testUrl}</p>
                            <p><strong>狀態:</strong> ${response.status} ${response.statusText}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('❌ API 連接失敗:', error);
                resultDiv.innerHTML = `
                    <div class="info-box error">
                        <h3>❌ API 連接失敗</h3>
                        <p><strong>錯誤:</strong> ${error.message}</p>
                        <p><strong>可能原因:</strong></p>
                        <ul>
                            <li>後端服務未啟動</li>
                            <li>API URL 檢測錯誤</li>
                            <li>網路連接問題</li>
                            <li>CORS 設定問題</li>
                        </ul>
                    </div>
                `;
            }
        }

        // 清除所有快取
        async function clearAllCache() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                }
                
                alert('✅ 快取已清除！頁面將重新載入。');
                location.reload(true);
            } catch (error) {
                alert('❌ 清除快取失敗: ' + error.message);
            }
        }

        // 頁面載入時執行
        window.addEventListener('load', () => {
            showEnvironmentInfo();
            showApiUrlResult();
        });
    </script>
</body>
</html>
