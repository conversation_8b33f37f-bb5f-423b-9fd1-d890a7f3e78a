/** @type {import('next').NextConfig} */
const nextConfig = {
    // 確保靜態文件正確提供
    async headers() {
        return [
            {
                source: '/favicon.ico',
                headers: [
                    {
                        key: 'Content-Type',
                        value: 'image/x-icon',
                    },
                    {
                        key: 'Cache-Control',
                        value: 'public, max-age=86400',
                    },
                ],
            },
            {
                source: '/icon.svg',
                headers: [
                    {
                        key: 'Content-Type',
                        value: 'image/svg+xml',
                    },
                    {
                        key: 'Cache-Control',
                        value: 'public, max-age=86400',
                    },
                ],
            },
        ]
    },
}

module.exports = nextConfig
