# SQL 安全檢查報告
檢查時間: 2025-06-16 13:14:54.514237

## 摘要
- 總查詢數: 974
- 高風險: 125
- 中風險: 849
- 低風險: 0

## 🔴 高風險查詢 (需要立即修復)
### 1. app_backup_before_fix.py:879
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" SELECT d.name as department, COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count, COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count, COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count, COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count, COUNT(CASE WHEN a.status = 'manual' THEN 1 END) as manual_count
```

### 2. app_backup_before_fix.py:902
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" SELECT DATE(a.check_in) as date, COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count, COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count, COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count, COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count
```

### 3. app_backup_before_fix.py:961
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" SELECT e.name, d.name as department, a.check_in, a.check_out, a.status, a.note FROM attendance a JOIN employees e ON a.employee_id = e.id JOIN departments d ON e.department_id = d.id {where_clause} ORDER BY a.check_in DESC """, params, )
```

### 4. app_backup_before_fix.py:1310
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" UPDATE employees SET {', '.join(set_clause)}
```

### 5. app_backup_before_fix.py:2485
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT * FROM {table_name} WHERE is_active = 1 ORDER BY {order_by_clause} """)
```

### 6. app_backup_before_fix.py:2652
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f"SELECT * FROM {table_name} WHERE id = ?", (item_id,))
```

### 7. app_backup_before_fix.py:2739
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" UPDATE {table_name} SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (item_id,))
```

### 8. app_backup_before_fix.py:2842
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f"SELECT COUNT(*) FROM {table}")
```

### 9. app_backup_before_fix.py:3198
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" UPDATE leaves SET {', '.join(update_fields)}
```

### 10. app_backup_before_fix.py:4265
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT leave_type, COUNT(*) as leave_count, SUM(julianday(end_date) - julianday(start_date) + 1) as total_leave_days
```

### 11. app_backup_before_fix.py:4444
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" UPDATE schedules SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
```

### 12. app_backup_before_fix.py:4827
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 13. app_backup_before_fix.py:5732
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 14. app_backup_before_fix.py:5976
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f"SELECT id, name, employee_id FROM employees {employee_filter}", employee_ids)
```

### 15. app_backup_before_fix.py:6926
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 16. app_backup.py:879
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" SELECT d.name as department, COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count, COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count, COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count, COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count, COUNT(CASE WHEN a.status = 'manual' THEN 1 END) as manual_count
```

### 17. app_backup.py:902
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" SELECT DATE(a.check_in) as date, COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count, COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count, COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count, COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count
```

### 18. app_backup.py:961
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" SELECT e.name, d.name as department, a.check_in, a.check_out, a.status, a.note FROM attendance a JOIN employees e ON a.employee_id = e.id JOIN departments d ON e.department_id = d.id {where_clause} ORDER BY a.check_in DESC """, params, )
```

### 19. app_backup.py:1310
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" UPDATE employees SET {', '.join(set_clause)}
```

### 20. app_backup.py:2485
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT * FROM {table_name} WHERE is_active = 1 ORDER BY {order_by_clause} """)
```

### 21. app_backup.py:2652
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f"SELECT * FROM {table_name} WHERE id = ?", (item_id,))
```

### 22. app_backup.py:2739
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" UPDATE {table_name} SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (item_id,))
```

### 23. app_backup.py:2842
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f"SELECT COUNT(*) FROM {table}")
```

### 24. app_backup.py:3198
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" UPDATE leaves SET {', '.join(update_fields)}
```

### 25. app_backup.py:4265
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT leave_type, COUNT(*) as leave_count, SUM(julianday(end_date) - julianday(start_date) + 1) as total_leave_days
```

### 26. app_backup.py:4444
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" UPDATE schedules SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
```

### 27. app_backup.py:4827
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 28. app_backup.py:5732
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 29. app_backup.py:5976
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f"SELECT id, name, employee_id FROM employees {employee_filter}", employee_ids)
```

### 30. app_backup.py:6926
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 31. db_schema_check.py:52
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f"PRAGMA table_info({table_name})")
```

### 32. db_schema_check.py:56
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f"PRAGMA foreign_key_list({table_name})")
```

### 33. db_schema_check.py:60
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f"PRAGMA index_list({table_name})")
```

### 34. db_schema_check.py:172
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
```

### 35. db_schema_check.py:201
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f"SELECT COUNT(*) FROM {table}")
```

### 36. fix_database_structure.py:53
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f"SELECT * FROM {table}")
```

### 37. fix_database_structure.py:155
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: CREATE, EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" INSERT INTO schedules ( employee_id, shift_date, shift_id, status, actual_start_time, actual_end_time, note, created_at )
```

### 38. attendance_api_old.py:1002
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 39. attendance_api_old.py:1170
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute(delete_query, [target_date] + dept_params)
```

### 40. attendance_api_old.py:1196
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(punch_records_query, [target_date] + dept_params + [target_date] + dept_params)
```

### 41. attendance_api_old.py:1220
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_records_query, [target_date] + dept_params)
```

### 42. attendance_api_old.py:1425
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(recalculation_query, [target_date] + dept_params)
```

### 43. attendance_api_old.py:1544
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_coverage_query, active_employee_ids + [target_date])
```

### 44. attendance_api_old.py:1562
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(punch_coverage_query, active_employee_ids + [target_date, target_date])
```

### 45. attendance_api_old.py:1579
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_coverage_query, active_employee_ids + [target_date, target_date])
```

### 46. attendance_api_old.py:1877
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 47. attendance_api_old.py:1906
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: DELETE, EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" DELETE FROM attendance_records WHERE work_date = ? AND employee_id IN ( SELECT id FROM employees WHERE department_id IN ({placeholders})
```

### 48. attendance_api_old.py:2360
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 49. attendance_api_old.py:2622
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_coverage_query, active_employee_ids + [target_date])
```

### 50. attendance_api_old.py:2640
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(punch_coverage_query, active_employee_ids + [target_date, target_date])
```

### 51. attendance_api_old.py:2657
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_coverage_query, active_employee_ids + [target_date, target_date])
```

### 52. attendance_api_old.py:2955
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 53. attendance_api_old.py:2984
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: DELETE, EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" DELETE FROM attendance_records WHERE work_date = ? AND employee_id IN ( SELECT id FROM employees WHERE department_id IN ({placeholders})
```

### 54. attendance_api_old.py:3438
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 55. attendance_api_old.py:3700
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_coverage_query, active_employee_ids + [target_date])
```

### 56. attendance_api_old.py:3718
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(punch_coverage_query, active_employee_ids + [target_date, target_date])
```

### 57. attendance_api_old.py:3735
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_coverage_query, active_employee_ids + [target_date, target_date])
```

### 58. attendance_api_old.py:4033
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 59. attendance_api_old.py:4062
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: DELETE, EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" DELETE FROM attendance_records WHERE work_date = ? AND employee_id IN ( SELECT id FROM employees WHERE department_id IN ({placeholders})
```

### 60. employee_api.py:508
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" UPDATE employees SET {', '.join(set_clause)}
```

### 61. employee_api.py:741
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" UPDATE departments SET {', '.join(set_clause)}
```

### 62. attendance_api_broken.py:1004
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 63. attendance_api_broken.py:1229
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(punch_records_query, [target_date] + dept_params + [target_date] + dept_params)
```

### 64. attendance_api_broken.py:1253
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_records_query, [target_date] + dept_params)
```

### 65. attendance_api_broken.py:1458
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(recalculation_query, [target_date] + dept_params)
```

### 66. attendance_api_broken.py:1577
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_coverage_query, active_employee_ids + [target_date])
```

### 67. attendance_api_broken.py:1595
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(punch_coverage_query, active_employee_ids + [target_date, target_date])
```

### 68. attendance_api_broken.py:1612
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_coverage_query, active_employee_ids + [target_date, target_date])
```

### 69. attendance_api_broken.py:1842
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 70. attendance_api_broken.py:1871
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: DELETE, EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" DELETE FROM attendance WHERE work_date = ? AND employee_id IN ( SELECT id FROM employees WHERE department_id IN ({placeholders})
```

### 71. attendance_api_broken.py:2445
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 72. attendance_api_broken.py:2718
**風險**: 使用%格式化構建SQL查詢, 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute("INSERT INTO attendance (employee_id, check_in, check_out, status, note, created_at) VALUES (?, ?, ?, ?, ?, ?)", (emp_id, check_in_time, check_out_time, status, note, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
```

### 73. attendance_api_broken.py:2971
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, params + [limit, offset])
```

### 74. attendance_edit_api.py:444
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT SUM( CASE WHEN time_type = 'full_day' THEN 8.0 WHEN time_type = 'partial_day' AND start_time IS NOT NULL AND end_time IS NOT NULL THEN (CAST(SUBSTR(end_time, 1, 2) AS INTEGER) * 60 + CAST(SUBSTR(end_time, 4, 2) AS INTEGER) -
```

### 75. attendance_api.py:255
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params + [per_page, offset])
```

### 76. attendance_api.py:582
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT SUM( CASE WHEN time_type = 'full_day' THEN 8.0 WHEN time_type = 'partial_day' AND start_time IS NOT NULL AND end_time IS NOT NULL THEN (CAST(SUBSTR(end_time, 1, 2) AS INTEGER) * 60 + CAST(SUBSTR(end_time, 4, 2) AS INTEGER) -
```

### 77. attendance_api.py:1034
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 78. attendance_api_backup.py:1002
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 79. attendance_api_backup.py:1170
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute(delete_query, [target_date] + dept_params)
```

### 80. attendance_api_backup.py:1196
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(punch_records_query, [target_date] + dept_params + [target_date] + dept_params)
```

### 81. attendance_api_backup.py:1220
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_records_query, [target_date] + dept_params)
```

### 82. attendance_api_backup.py:1425
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(recalculation_query, [target_date] + dept_params)
```

### 83. attendance_api_backup.py:1544
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_coverage_query, active_employee_ids + [target_date])
```

### 84. attendance_api_backup.py:1562
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(punch_coverage_query, active_employee_ids + [target_date, target_date])
```

### 85. attendance_api_backup.py:1579
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_coverage_query, active_employee_ids + [target_date, target_date])
```

### 86. attendance_api_backup.py:1877
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 87. attendance_api_backup.py:1906
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: DELETE, EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" DELETE FROM attendance_records WHERE work_date = ? AND employee_id IN ( SELECT id FROM employees WHERE department_id IN ({placeholders})
```

### 88. attendance_api_backup.py:2360
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 89. attendance_api_backup.py:2622
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_coverage_query, active_employee_ids + [target_date])
```

### 90. attendance_api_backup.py:2640
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(punch_coverage_query, active_employee_ids + [target_date, target_date])
```

### 91. attendance_api_backup.py:2657
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_coverage_query, active_employee_ids + [target_date, target_date])
```

### 92. attendance_api_backup.py:2955
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 93. attendance_api_backup.py:2984
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: DELETE, EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" DELETE FROM attendance_records WHERE work_date = ? AND employee_id IN ( SELECT id FROM employees WHERE department_id IN ({placeholders})
```

### 94. attendance_api_backup.py:3438
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 95. attendance_api_backup.py:3700
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_coverage_query, active_employee_ids + [target_date])
```

### 96. attendance_api_backup.py:3718
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(punch_coverage_query, active_employee_ids + [target_date, target_date])
```

### 97. attendance_api_backup.py:3735
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_coverage_query, active_employee_ids + [target_date, target_date])
```

### 98. attendance_api_backup.py:4033
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 99. attendance_api_backup.py:4062
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: DELETE, EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" DELETE FROM attendance_records WHERE work_date = ? AND employee_id IN ( SELECT id FROM employees WHERE department_id IN ({placeholders})
```

### 100. attendance_processing_api.py:241
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f"SELECT COUNT(*) FROM employees WHERE department_id IN ({placeholders}) AND status = 'active'", department_ids, )
```

### 101. attendance_processing_api.py:443
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" SELECT id, employee_id, name FROM employees WHERE department_id IN ({placeholders}) AND status = 'active'
```

### 102. attendance_processing_api.py:458
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute( f""" SELECT id, employee_id, name FROM employees WHERE id IN ({placeholders}) AND status = 'active'
```

### 103. system_api_backup.py:556
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f"SELECT id FROM {table_name} WHERE id = ?", (item_id,))
```

### 104. system_api_backup.py:587
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f"SELECT id FROM {table_name} WHERE id = ?", (item_id,))
```

### 105. system_api_backup.py:591
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" UPDATE {table_name} SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (item_id,))
```

### 106. system_api_backup.py:746
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f"SELECT COUNT(*) FROM {table}")
```

### 107. auth_api.py:471
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, params + [limit, offset])
```

### 108. leave_api.py:325
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 109. leave_api.py:743
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT lr.status, COUNT(*) as count, SUM(lr.leave_hours) as total_days
```

### 110. leave_api.py:762
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT lt.name as leave_type_name, COUNT(*) as count, SUM(lr.leave_hours) as total_days
```

### 111. leave_api.py:784
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT strftime('%m', lr.start_date) as month, COUNT(*) as count, SUM(lr.leave_hours) as total_days
```

### 112. leave_api.py:903
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(records_query, query_params + [limit, offset])
```

### 113. leave_api.py:1125
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" UPDATE leaves SET {', '.join(update_fields)}
```

### 114. shift_api.py:1274
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f""" SELECT s.employee_id, s.shift_date, s.shift_id, e.name as employee_name FROM schedules s JOIN employees e ON s.employee_id = e.id WHERE {' AND '.join(where_conditions)}
```

### 115. report_api.py:229
**風險**: 使用%格式化構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT ar.id, ar.employee_id, e.name as employee_name, ar.check_in, ar.check_out, CASE WHEN ar.check_in IS NOT NULL AND ar.check_out IS NOT NULL THEN ROUND((strftime('%s', ar.check_out) - strftime('%s', ar.check_in)) / 3600.0, 2) ELSE 0 END, 0, ar.status FROM attendance ar JOIN employees e ON ar.employee_id = e.id WHERE DATE(ar.check_in) BETWEEN ? AND ?
```

### 116. report_api.py:348
**風險**: 使用字串拼接構建SQL查詢, 使用%格式化構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" SELECT strftime('{date_format}', ar.check_in) as period, COUNT(*) as attendance_count, AVG(CASE WHEN ar.check_in IS NOT NULL AND ar.check_out IS NOT NULL THEN ROUND((strftime('%s', ar.check_out) - strftime('%s', ar.check_in)) / 3600.0, 2) ELSE 0 END) as avg_work_hours, SUM(0) as total_overtime, COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count, COUNT(CASE WHEN ar.status = 'early_leave' THEN 1 END) as early_leave_count
```

### 117. report_api.py:407
**風險**: 使用%格式化構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT d.id, d.name as department_name, COUNT(DISTINCT e.id) as employee_count, COUNT(ar.id) as attendance_count, AVG(CASE WHEN ar.check_in IS NOT NULL AND ar.check_out IS NOT NULL THEN ROUND((strftime('%s', ar.check_out) - strftime('%s', ar.check_in)) / 3600.0, 2) ELSE 0 END) as avg_work_hours, SUM(0) as total_overtime, COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count, COUNT(CASE WHEN ar.status = 'early_leave' THEN 1 END) as early_leave_count
```

### 118. report_api.py:634
**風險**: 使用字串拼接構建SQL查詢, 使用%格式化構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" SELECT e.id, e.name as employee_name, d.name as department_name, COUNT(ar.id) as attendance_days, AVG(CASE WHEN ar.check_in IS NOT NULL AND ar.check_out IS NOT NULL THEN ROUND((strftime('%s', ar.check_out) - strftime('%s', ar.check_in)) / 3600.0, 2) ELSE 0 END) as avg_work_hours, SUM(0) as total_overtime, COUNT(CASE WHEN ar.status = 'on_time' THEN 1 END) as on_time_count, COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count
```

### 119. report_api.py:736
**風險**: 使用%格式化構建SQL查詢, 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT COUNT(*) as total_records, COUNT(DISTINCT employee_id) as active_employees, AVG(CASE WHEN check_in IS NOT NULL AND check_out IS NOT NULL THEN (strftime("%s", check_out) - strftime("%s", check_in)) / 3600.0 ELSE 0 END) as avg_work_hours, 0 as total_overtime FROM attendance WHERE strftime('%Y-%m', check_in) = ?
```

### 120. system_api.py:550
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f"SELECT id FROM {table_name} WHERE id = ?", (item_id,))
```

### 121. system_api.py:582
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f"SELECT id FROM {table_name} WHERE id = ?", (item_id,))
```

### 122. system_api.py:587
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: DELETE, EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f"DELETE FROM {table_name} WHERE id = ?", (item_id,))
```

### 123. system_api.py:589
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(f""" UPDATE {table_name} SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (item_id,))
```

### 124. system_api.py:772
**風險**: 使用字串拼接構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名, 可能未使用參數化查詢
```sql
cursor.execute(f"SELECT COUNT(*) FROM {table}")
```

### 125. attendance_service.py:318
**風險**: 使用字串拼接構建SQL查詢, 使用%格式化構建SQL查詢, 包含危險關鍵字: EXEC, 可能包含動態表名或欄位名
```sql
cursor.execute(""" UPDATE attendance SET check_out = ? WHERE id = ? """, (now.strftime('%Y-%m-%d %H:%M:%S.%f'), record[0]))
```

## 🟡 中風險查詢 (建議修復)
### 1. recalculate_all_leave_hours.py:35
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (employee_id, work_date))
```

### 2. recalculate_all_leave_hours.py:94
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query)
```

### 3. recalculate_all_leave_hours.py:144
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(update_query, (correct_leave_hours, attendance_id))
```

### 4. recalculate_all_leave_hours.py:148
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("UPDATE attendance SET status = 'leave' WHERE id = ?", (attendance_id,))
```

### 5. recalculate_all_leave_hours.py:153
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("UPDATE attendance SET leave_hours = 0.0 WHERE id = ?", (attendance_id,))
```

### 6. database.py:695
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO punch_records (device_id, employee_id, punch_date, punch_time, punch_datetime, status_code, raw_data, note)
```

### 7. app_backup_before_fix.py:203
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT COUNT(*) as total, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late, SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave, SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent, SUM(CASE WHEN status = 'manual' THEN 1 ELSE 0 END) as manual
```

### 8. app_backup_before_fix.py:226
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT COUNT(*) FROM leaves WHERE status = 'pending'
```

### 9. app_backup_before_fix.py:235
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT d.name, COUNT(e.id)
```

### 10. app_backup_before_fix.py:246
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status FROM attendance a JOIN employees e ON a.employee_id = e.id ORDER BY a.check_in DESC LIMIT 5 """ )
```

### 11. app_backup_before_fix.py:291
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_checkins, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal_count, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count, SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave_count, SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
```

### 12. app_backup_before_fix.py:306
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as week_total
```

### 13. app_backup_before_fix.py:315
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as pending_leaves
```

### 14. app_backup_before_fix.py:324
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.name, COUNT(a.id) as total_attendance, SUM(CASE WHEN a.status = 'normal' THEN 1 ELSE 0 END) as normal_count
```

### 15. app_backup_before_fix.py:345
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT DATE(check_in) as date, COUNT(*) as total, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late
```

### 16. app_backup_before_fix.py:418
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO attendance (employee_id, check_in, status)
```

### 17. app_backup_before_fix.py:449
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO attendance (employee_id, check_in, check_out, status, note)
```

### 18. app_backup_before_fix.py:490
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name, employee_id FROM employees WHERE id = ?", (employee_id,))
```

### 19. app_backup_before_fix.py:497
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT id, check_in, check_out, status, note FROM attendance WHERE employee_id = ? AND DATE(check_in) = ?
```

### 20. app_backup_before_fix.py:577
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name FROM employees WHERE id = ?", (employee_id,))
```

### 21. app_backup_before_fix.py:621
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status, d.name as department_name FROM attendance a JOIN employees e ON a.employee_id = e.id LEFT JOIN departments d ON e.department_id = d.id ORDER BY a.check_in DESC LIMIT ? """, (limit,))
```

### 22. app_backup_before_fix.py:690
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT rule_type, rule_value, description FROM schedule_rules WHERE rule_type IN ( 'day_change_time', 'cross_day_attendance', 'first_punch_as_checkin', 'last_punch_as_checkout', 'late_tolerance_minutes', 'early_leave_tolerance_minutes' )
```

### 23. app_backup_before_fix.py:729
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE schedule_rules SET rule_value = ? WHERE rule_type = ? """, (str(rule_value), rule_type))
```

### 24. app_backup_before_fix.py:737
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO schedule_rules (rule_type, rule_value, description)
```

### 25. app_backup_before_fix.py:795
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO schedules (employee_id, shift_date, shift_type, start_time, end_time)
```

### 26. app_backup_before_fix.py:827
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT * FROM schedule_rules")
```

### 27. app_backup_before_fix.py:833
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO schedule_rules (rule_type, rule_value, description)
```

### 28. app_backup_before_fix.py:1059
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 29. app_backup_before_fix.py:1138
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM departments WHERE id = ?", (data['department_id'],))
```

### 30. app_backup_before_fix.py:1143
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM permissions WHERE id = ?", (data['role_id'],))
```

### 31. app_backup_before_fix.py:1148
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (data['manager_id'],))
```

### 32. app_backup_before_fix.py:1153
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (data['employee_id'],))
```

### 33. app_backup_before_fix.py:1158
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO employees (name, employee_id, department_id, position, email, phone, role_id, manager_id, password, hire_date, status, salary_level, id_number, address, emergency_contact, emergency_phone, photo_url, shift_type)
```

### 34. app_backup_before_fix.py:1217
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT e.id, e.name, e.employee_id, e.department_id, d.name as department_name, e.position, e.email, e.phone, e.password, e.hire_date, e.status, e.salary_level, e.id_number, e.address, e.emergency_contact, e.emergency_phone, e.role_id, p.role_name, e.manager_id, m.name as manager_name, e.shift_type FROM employees e JOIN departments d ON e.department_id = d.id LEFT JOIN permissions p ON e.role_id = p.id LEFT JOIN employees m ON e.manager_id = m.id WHERE e.id = ? """, (employee_id,), )
```

### 35. app_backup_before_fix.py:1259
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM departments WHERE id = ?", (data["department_id"],))
```

### 36. app_backup_before_fix.py:1264
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM permissions WHERE id = ?", (data["role_id"],))
```

### 37. app_backup_before_fix.py:1269
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (data["manager_id"],))
```

### 38. app_backup_before_fix.py:1337
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute("DELETE FROM employees WHERE id = ?", (employee_id,))
```

### 39. app_backup_before_fix.py:1360
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name, p.role_name, e.department_id FROM employees e JOIN departments d ON e.department_id = d.id LEFT JOIN permissions p ON e.role_id = p.id WHERE e.role_id <= 2 OR e.id IN ( SELECT DISTINCT manager_id FROM employees WHERE manager_id IS NOT NULL )
```

### 40. app_backup_before_fix.py:1395
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT department_id FROM employees WHERE id = ? """, (employee_id,))
```

### 41. app_backup_before_fix.py:1406
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name FROM employees e JOIN departments d ON e.department_id = d.id WHERE e.id != ? AND ( e.department_id = ? OR e.role_id <= 2 )
```

### 42. app_backup_before_fix.py:1444
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT d.id, d.name, d.manager_id, e.name as manager_name, d.description FROM departments d LEFT JOIN employees e ON d.manager_id = e.id """ )
```

### 43. app_backup_before_fix.py:1463
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO departments (name, manager_id, description)
```

### 44. app_backup_before_fix.py:1489
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT * FROM departments WHERE id = ?", (dept_id,))
```

### 45. app_backup_before_fix.py:1506
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE leaves SET status = ?, comment = ?, approved_at = ? WHERE id = ? """, (status, comment, datetime.now(), dept_id))
```

### 46. app_backup_before_fix.py:1521
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT status FROM leaves WHERE id = ?", (dept_id,))
```

### 47. app_backup_before_fix.py:1530
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE leaves SET leave_type = ?, start_date = ?, end_date = ?, reason = ? WHERE id = ? """, ( data.get("leave_type"), data.get("start_date"), data.get("end_date"), data.get("reason"), dept_id ))
```

### 48. app_backup_before_fix.py:1550
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute("DELETE FROM leaves WHERE id = ?", (dept_id,))
```

### 49. app_backup_before_fix.py:1578
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT * FROM schedules WHERE employee_id = ? AND shift_date BETWEEN ? AND ? ORDER BY shift_date, start_time """, (employee_id, start_date, end_date))
```

### 50. app_backup_before_fix.py:1612
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT strftime('%w', check_in) as day_of_week, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late, SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent, COUNT(*) as total
```

### 51. app_backup_before_fix.py:1684
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.name as department, COUNT(a.id) as total_records, SUM(CASE WHEN a.status = 'normal' THEN 1 ELSE 0 END) as normal_count
```

### 52. app_backup_before_fix.py:1732
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT CASE WHEN strftime('%H:%M', check_in) BETWEEN '08:00' AND '08:30' THEN '8:00-8:30'
```

### 53. app_backup_before_fix.py:1783
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT leave_type, COUNT(*) as count
```

### 54. app_backup_before_fix.py:1827
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as on_time
```

### 55. app_backup_before_fix.py:1841
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_checkouts, SUM(CASE WHEN strftime('%H', check_out) >= '18' THEN 1 ELSE 0 END) as overtime_count
```

### 56. app_backup_before_fix.py:1856
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(DISTINCT e.id) as total_employees, COUNT(DISTINCT e.id) - COUNT(DISTINCT CASE WHEN a.status = 'absent' THEN e.id END) as full_attendance_employees
```

### 57. app_backup_before_fix.py:1910
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT * FROM system_settings ORDER BY category, setting_key")
```

### 58. app_backup_before_fix.py:1930
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 59. app_backup_before_fix.py:1954
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value FROM system_settings WHERE category = 'attendance_rules' """)
```

### 60. app_backup_before_fix.py:1983
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 61. app_backup_before_fix.py:2007
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value FROM system_settings WHERE category = 'notifications' """)
```

### 62. app_backup_before_fix.py:2035
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 63. app_backup_before_fix.py:2060
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT p.id, p.role_name, p.permission_level, p.description, COUNT(e.id) as user_count
```

### 64. app_backup_before_fix.py:2082
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO permissions (role_name, permission_level, description)
```

### 65. app_backup_before_fix.py:2113
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT d.id, d.name, d.manager_id, e.name as manager_name FROM departments d LEFT JOIN employees e ON d.manager_id = e.id """ )
```

### 66. app_backup_before_fix.py:2133
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE departments SET manager_id = ? WHERE id = ? """, (manager_id, dept_id), )
```

### 67. app_backup_before_fix.py:2158
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.id, d.name, COUNT(e.id) as total_employees, SUM(CASE WHEN e.status = 'active' THEN 1 ELSE 0 END) as active_employees, SUM(CASE WHEN e.status = 'trial' THEN 1 ELSE 0 END) as trial_employees
```

### 68. app_backup_before_fix.py:2196
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_checkins, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal_count, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count, SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave_count, SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
```

### 69. app_backup_before_fix.py:2211
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as week_total
```

### 70. app_backup_before_fix.py:2220
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as pending_leaves
```

### 71. app_backup_before_fix.py:2260
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT l.id, l.employee_id, e.name as employee_name, e.employee_id as emp_id, e.position as employee_position, d.name as department_name, l.leave_type, l.start_date, l.end_date, l.reason, l.created_at, l.status, l.approver_id, a.name as approver_name, a.position as approver_position, l.substitute_id, s.name as substitute_name, s.position as substitute_position, e.photo_url FROM leaves l JOIN employees e ON l.employee_id = e.id LEFT JOIN departments d ON e.department_id = d.id LEFT JOIN employees a ON l.approver_id = a.id LEFT JOIN employees s ON l.substitute_id = s.id WHERE l.status = 'pending' ORDER BY l.created_at DESC """)
```

### 72. app_backup_before_fix.py:2315
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT l.id, l.employee_id, e.name as employee_name, l.leave_type, l.start_date, l.end_date, l.status FROM leaves l JOIN employees e ON l.employee_id = e.id WHERE l.id = ? """, (leave_id,))
```

### 73. app_backup_before_fix.py:2332
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE leaves SET status = ?, comment = ?, approver_id = ?, approved_at = ? WHERE id = ? """, (new_status, comment, approver_id, datetime.now(), leave_id))
```

### 74. app_backup_before_fix.py:2363
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT status, COUNT(*) as count, leave_type FROM leaves WHERE created_at >= DATE('now', '-30 days')
```

### 75. app_backup_before_fix.py:2393
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) FROM leaves
```

### 76. app_backup_before_fix.py:2445
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key as code, setting_value as name, description FROM system_settings WHERE category = 'employee_status' ORDER BY CASE setting_key WHEN 'active' THEN 1 WHEN 'trial' THEN 2 WHEN 'inactive' THEN 3 WHEN 'leave' THEN 4 ELSE 5 END """)
```

### 77. app_backup_before_fix.py:2513
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO education_levels (name, level_order, description)
```

### 78. app_backup_before_fix.py:2525
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO positions (name, department_id, level_order, salary_range_min, salary_range_max, description)
```

### 79. app_backup_before_fix.py:2540
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO leave_types (name, code, max_days_per_year, is_paid, requires_approval, advance_notice_days, description)
```

### 80. app_backup_before_fix.py:2555
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO salary_grades (name, grade_code, level_order, min_salary, max_salary, description)
```

### 81. app_backup_before_fix.py:2568
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO work_locations (name, address, city, country, timezone, is_remote, description)
```

### 82. app_backup_before_fix.py:2583
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO skills (name, category, description)
```

### 83. app_backup_before_fix.py:2595
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO clock_status_types (status_code, status_name, description, sort_order, is_active)
```

### 84. app_backup_before_fix.py:2669
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE education_levels SET name = ?, level_order = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data['level_order'], data.get('description', ''), item_id))
```

### 85. app_backup_before_fix.py:2676
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE positions SET name = ?, department_id = ?, level_order = ?, salary_range_min = ?, salary_range_max = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data.get('department_id'), data['level_order'], data.get('salary_range_min'), data.get('salary_range_max'), data.get('description', ''), item_id))
```

### 86. app_backup_before_fix.py:2686
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE leave_types SET name = ?, code = ?, max_days_per_year = ?, is_paid = ?, requires_approval = ?, advance_notice_days = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data['code'], data.get('max_days_per_year'), data.get('is_paid', 1), data.get('requires_approval', 1), data.get('advance_notice_days', 1), data.get('description', ''), item_id))
```

### 87. app_backup_before_fix.py:2697
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE salary_grades SET name = ?, grade_code = ?, level_order = ?, min_salary = ?, max_salary = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data['grade_code'], data['level_order'], data['min_salary'], data['max_salary'], data.get('description', ''), item_id))
```

### 88. app_backup_before_fix.py:2706
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE work_locations SET name = ?, address = ?, city = ?, country = ?, timezone = ?, is_remote = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data.get('address', ''), data.get('city', ''), data.get('country', '台灣'), data.get('timezone', 'Asia/Taipei'), data.get('is_remote', 0), data.get('description', ''), item_id))
```

### 89. app_backup_before_fix.py:2716
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE skills SET name = ?, category = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data.get('category', ''), data.get('description', ''), item_id))
```

### 90. app_backup_before_fix.py:2723
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE clock_status_types SET status_code = ?, status_name = ?, description = ?, sort_order = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['status_code'], data['status_name'], data.get('description', ''), data.get('sort_order', 0), data.get('is_active', 1), item_id))
```

### 91. app_backup_before_fix.py:2777
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT status_code, status_name, description, sort_order FROM clock_status_types WHERE is_active = 1 ORDER BY sort_order, status_code """)
```

### 92. app_backup_before_fix.py:2814
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT 1")
```

### 93. app_backup_before_fix.py:2905
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) FROM attendance
```

### 94. app_backup_before_fix.py:2912
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM employees WHERE status = 'active'")
```

### 95. app_backup_before_fix.py:2917
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) FROM leaves
```

### 96. app_backup_before_fix.py:2924
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) FROM leaves
```

### 97. app_backup_before_fix.py:3002
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 98. app_backup_before_fix.py:3055
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, name FROM employees WHERE id = ?", (data['employee_id'],))
```

### 99. app_backup_before_fix.py:3063
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, name FROM employees WHERE id = ?", (substitute_id,))
```

### 100. app_backup_before_fix.py:3071
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, name FROM employees WHERE id = ?", (approver_id,))
```

### 101. app_backup_before_fix.py:3077
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" INSERT INTO leaves ( employee_id, leave_type, start_date, end_date, reason, substitute_id, approver_id, status, created_at ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?)
```

### 102. app_backup_before_fix.py:3111
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" SELECT l.id, l.employee_id, e.name as employee_name, e.employee_id as emp_id, d.name as department_name, l.leave_type, l.start_date, l.end_date, l.reason, l.status, l.created_at, l.approved_at, l.comment, l.approver_id, a.name as approver_name, l.substitute_id, s.name as substitute_name, e.photo_url FROM leaves l JOIN employees e ON l.employee_id = e.id LEFT JOIN departments d ON e.department_id = d.id LEFT JOIN employees a ON l.approver_id = a.id LEFT JOIN employees s ON l.substitute_id = s.id WHERE l.id = ? """, (leave_id,))
```

### 103. app_backup_before_fix.py:3158
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT status, employee_id FROM leaves WHERE id = ?", (leave_id,))
```

### 104. app_backup_before_fix.py:3227
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT status FROM leaves WHERE id = ? """, (leave_id,), )
```

### 105. app_backup_before_fix.py:3242
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute( """ DELETE FROM leaves WHERE id = ? """, (leave_id,), )
```

### 106. app_backup_before_fix.py:3268
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT id, name, code, start_time, end_time, break_start_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime, color_code, description, is_active, created_at, updated_at FROM shifts WHERE is_active = 1 ORDER BY name """ )
```

### 107. app_backup_before_fix.py:3349
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM shifts WHERE code = ?", (data["code"],))
```

### 108. app_backup_before_fix.py:3354
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ INSERT INTO shifts ( name, code, start_time, end_time, break_start_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime, color_code, description ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 109. app_backup_before_fix.py:3400
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute( """ SELECT id, name, code, start_time, end_time, break_start_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime, color_code, description, is_active, created_at, updated_at FROM shifts WHERE id = ? """, (shift_id,), )
```

### 110. app_backup_before_fix.py:3483
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM shifts WHERE id = ?", (shift_id,))
```

### 111. app_backup_before_fix.py:3488
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( "SELECT id FROM shifts WHERE code = ? AND id != ?", (data["code"], shift_id)
```

### 112. app_backup_before_fix.py:3496
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE shifts SET name = ?, code = ?, start_time = ?, end_time = ?, break_start_time = ?, break_duration_minutes = ?, pre_overtime_threshold_minutes = ?, post_overtime_threshold_minutes = ?, enable_pre_overtime = ?, enable_post_overtime = ?, auto_calculate_overtime = ?, color_code = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, ( data["name"], data["code"], data["start_time"], data["end_time"], data.get("break_start_time"), data.get("break_duration_minutes", 60), data.get("pre_overtime_threshold_minutes", 0), data.get("post_overtime_threshold_minutes", 0), data.get("enable_pre_overtime", False), data.get("enable_post_overtime", False), data.get("auto_calculate_overtime", True), data.get("color_code", "#3B82F6"), data.get("description", ""), shift_id ), )
```

### 113. app_backup_before_fix.py:3544
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, is_active FROM shifts WHERE id = ?", (shift_id,))
```

### 114. app_backup_before_fix.py:3553
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE shifts SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (shift_id,), )
```

### 115. app_backup_before_fix.py:3605
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT start_time, end_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime FROM shifts WHERE id = ? AND is_active = 1 """, (data["shift_id"],), )
```

### 116. app_backup_before_fix.py:3710
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value FROM system_settings WHERE category = 'overtime_calculation' """)
```

### 117. app_backup_before_fix.py:3737
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 118. app_backup_before_fix.py:3827
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT start_time, end_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime FROM shifts WHERE id = ? AND is_active = 1 """, (data["shift_id"],))
```

### 119. app_backup_before_fix.py:3847
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value FROM system_settings WHERE category = 'overtime_calculation' """)
```

### 120. app_backup_before_fix.py:3957
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value FROM system_settings WHERE category = 'overtime_calculation' """)
```

### 121. app_backup_before_fix.py:4011
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params_with_date)
```

### 122. app_backup_before_fix.py:4030
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_query, leave_params)
```

### 123. app_backup_before_fix.py:4044
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params_with_date)
```

### 124. app_backup_before_fix.py:4258
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 125. app_backup_before_fix.py:4358
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ? AND status = 'active'", (data['employee_id'],))
```

### 126. app_backup_before_fix.py:4364
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM shifts WHERE id = ?", (data['shift_id'],))
```

### 127. app_backup_before_fix.py:4369
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM schedules WHERE employee_id = ? AND shift_date = ? AND status != 'cancelled' """, (data['employee_id'], data['shift_date']))
```

### 128. app_backup_before_fix.py:4378
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO schedules (employee_id, shift_date, shift_id, note, status, created_at)
```

### 129. app_backup_before_fix.py:4393
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.*, e.name as employee_name, sh.name as shift_name FROM schedules s LEFT JOIN employees e ON s.employee_id = e.id LEFT JOIN shifts sh ON s.shift_id = sh.id WHERE s.id = ? """, (schedule_id,))
```

### 130. app_backup_before_fix.py:4425
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT * FROM schedules WHERE id = ?", (schedule_id,))
```

### 131. app_backup_before_fix.py:4453
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.*, e.name as employee_name, sh.name as shift_name FROM schedules s LEFT JOIN employees e ON s.employee_id = e.id LEFT JOIN shifts sh ON s.shift_id = sh.id WHERE s.id = ? """, (schedule_id,))
```

### 132. app_backup_before_fix.py:4483
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT * FROM schedules WHERE id = ?", (schedule_id,))
```

### 133. app_backup_before_fix.py:4488
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE schedules SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (schedule_id,))
```

### 134. app_backup_before_fix.py:4558
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, query_params)
```

### 135. app_backup_before_fix.py:4596
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_schedules, COUNT(DISTINCT employee_id) as total_employees
```

### 136. app_backup_before_fix.py:4606
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT sh.name, COUNT(*) as count
```

### 137. app_backup_before_fix.py:4618
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.name, COUNT(*) as count
```

### 138. app_backup_before_fix.py:4792
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 139. app_backup_before_fix.py:4852
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(raw_records_query, (record['id'],))
```

### 140. app_backup_before_fix.py:4893
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 141. app_backup_before_fix.py:5065
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 142. app_backup_before_fix.py:5297
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, query_params)
```

### 143. app_backup_before_fix.py:5659
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 144. app_backup_before_fix.py:5805
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 145. app_backup_before_fix.py:5978
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id, name, employee_id FROM employees WHERE status = 'active'")
```

### 146. app_backup_before_fix.py:5996
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance WHERE employee_id = ? AND DATE(COALESCE(check_in, check_out)) = ?
```

### 147. app_backup_before_fix.py:6009
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.id, s.shift_id, sh.name, sh.start_time, sh.end_time, sh.break_duration_minutes FROM schedules s JOIN shifts sh ON s.shift_id = sh.id WHERE s.employee_id = ? AND s.shift_date = ? """, (emp_id, date_str))
```

### 148. app_backup_before_fix.py:6031
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET status = 'normal', note = '系統重新計算', updated_at = ? WHERE id = ? """, (datetime.now(), existing_record[0]))
```

### 149. app_backup_before_fix.py:6043
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance (employee_id, check_in, check_out, status, note, created_at)
```

### 150. app_backup_before_fix.py:6130
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(DISTINCT DATE(COALESCE(check_in, check_out))) as pending_days
```

### 151. app_backup_before_fix.py:6138
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(DISTINCT employee_id) as pending_employees
```

### 152. app_backup_before_fix.py:6146
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as anomaly_records
```

### 153. app_backup_before_fix.py:6155
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT MAX(created_at) as last_processed
```

### 154. app_backup_before_fix.py:6331
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 155. app_backup_before_fix.py:6353
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, query_params)
```

### 156. app_backup_before_fix.py:6382
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET status = ?, updated_at = ? WHERE id = ? """, (new_status, datetime.now(), record_id))
```

### 157. app_backup_before_fix.py:6538
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id, employee_id, card_number FROM employees WHERE status = 'active'")
```

### 158. app_backup_before_fix.py:6686
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT MAX(CAST(SUBSTR(employee_id, 2) AS INTEGER)) FROM employees WHERE employee_id LIKE 'E%'")
```

### 159. app_backup_before_fix.py:6692
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id FROM departments LIMIT 1")
```

### 160. app_backup_before_fix.py:6696
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO departments (name, description, created_at)
```

### 161. app_backup_before_fix.py:6705
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO employees (employee_id, name, card_number, department_id, position, status, created_at)
```

### 162. app_backup_before_fix.py:6725
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(check_query, (employee_id, record['date']))
```

### 163. app_backup_before_fix.py:6749
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET check_in = ?, check_out = ?, status = ?, device_id = ?, note = ? WHERE id = ? """, (record['check_in'], record['check_out'], status, record['device_id'], note, existing[0]))
```

### 164. app_backup_before_fix.py:6757
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute("DELETE FROM clock_raw_records WHERE attendance_id = ?", (attendance_id,))
```

### 165. app_backup_before_fix.py:6760
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance (employee_id, check_in, check_out, status, device_id, note)
```

### 166. app_backup_before_fix.py:6770
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO clock_raw_records (employee_code, machine_code, timestamp, status_code, attendance_id)
```

### 167. app_backup_before_fix.py:6904
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 168. app_backup_before_fix.py:7027
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT a.id, a.employee_id, DATE(a.check_in) as work_date, a.check_in, a.check_out, e.name as employee_name FROM attendance a JOIN employees e ON a.employee_id = e.id WHERE a.id = ? """, (attendance_id,))
```

### 169. app_backup_before_fix.py:7041
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id, name, start_time, end_time, break_start, break_end FROM shifts WHERE id = ? """, (shift_id,))
```

### 170. app_backup_before_fix.py:7053
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO schedules (employee_id, work_date, shift_id, created_at)
```

### 171. app_backup_before_fix.py:7103
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET late_minutes = ?, early_leave_minutes = ?, overtime_minutes = ?, status = ?, updated_at = datetime('now')
```

### 172. update_employee_shifts.py:33
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id, name, position FROM employees ORDER BY id")
```

### 173. update_employee_shifts.py:82
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( "UPDATE employees SET shift_type = ? WHERE id = ?", (shift_id, emp_id)
```

### 174. update_employee_shifts.py:100
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT s.name, COUNT(e.id) as count, ROUND(COUNT(e.id) * 100.0 / (SELECT COUNT(*) FROM employees), 1) as percentage
```

### 175. process_full_june.py:52
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_unprocessed, MIN(punch_date) as earliest_date, MAX(punch_date) as latest_date
```

### 176. process_full_june.py:104
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_records, MIN(work_date) as earliest_date, MAX(work_date) as latest_date
```

### 177. app_backup.py:203
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT COUNT(*) as total, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late, SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave, SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent, SUM(CASE WHEN status = 'manual' THEN 1 ELSE 0 END) as manual
```

### 178. app_backup.py:226
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT COUNT(*) FROM leaves WHERE status = 'pending'
```

### 179. app_backup.py:235
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT d.name, COUNT(e.id)
```

### 180. app_backup.py:246
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status FROM attendance a JOIN employees e ON a.employee_id = e.id ORDER BY a.check_in DESC LIMIT 5 """ )
```

### 181. app_backup.py:291
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_checkins, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal_count, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count, SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave_count, SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
```

### 182. app_backup.py:306
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as week_total
```

### 183. app_backup.py:315
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as pending_leaves
```

### 184. app_backup.py:324
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.name, COUNT(a.id) as total_attendance, SUM(CASE WHEN a.status = 'normal' THEN 1 ELSE 0 END) as normal_count
```

### 185. app_backup.py:345
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT DATE(check_in) as date, COUNT(*) as total, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late
```

### 186. app_backup.py:418
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO attendance (employee_id, check_in, status)
```

### 187. app_backup.py:449
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO attendance (employee_id, check_in, check_out, status, note)
```

### 188. app_backup.py:490
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name, employee_id FROM employees WHERE id = ?", (employee_id,))
```

### 189. app_backup.py:497
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT id, check_in, check_out, status, note FROM attendance WHERE employee_id = ? AND DATE(check_in) = ?
```

### 190. app_backup.py:577
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name FROM employees WHERE id = ?", (employee_id,))
```

### 191. app_backup.py:621
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status, d.name as department_name FROM attendance a JOIN employees e ON a.employee_id = e.id LEFT JOIN departments d ON e.department_id = d.id ORDER BY a.check_in DESC LIMIT ? """, (limit,))
```

### 192. app_backup.py:690
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT rule_type, rule_value, description FROM schedule_rules WHERE rule_type IN ( 'day_change_time', 'cross_day_attendance', 'first_punch_as_checkin', 'last_punch_as_checkout', 'late_tolerance_minutes', 'early_leave_tolerance_minutes' )
```

### 193. app_backup.py:729
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE schedule_rules SET rule_value = ? WHERE rule_type = ? """, (str(rule_value), rule_type))
```

### 194. app_backup.py:737
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO schedule_rules (rule_type, rule_value, description)
```

### 195. app_backup.py:795
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO schedules (employee_id, shift_date, shift_type, start_time, end_time)
```

### 196. app_backup.py:827
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT * FROM schedule_rules")
```

### 197. app_backup.py:833
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO schedule_rules (rule_type, rule_value, description)
```

### 198. app_backup.py:1059
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 199. app_backup.py:1138
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM departments WHERE id = ?", (data['department_id'],))
```

### 200. app_backup.py:1143
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM permissions WHERE id = ?", (data['role_id'],))
```

### 201. app_backup.py:1148
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (data['manager_id'],))
```

### 202. app_backup.py:1153
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (data['employee_id'],))
```

### 203. app_backup.py:1158
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO employees (name, employee_id, department_id, position, email, phone, role_id, manager_id, password, hire_date, status, salary_level, id_number, address, emergency_contact, emergency_phone, photo_url, shift_type)
```

### 204. app_backup.py:1217
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT e.id, e.name, e.employee_id, e.department_id, d.name as department_name, e.position, e.email, e.phone, e.password, e.hire_date, e.status, e.salary_level, e.id_number, e.address, e.emergency_contact, e.emergency_phone, e.role_id, p.role_name, e.manager_id, m.name as manager_name, e.shift_type FROM employees e JOIN departments d ON e.department_id = d.id LEFT JOIN permissions p ON e.role_id = p.id LEFT JOIN employees m ON e.manager_id = m.id WHERE e.id = ? """, (employee_id,), )
```

### 205. app_backup.py:1259
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM departments WHERE id = ?", (data["department_id"],))
```

### 206. app_backup.py:1264
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM permissions WHERE id = ?", (data["role_id"],))
```

### 207. app_backup.py:1269
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (data["manager_id"],))
```

### 208. app_backup.py:1337
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute("DELETE FROM employees WHERE id = ?", (employee_id,))
```

### 209. app_backup.py:1360
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name, p.role_name, e.department_id FROM employees e JOIN departments d ON e.department_id = d.id LEFT JOIN permissions p ON e.role_id = p.id WHERE e.role_id <= 2 OR e.id IN ( SELECT DISTINCT manager_id FROM employees WHERE manager_id IS NOT NULL )
```

### 210. app_backup.py:1395
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT department_id FROM employees WHERE id = ? """, (employee_id,))
```

### 211. app_backup.py:1406
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name FROM employees e JOIN departments d ON e.department_id = d.id WHERE e.id != ? AND ( e.department_id = ? OR e.role_id <= 2 )
```

### 212. app_backup.py:1444
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT d.id, d.name, d.manager_id, e.name as manager_name, d.description FROM departments d LEFT JOIN employees e ON d.manager_id = e.id """ )
```

### 213. app_backup.py:1463
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO departments (name, manager_id, description)
```

### 214. app_backup.py:1489
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT * FROM departments WHERE id = ?", (dept_id,))
```

### 215. app_backup.py:1506
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE leaves SET status = ?, comment = ?, approved_at = ? WHERE id = ? """, (status, comment, datetime.now(), dept_id))
```

### 216. app_backup.py:1521
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT status FROM leaves WHERE id = ?", (dept_id,))
```

### 217. app_backup.py:1530
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE leaves SET leave_type = ?, start_date = ?, end_date = ?, reason = ? WHERE id = ? """, ( data.get("leave_type"), data.get("start_date"), data.get("end_date"), data.get("reason"), dept_id ))
```

### 218. app_backup.py:1550
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute("DELETE FROM leaves WHERE id = ?", (dept_id,))
```

### 219. app_backup.py:1578
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT * FROM schedules WHERE employee_id = ? AND shift_date BETWEEN ? AND ? ORDER BY shift_date, start_time """, (employee_id, start_date, end_date))
```

### 220. app_backup.py:1612
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT strftime('%w', check_in) as day_of_week, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late, SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent, COUNT(*) as total
```

### 221. app_backup.py:1684
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.name as department, COUNT(a.id) as total_records, SUM(CASE WHEN a.status = 'normal' THEN 1 ELSE 0 END) as normal_count
```

### 222. app_backup.py:1732
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT CASE WHEN strftime('%H:%M', check_in) BETWEEN '08:00' AND '08:30' THEN '8:00-8:30'
```

### 223. app_backup.py:1783
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT leave_type, COUNT(*) as count
```

### 224. app_backup.py:1827
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as on_time
```

### 225. app_backup.py:1841
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_checkouts, SUM(CASE WHEN strftime('%H', check_out) >= '18' THEN 1 ELSE 0 END) as overtime_count
```

### 226. app_backup.py:1856
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(DISTINCT e.id) as total_employees, COUNT(DISTINCT e.id) - COUNT(DISTINCT CASE WHEN a.status = 'absent' THEN e.id END) as full_attendance_employees
```

### 227. app_backup.py:1910
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT * FROM system_settings ORDER BY category, setting_key")
```

### 228. app_backup.py:1930
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 229. app_backup.py:1954
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value FROM system_settings WHERE category = 'attendance_rules' """)
```

### 230. app_backup.py:1983
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 231. app_backup.py:2007
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value FROM system_settings WHERE category = 'notifications' """)
```

### 232. app_backup.py:2035
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 233. app_backup.py:2060
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT p.id, p.role_name, p.permission_level, p.description, COUNT(e.id) as user_count
```

### 234. app_backup.py:2082
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO permissions (role_name, permission_level, description)
```

### 235. app_backup.py:2113
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT d.id, d.name, d.manager_id, e.name as manager_name FROM departments d LEFT JOIN employees e ON d.manager_id = e.id """ )
```

### 236. app_backup.py:2133
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE departments SET manager_id = ? WHERE id = ? """, (manager_id, dept_id), )
```

### 237. app_backup.py:2158
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.id, d.name, COUNT(e.id) as total_employees, SUM(CASE WHEN e.status = 'active' THEN 1 ELSE 0 END) as active_employees, SUM(CASE WHEN e.status = 'trial' THEN 1 ELSE 0 END) as trial_employees
```

### 238. app_backup.py:2196
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_checkins, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal_count, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count, SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave_count, SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
```

### 239. app_backup.py:2211
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as week_total
```

### 240. app_backup.py:2220
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as pending_leaves
```

### 241. app_backup.py:2260
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT l.id, l.employee_id, e.name as employee_name, e.employee_id as emp_id, e.position as employee_position, d.name as department_name, l.leave_type, l.start_date, l.end_date, l.reason, l.created_at, l.status, l.approver_id, a.name as approver_name, a.position as approver_position, l.substitute_id, s.name as substitute_name, s.position as substitute_position, e.photo_url FROM leaves l JOIN employees e ON l.employee_id = e.id LEFT JOIN departments d ON e.department_id = d.id LEFT JOIN employees a ON l.approver_id = a.id LEFT JOIN employees s ON l.substitute_id = s.id WHERE l.status = 'pending' ORDER BY l.created_at DESC """)
```

### 242. app_backup.py:2315
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT l.id, l.employee_id, e.name as employee_name, l.leave_type, l.start_date, l.end_date, l.status FROM leaves l JOIN employees e ON l.employee_id = e.id WHERE l.id = ? """, (leave_id,))
```

### 243. app_backup.py:2332
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE leaves SET status = ?, comment = ?, approver_id = ?, approved_at = ? WHERE id = ? """, (new_status, comment, approver_id, datetime.now(), leave_id))
```

### 244. app_backup.py:2363
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT status, COUNT(*) as count, leave_type FROM leaves WHERE created_at >= DATE('now', '-30 days')
```

### 245. app_backup.py:2393
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) FROM leaves
```

### 246. app_backup.py:2445
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key as code, setting_value as name, description FROM system_settings WHERE category = 'employee_status' ORDER BY CASE setting_key WHEN 'active' THEN 1 WHEN 'trial' THEN 2 WHEN 'inactive' THEN 3 WHEN 'leave' THEN 4 ELSE 5 END """)
```

### 247. app_backup.py:2513
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO education_levels (name, level_order, description)
```

### 248. app_backup.py:2525
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO positions (name, department_id, level_order, salary_range_min, salary_range_max, description)
```

### 249. app_backup.py:2540
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO leave_types (name, code, max_days_per_year, is_paid, requires_approval, advance_notice_days, description)
```

### 250. app_backup.py:2555
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO salary_grades (name, grade_code, level_order, min_salary, max_salary, description)
```

### 251. app_backup.py:2568
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO work_locations (name, address, city, country, timezone, is_remote, description)
```

### 252. app_backup.py:2583
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO skills (name, category, description)
```

### 253. app_backup.py:2595
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO clock_status_types (status_code, status_name, description, sort_order, is_active)
```

### 254. app_backup.py:2669
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE education_levels SET name = ?, level_order = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data['level_order'], data.get('description', ''), item_id))
```

### 255. app_backup.py:2676
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE positions SET name = ?, department_id = ?, level_order = ?, salary_range_min = ?, salary_range_max = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data.get('department_id'), data['level_order'], data.get('salary_range_min'), data.get('salary_range_max'), data.get('description', ''), item_id))
```

### 256. app_backup.py:2686
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE leave_types SET name = ?, code = ?, max_days_per_year = ?, is_paid = ?, requires_approval = ?, advance_notice_days = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data['code'], data.get('max_days_per_year'), data.get('is_paid', 1), data.get('requires_approval', 1), data.get('advance_notice_days', 1), data.get('description', ''), item_id))
```

### 257. app_backup.py:2697
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE salary_grades SET name = ?, grade_code = ?, level_order = ?, min_salary = ?, max_salary = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data['grade_code'], data['level_order'], data['min_salary'], data['max_salary'], data.get('description', ''), item_id))
```

### 258. app_backup.py:2706
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE work_locations SET name = ?, address = ?, city = ?, country = ?, timezone = ?, is_remote = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data.get('address', ''), data.get('city', ''), data.get('country', '台灣'), data.get('timezone', 'Asia/Taipei'), data.get('is_remote', 0), data.get('description', ''), item_id))
```

### 259. app_backup.py:2716
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE skills SET name = ?, category = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['name'], data.get('category', ''), data.get('description', ''), item_id))
```

### 260. app_backup.py:2723
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE clock_status_types SET status_code = ?, status_name = ?, description = ?, sort_order = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (data['status_code'], data['status_name'], data.get('description', ''), data.get('sort_order', 0), data.get('is_active', 1), item_id))
```

### 261. app_backup.py:2777
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT status_code, status_name, description, sort_order FROM clock_status_types WHERE is_active = 1 ORDER BY sort_order, status_code """)
```

### 262. app_backup.py:2814
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT 1")
```

### 263. app_backup.py:2905
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) FROM attendance
```

### 264. app_backup.py:2912
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM employees WHERE status = 'active'")
```

### 265. app_backup.py:2917
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) FROM leaves
```

### 266. app_backup.py:2924
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) FROM leaves
```

### 267. app_backup.py:3002
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 268. app_backup.py:3055
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, name FROM employees WHERE id = ?", (data['employee_id'],))
```

### 269. app_backup.py:3063
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, name FROM employees WHERE id = ?", (substitute_id,))
```

### 270. app_backup.py:3071
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, name FROM employees WHERE id = ?", (approver_id,))
```

### 271. app_backup.py:3077
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" INSERT INTO leaves ( employee_id, leave_type, start_date, end_date, reason, substitute_id, approver_id, status, created_at ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?)
```

### 272. app_backup.py:3111
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" SELECT l.id, l.employee_id, e.name as employee_name, e.employee_id as emp_id, d.name as department_name, l.leave_type, l.start_date, l.end_date, l.reason, l.status, l.created_at, l.approved_at, l.comment, l.approver_id, a.name as approver_name, l.substitute_id, s.name as substitute_name, e.photo_url FROM leaves l JOIN employees e ON l.employee_id = e.id LEFT JOIN departments d ON e.department_id = d.id LEFT JOIN employees a ON l.approver_id = a.id LEFT JOIN employees s ON l.substitute_id = s.id WHERE l.id = ? """, (leave_id,))
```

### 273. app_backup.py:3158
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT status, employee_id FROM leaves WHERE id = ?", (leave_id,))
```

### 274. app_backup.py:3227
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT status FROM leaves WHERE id = ? """, (leave_id,), )
```

### 275. app_backup.py:3242
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute( """ DELETE FROM leaves WHERE id = ? """, (leave_id,), )
```

### 276. app_backup.py:3268
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT id, name, code, start_time, end_time, break_start_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime, color_code, description, is_active, created_at, updated_at FROM shifts WHERE is_active = 1 ORDER BY name """ )
```

### 277. app_backup.py:3349
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM shifts WHERE code = ?", (data["code"],))
```

### 278. app_backup.py:3354
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ INSERT INTO shifts ( name, code, start_time, end_time, break_start_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime, color_code, description ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 279. app_backup.py:3400
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute( """ SELECT id, name, code, start_time, end_time, break_start_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime, color_code, description, is_active, created_at, updated_at FROM shifts WHERE id = ? """, (shift_id,), )
```

### 280. app_backup.py:3483
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM shifts WHERE id = ?", (shift_id,))
```

### 281. app_backup.py:3488
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( "SELECT id FROM shifts WHERE code = ? AND id != ?", (data["code"], shift_id)
```

### 282. app_backup.py:3496
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE shifts SET name = ?, code = ?, start_time = ?, end_time = ?, break_start_time = ?, break_duration_minutes = ?, pre_overtime_threshold_minutes = ?, post_overtime_threshold_minutes = ?, enable_pre_overtime = ?, enable_post_overtime = ?, auto_calculate_overtime = ?, color_code = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, ( data["name"], data["code"], data["start_time"], data["end_time"], data.get("break_start_time"), data.get("break_duration_minutes", 60), data.get("pre_overtime_threshold_minutes", 0), data.get("post_overtime_threshold_minutes", 0), data.get("enable_pre_overtime", False), data.get("enable_post_overtime", False), data.get("auto_calculate_overtime", True), data.get("color_code", "#3B82F6"), data.get("description", ""), shift_id ), )
```

### 283. app_backup.py:3544
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, is_active FROM shifts WHERE id = ?", (shift_id,))
```

### 284. app_backup.py:3553
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE shifts SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (shift_id,), )
```

### 285. app_backup.py:3605
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT start_time, end_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime FROM shifts WHERE id = ? AND is_active = 1 """, (data["shift_id"],), )
```

### 286. app_backup.py:3710
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value FROM system_settings WHERE category = 'overtime_calculation' """)
```

### 287. app_backup.py:3737
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 288. app_backup.py:3827
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT start_time, end_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime FROM shifts WHERE id = ? AND is_active = 1 """, (data["shift_id"],))
```

### 289. app_backup.py:3847
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value FROM system_settings WHERE category = 'overtime_calculation' """)
```

### 290. app_backup.py:3957
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value FROM system_settings WHERE category = 'overtime_calculation' """)
```

### 291. app_backup.py:4011
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params_with_date)
```

### 292. app_backup.py:4030
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(leave_query, leave_params)
```

### 293. app_backup.py:4044
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params_with_date)
```

### 294. app_backup.py:4258
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 295. app_backup.py:4358
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ? AND status = 'active'", (data['employee_id'],))
```

### 296. app_backup.py:4364
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM shifts WHERE id = ?", (data['shift_id'],))
```

### 297. app_backup.py:4369
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM schedules WHERE employee_id = ? AND shift_date = ? AND status != 'cancelled' """, (data['employee_id'], data['shift_date']))
```

### 298. app_backup.py:4378
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO schedules (employee_id, shift_date, shift_id, note, status, created_at)
```

### 299. app_backup.py:4393
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.*, e.name as employee_name, sh.name as shift_name FROM schedules s LEFT JOIN employees e ON s.employee_id = e.id LEFT JOIN shifts sh ON s.shift_id = sh.id WHERE s.id = ? """, (schedule_id,))
```

### 300. app_backup.py:4425
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT * FROM schedules WHERE id = ?", (schedule_id,))
```

### 301. app_backup.py:4453
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.*, e.name as employee_name, sh.name as shift_name FROM schedules s LEFT JOIN employees e ON s.employee_id = e.id LEFT JOIN shifts sh ON s.shift_id = sh.id WHERE s.id = ? """, (schedule_id,))
```

### 302. app_backup.py:4483
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT * FROM schedules WHERE id = ?", (schedule_id,))
```

### 303. app_backup.py:4488
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE schedules SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (schedule_id,))
```

### 304. app_backup.py:4558
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, query_params)
```

### 305. app_backup.py:4596
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_schedules, COUNT(DISTINCT employee_id) as total_employees
```

### 306. app_backup.py:4606
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT sh.name, COUNT(*) as count
```

### 307. app_backup.py:4618
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.name, COUNT(*) as count
```

### 308. app_backup.py:4792
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 309. app_backup.py:4852
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(raw_records_query, (record['id'],))
```

### 310. app_backup.py:4893
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 311. app_backup.py:5065
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 312. app_backup.py:5297
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, query_params)
```

### 313. app_backup.py:5659
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 314. app_backup.py:5805
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 315. app_backup.py:5978
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id, name, employee_id FROM employees WHERE status = 'active'")
```

### 316. app_backup.py:5996
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance WHERE employee_id = ? AND DATE(COALESCE(check_in, check_out)) = ?
```

### 317. app_backup.py:6009
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.id, s.shift_id, sh.name, sh.start_time, sh.end_time, sh.break_duration_minutes FROM schedules s JOIN shifts sh ON s.shift_id = sh.id WHERE s.employee_id = ? AND s.shift_date = ? """, (emp_id, date_str))
```

### 318. app_backup.py:6031
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET status = 'normal', note = '系統重新計算', updated_at = ? WHERE id = ? """, (datetime.now(), existing_record[0]))
```

### 319. app_backup.py:6043
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance (employee_id, check_in, check_out, status, note, created_at)
```

### 320. app_backup.py:6130
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(DISTINCT DATE(COALESCE(check_in, check_out))) as pending_days
```

### 321. app_backup.py:6138
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(DISTINCT employee_id) as pending_employees
```

### 322. app_backup.py:6146
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as anomaly_records
```

### 323. app_backup.py:6155
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT MAX(created_at) as last_processed
```

### 324. app_backup.py:6331
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 325. app_backup.py:6353
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, query_params)
```

### 326. app_backup.py:6382
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET status = ?, updated_at = ? WHERE id = ? """, (new_status, datetime.now(), record_id))
```

### 327. app_backup.py:6538
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id, employee_id, card_number FROM employees WHERE status = 'active'")
```

### 328. app_backup.py:6686
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT MAX(CAST(SUBSTR(employee_id, 2) AS INTEGER)) FROM employees WHERE employee_id LIKE 'E%'")
```

### 329. app_backup.py:6692
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id FROM departments LIMIT 1")
```

### 330. app_backup.py:6696
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO departments (name, description, created_at)
```

### 331. app_backup.py:6705
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO employees (employee_id, name, card_number, department_id, position, status, created_at)
```

### 332. app_backup.py:6725
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(check_query, (employee_id, record['date']))
```

### 333. app_backup.py:6749
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET check_in = ?, check_out = ?, status = ?, device_id = ?, note = ? WHERE id = ? """, (record['check_in'], record['check_out'], status, record['device_id'], note, existing[0]))
```

### 334. app_backup.py:6757
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute("DELETE FROM clock_raw_records WHERE attendance_id = ?", (attendance_id,))
```

### 335. app_backup.py:6760
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance (employee_id, check_in, check_out, status, device_id, note)
```

### 336. app_backup.py:6770
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO clock_raw_records (employee_code, machine_code, timestamp, status_code, attendance_id)
```

### 337. app_backup.py:6904
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 338. add_api.py:68
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT a.id, a.employee_id, a.work_date, a.check_in, a.check_out, e.name as employee_name FROM attendance a JOIN employees e ON a.employee_id = e.id WHERE a.id = ? """, (attendance_id,))
```

### 339. add_api.py:82
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id, name, start_time, end_time, break_start, break_end FROM shifts WHERE id = ? """, (shift_id,))
```

### 340. add_api.py:94
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO schedules (employee_id, work_date, shift_id, created_at)
```

### 341. add_api.py:144
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET late_minutes = ?, early_leave_minutes = ?, overtime_minutes = ?, status = ?, updated_at = datetime('now')
```

### 342. attendance_settings.py:74
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT rule_type, rule_value FROM schedule_rules WHERE rule_type IN ( 'late_tolerance_minutes', 'early_leave_tolerance_minutes', 'overtime_minimum_hours', 'day_change_time', 'cross_day_attendance', 'first_punch_as_checkin', 'last_punch_as_checkout', 'auto_clock_out_hours', 'break_time_deduction', 'weekend_overtime_auto' )
```

### 343. attendance.py:81
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT id, check_in, check_out FROM attendance WHERE employee_id = ? AND DATE(check_in) = ?
```

### 344. attendance.py:93
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE attendance SET check_out = ?, device_id = ?, note = ? WHERE id = ? """, (timestamp, device_type, note, existing_record[0]), )
```

### 345. attendance.py:105
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO attendance (employee_id, check_in, status, device_id, note)
```

### 346. attendance.py:193
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 347. attendance.py:250
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT COUNT(*) as total_days, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal_days, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_days, SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave_days, SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days
```

### 348. generate_june_punch_records.py:89
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT id, name, employee_id, department_id FROM employees WHERE status = 'active' OR status IS NULL ORDER BY employee_id """)
```

### 349. generate_june_punch_records.py:423
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM punch_records WHERE punch_date BETWEEN '2025-06-01' AND '2025-06-30'")
```

### 350. generate_june_punch_records.py:427
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT employee_id, COUNT(*) as record_count
```

### 351. generate_june_punch_records.py:437
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT device_id, COUNT(*) as record_count
```

### 352. generate_june_punch_records.py:458
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT punch_date, COUNT(*) as daily_count
```

### 353. db_schema_check.py:33
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
```

### 354. update_employee_schema.py:38
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(sql)
```

### 355. update_employee_schema.py:47
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" UPDATE employees SET password = 'password123', status = 'active', hire_date = date('now', '-' || (id * 30) || ' days')
```

### 356. update_employee_schema.py:59
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("PRAGMA table_info(employees)")
```

### 357. fix_database_structure.py:30
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT * FROM schedules")
```

### 358. fix_database_structure.py:40
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT * FROM shifts")
```

### 359. fix_database_structure.py:87
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("PRAGMA table_info(schedules)")
```

### 360. fix_database_structure.py:94
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("PRAGMA table_info(shifts)")
```

### 361. fix_database_structure.py:117
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("PRAGMA table_info(schedules)")
```

### 362. fix_database_structure.py:124
**風險**: 包含危險關鍵字: ALTER, EXEC
```sql
cursor.execute("ALTER TABLE schedules RENAME TO schedules_old")
```

### 363. fix_database_structure.py:127
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" CREATE TABLE schedules ( id INTEGER PRIMARY KEY AUTOINCREMENT, employee_id INTEGER NOT NULL, shift_date DATE NOT NULL, shift_id INTEGER NOT NULL,  -- 關聯到班別表 status TEXT DEFAULT 'scheduled',  -- scheduled, completed, cancelled actual_start_time TIME,  -- 實際上班時間 actual_end_time TIME,    -- 實際下班時間 overtime_hours DECIMAL(4,2) DEFAULT 0,  -- 加班時數
```

### 364. fix_database_structure.py:150
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id FROM shifts WHERE code = 'STANDARD_DAY' LIMIT 1")
```

### 365. fix_database_structure.py:176
**風險**: 包含危險關鍵字: DROP, EXEC
```sql
cursor.execute("DROP TABLE schedules_old")
```

### 366. fix_database_structure.py:183
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute("CREATE INDEX IF NOT EXISTS idx_schedules_employee_date ON schedules(employee_id, shift_date)")
```

### 367. fix_database_structure.py:184
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute("CREATE INDEX IF NOT EXISTS idx_schedules_shift ON schedules(shift_id)")
```

### 368. fix_database_structure.py:185
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute("CREATE INDEX IF NOT EXISTS idx_schedules_date ON schedules(shift_date)")
```

### 369. fix_database_structure.py:206
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("PRAGMA table_info(schedules)")
```

### 370. fix_database_structure.py:216
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM schedules")
```

### 371. fix_database_structure.py:221
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) FROM schedules sc
```

### 372. fix_database_structure.py:233
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM shifts WHERE is_active = 1")
```

### 373. fix_database_structure.py:255
**風險**: 包含危險關鍵字: EXEC
```sql
old_sql = """            cursor.execute(\"\"\" SELECT s.start_time, s.end_time, s.break_duration_minutes, s.pre_overtime_threshold_minutes, s.post_overtime_threshold_minutes, s.enable_pre_overtime, s.enable_post_overtime FROM schedules sc JOIN shifts s ON sc.shift_id = s.id WHERE sc.employee_id = ? AND sc.shift_date = ? \"\"\", (employee_id, work_date))"""
```

### 374. fix_database_structure.py:264
**風險**: 包含危險關鍵字: EXEC
```sql
new_sql = """            cursor.execute(\"\"\" SELECT s.start_time, s.end_time, s.break_duration_minutes, s.pre_overtime_threshold_minutes, s.post_overtime_threshold_minutes, s.enable_pre_overtime, s.enable_post_overtime FROM schedules sc JOIN shifts s ON sc.shift_id = s.id WHERE sc.employee_id = ? AND sc.shift_date = ? \"\"\", (employee_id, work_date))"""
```

### 375. fix_database_structure.py:281
**風險**: 包含危險關鍵字: EXEC
```sql
"            # cursor.execute(\"\"\"", "            cursor.execute(\"\"\"" )
```

### 376. fix_database_structure.py:282
**風險**: 包含危險關鍵字: EXEC
```sql
"            cursor.execute(\"\"\"" )
```

### 377. system_check.py:62
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
```

### 378. system_check.py:78
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM employees")
```

### 379. system_check.py:82
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM attendance")
```

### 380. system_check.py:86
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM departments")
```

### 381. attendance_api_old.py:73
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name FROM employees WHERE id = ?", (employee_id,))
```

### 382. attendance_api_old.py:126
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status, d.name as department_name FROM attendance a JOIN employees e ON a.employee_id = e.id LEFT JOIN departments d ON e.department_id = d.id ORDER BY a.check_in DESC LIMIT ? """, (limit,))
```

### 383. attendance_api_old.py:251
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT ar.*, e.name as employee_name, e.employee_id FROM attendance_records ar JOIN employees e ON ar.employee_id = e.id WHERE ar.id = ? """, (attendance_id,))
```

### 384. attendance_api_old.py:279
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("UPDATE attendance_records SET work_date = ? WHERE id = ?", (work_date, attendance_id))
```

### 385. attendance_api_old.py:284
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, name, start_time, end_time FROM shifts WHERE id = ?", (shift_id,))
```

### 386. attendance_api_old.py:293
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO schedules (employee_id, shift_date, shift_id, status, updated_at)
```

### 387. attendance_api_old.py:433
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance_records SET shift_id = ?, status = ?, late_minutes = ?, early_leave_minutes = ?, overtime_minutes = ?, work_hours = ?, overtime_hours = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (shift['id'], status, late_minutes, early_leave_minutes, overtime_minutes, work_hours, overtime_minutes / 60.0, attendance['id']))
```

### 388. attendance_api_old.py:502
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO attendance (employee_id, check_in, status)
```

### 389. attendance_api_old.py:550
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO attendance (employee_id, check_in, check_out, status, note)
```

### 390. attendance_api_old.py:604
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name, employee_id FROM employees WHERE id = ?", (employee_id,))
```

### 391. attendance_api_old.py:611
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT id, check_in, check_out, status, note FROM attendance WHERE employee_id = ? AND DATE(check_in) = ?
```

### 392. attendance_api_old.py:683
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT rule_type, rule_value, description FROM schedule_rules WHERE rule_type IN ( 'day_change_time', 'cross_day_attendance', 'first_punch_as_checkin', 'last_punch_as_checkout', 'late_tolerance_minutes', 'early_leave_tolerance_minutes' )
```

### 393. attendance_api_old.py:723
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE schedule_rules SET rule_value = ? WHERE rule_type = ? """, (str(rule_value), rule_type))
```

### 394. attendance_api_old.py:731
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO schedule_rules (rule_type, rule_value, description)
```

### 395. attendance_api_old.py:805
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 396. attendance_api_old.py:822
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(dept_query, params)
```

### 397. attendance_api_old.py:962
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 398. attendance_api_old.py:1037
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 399. attendance_api_old.py:1154
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(active_employees_query, dept_params)
```

### 400. attendance_api_old.py:1271
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" SELECT id FROM attendance_records WHERE employee_id = ? AND DATE(COALESCE(clock_in_time, clock_out_time, created_at)) = ?
```

### 401. attendance_api_old.py:1377
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance_records SET clock_in_time = ?, clock_out_time = ?, check_in = ?, check_out = ?, status = ?, note = ?, leave_hours = ?, work_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, ( check_in_time, check_out_time, check_in_time, check_out_time, attendance_status, combined_note, leave_hours, target_date, existing_record['id'] ))
```

### 402. attendance_api_old.py:1396
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance_records (employee_id, clock_in_time, clock_out_time, check_in, check_out, status, note, leave_hours, work_date, created_at)
```

### 403. attendance_api_old.py:1531
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(active_employees_query, dept_params)
```

### 404. attendance_api_old.py:1737
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_value FROM system_settings WHERE category = 'attendance_management' AND setting_key = 'last_attendance_process_date' """)
```

### 405. attendance_api_old.py:1819
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 406. attendance_api_old.py:1886
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 407. attendance_api_old.py:1915
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute(""" DELETE FROM attendance_records WHERE work_date = ? """, (target_date,))
```

### 408. attendance_api_old.py:1921
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT employee_id, punch_time, note FROM ( SELECT employee_id, check_in as punch_time, note FROM attendance WHERE DATE(check_in) = ? AND check_in IS NOT NULL
```

### 409. attendance_api_old.py:1938
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT lr.employee_id, lr.leave_type as leave_type_name, lr.reason, lr.start_date, lr.end_date, lr.leave_hours FROM leaves lr WHERE lr.status = 'approved' AND ? BETWEEN lr.start_date AND lr.end_date """, (target_date,))
```

### 410. attendance_api_old.py:1998
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance_records WHERE employee_id = ? AND work_date = ? """, (employee_id, target_date))
```

### 411. attendance_api_old.py:2106
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" INSERT INTO attendance_records ( employee_id, check_in, check_out, status, note, work_date, clock_in_time, clock_out_time, leave_hours ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 412. attendance_api_old.py:2188
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance_records SET check_in = ?, check_out = ?, status = ?, note = ?, work_date = ?, clock_in_time = ?, clock_out_time = ?, leave_hours = ?, updated_at = CURRENT_TIMESTAMP WHERE employee_id = ? AND (work_date = ? OR work_date IS NULL)
```

### 413. attendance_api_old.py:2215
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_value FROM system_settings WHERE category = 'attendance_management' AND setting_key = 'last_attendance_process_date' """)
```

### 414. attendance_api_old.py:2331
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 415. attendance_api_old.py:2391
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 416. attendance_api_old.py:2503
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 417. attendance_api_old.py:2523
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_query, (record['attendance_id'],))
```

### 418. attendance_api_old.py:2609
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(active_employees_query, dept_params)
```

### 419. attendance_api_old.py:2815
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_value FROM system_settings WHERE category = 'attendance_management' AND setting_key = 'last_attendance_process_date' """)
```

### 420. attendance_api_old.py:2897
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 421. attendance_api_old.py:2964
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 422. attendance_api_old.py:2993
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute(""" DELETE FROM attendance_records WHERE work_date = ? """, (target_date,))
```

### 423. attendance_api_old.py:2999
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT employee_id, punch_time, note FROM ( SELECT employee_id, check_in as punch_time, note FROM attendance WHERE DATE(check_in) = ? AND check_in IS NOT NULL
```

### 424. attendance_api_old.py:3016
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT lr.employee_id, lr.leave_type as leave_type_name, lr.reason, lr.start_date, lr.end_date, lr.leave_hours FROM leaves lr WHERE lr.status = 'approved' AND ? BETWEEN lr.start_date AND lr.end_date """, (target_date,))
```

### 425. attendance_api_old.py:3076
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance_records WHERE employee_id = ? AND work_date = ? """, (employee_id, target_date))
```

### 426. attendance_api_old.py:3184
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" INSERT INTO attendance_records ( employee_id, check_in, check_out, status, note, work_date, clock_in_time, clock_out_time, leave_hours ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 427. attendance_api_old.py:3266
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance_records SET check_in = ?, check_out = ?, status = ?, note = ?, work_date = ?, clock_in_time = ?, clock_out_time = ?, leave_hours = ?, updated_at = CURRENT_TIMESTAMP WHERE employee_id = ? AND (work_date = ? OR work_date IS NULL)
```

### 428. attendance_api_old.py:3293
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_value FROM system_settings WHERE category = 'attendance_management' AND setting_key = 'last_attendance_process_date' """)
```

### 429. attendance_api_old.py:3409
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 430. attendance_api_old.py:3469
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 431. attendance_api_old.py:3581
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 432. attendance_api_old.py:3601
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_query, (record['attendance_id'],))
```

### 433. attendance_api_old.py:3687
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(active_employees_query, dept_params)
```

### 434. attendance_api_old.py:3893
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_value FROM system_settings WHERE category = 'attendance_management' AND setting_key = 'last_attendance_process_date' """)
```

### 435. attendance_api_old.py:3975
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 436. attendance_api_old.py:4042
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 437. attendance_api_old.py:4071
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute(""" DELETE FROM attendance_records WHERE work_date = ? """, (target_date,))
```

### 438. attendance_api_old.py:4077
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT employee_id, punch_time, note FROM ( SELECT employee_id, check_in as punch_time, note FROM attendance WHERE DATE(check_in) = ? AND check_in IS NOT NULL
```

### 439. attendance_api_old.py:4094
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT lr.employee_id, lr.leave_type as leave_type_name, lr.reason, lr.start_date, lr.end_date, lr.leave_hours FROM leaves lr WHERE lr.status = 'approved' AND ? BETWEEN lr.start_date AND lr.end_date """, (target_date,))
```

### 440. attendance_api_old.py:4154
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance_records WHERE employee_id = ? AND work_date = ? """, (employee_id, target_date))
```

### 441. attendance_api_old.py:4262
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" INSERT INTO attendance_records ( employee_id, check_in, check_out, status, note, work_date, clock_in_time, clock_out_time, leave_hours ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 442. employee_api.py:90
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 443. employee_api.py:123
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.id, s.name, s.category, es.proficiency_level, es.years_experience, es.certification FROM employee_skills es JOIN skills s ON es.skill_id = s.id WHERE es.employee_id = ? ORDER BY s.category, s.name """, (employee['id'],))
```

### 444. employee_api.py:196
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM departments WHERE id = ?", (data['department_id'],))
```

### 445. employee_api.py:201
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM permissions WHERE id = ?", (data['role_id'],))
```

### 446. employee_api.py:206
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (data['manager_id'],))
```

### 447. employee_api.py:211
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (data['employee_id'],))
```

### 448. employee_api.py:216
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO employees (name, employee_id, department_id, position, email, phone, role_id, manager_id, password, hire_date, status, salary_level, id_number, address, emergency_contact, emergency_phone, photo_url, shift_type, allow_online_punch, education_level_id)
```

### 449. employee_api.py:251
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO employee_skills (employee_id, skill_id, proficiency_level, years_experience, certification)
```

### 450. employee_api.py:298
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.id, d.name, d.description, d.manager_id, d.created_at, COUNT(e.id) as employee_count, m.name as manager_name FROM departments d LEFT JOIN employees e ON d.id = e.department_id AND e.status = 'active' LEFT JOIN employees m ON d.manager_id = m.id GROUP BY d.id, d.name, d.description, d.manager_id, d.created_at, m.name ORDER BY d.id """)
```

### 451. employee_api.py:356
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (data['manager_id'],))
```

### 452. employee_api.py:361
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM departments WHERE name = ?", (data['name'],))
```

### 453. employee_api.py:366
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO departments (name, description, manager_id, created_at)
```

### 454. employee_api.py:415
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT e.id, e.name, e.employee_id, e.department_id, d.name as department_name, e.position, e.email, e.phone, e.password, e.hire_date, e.status, e.salary_level, e.id_number, e.address, e.emergency_contact, e.emergency_phone, e.role_id, p.role_name, e.manager_id, m.name as manager_name, e.shift_type, e.allow_online_punch FROM employees e JOIN departments d ON e.department_id = d.id LEFT JOIN permissions p ON e.role_id = p.id LEFT JOIN employees m ON e.manager_id = m.id WHERE e.id = ? """, (employee_id,), )
```

### 455. employee_api.py:457
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM departments WHERE id = ?", (data["department_id"],))
```

### 456. employee_api.py:462
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM permissions WHERE id = ?", (data["role_id"],))
```

### 457. employee_api.py:467
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (data["manager_id"],))
```

### 458. employee_api.py:523
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute("DELETE FROM employee_skills WHERE employee_id = ?", (employee_id,))
```

### 459. employee_api.py:528
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO employee_skills (employee_id, skill_id, proficiency_level, years_experience, certification)
```

### 460. employee_api.py:555
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute("DELETE FROM employees WHERE id = ?", (employee_id,))
```

### 461. employee_api.py:586
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name, p.role_name, e.department_id FROM employees e JOIN departments d ON e.department_id = d.id LEFT JOIN permissions p ON e.role_id = p.id WHERE e.role_id <= 2 OR e.id IN ( SELECT DISTINCT manager_id FROM employees WHERE manager_id IS NOT NULL )
```

### 462. employee_api.py:631
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT department_id FROM employees WHERE id = ? """, (employee_id,))
```

### 463. employee_api.py:642
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name, p.role_name, e.department_id FROM employees e JOIN departments d ON e.department_id = d.id LEFT JOIN permissions p ON e.role_id = p.id WHERE (e.department_id = ? OR e.role_id <= 2)
```

### 464. employee_api.py:696
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT d.*, COUNT(e.id) as employee_count, m.name as manager_name FROM departments d LEFT JOIN employees e ON d.id = e.department_id AND e.status = 'active' LEFT JOIN employees m ON d.manager_id = m.id WHERE d.id = ? GROUP BY d.id """, (dept_id,))
```

### 465. employee_api.py:722
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (data["manager_id"],))
```

### 466. employee_api.py:759
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT COUNT(*) FROM employees WHERE department_id = ?", (dept_id,))
```

### 467. employee_api.py:766
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute("DELETE FROM departments WHERE id = ?", (dept_id,))
```

### 468. employee_api.py:799
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.id, d.name, d.manager_id, m.name as manager_name, COUNT(e.id) as employee_count, GROUP_CONCAT(DISTINCT p.role_name) as available_roles
```

### 469. employee_api.py:831
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE departments SET manager_id = ? WHERE id = ? """, (data['manager_id'], department_id))
```

### 470. employee_api.py:837
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE employees SET role_id = ? WHERE department_id = ? AND role_id IS NULL """, (data['default_role_id'], department_id))
```

### 471. employee_api.py:869
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.id, d.name, d.manager_id, m.name as manager_name, COUNT(DISTINCT e.id) as total_employees, COUNT(DISTINCT CASE WHEN e.status = 'active' THEN e.id END) as active_employees, COUNT(DISTINCT CASE WHEN e.status = 'inactive' THEN e.id END) as inactive_employees
```

### 472. employee_api.py:886
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(DISTINCT a.employee_id) as attended_employees, COUNT(DISTINCT e.id) as total_active_employees
```

### 473. employee_api.py:949
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT p.id, p.promotion_date, p.from_position, p.to_position, p.reason, p.notes, pt.name as type_name, pt.description as type_description FROM employee_promotions p JOIN promotion_types pt ON p.type_id = pt.id WHERE p.employee_id = ? ORDER BY p.promotion_date DESC """, (employee_id,))
```

### 474. employee_api.py:1011
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO employee_promotions (employee_id, type_id, promotion_date, from_position, to_position, reason, notes)
```

### 475. employee_api.py:1049
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id, name, description FROM promotion_types ORDER BY id")
```

### 476. employee_api.py:1085
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT r.id, r.reward_date, r.reason, r.amount, r.approver, r.notes, rt.name as type_name, rt.type as category, rt.description as type_description FROM employee_rewards r JOIN reward_types rt ON r.type_id = rt.id WHERE r.employee_id = ? ORDER BY r.reward_date DESC """, (employee_id,))
```

### 477. employee_api.py:1148
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO employee_rewards (employee_id, type_id, reward_date, reason, amount, approver, notes)
```

### 478. employee_api.py:1186
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id, name, type, description FROM reward_types ORDER BY type, id")
```

### 479. attendance_api_broken.py:73
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name FROM employees WHERE id = ?", (employee_id,))
```

### 480. attendance_api_broken.py:126
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status, d.name as department_name FROM attendance a JOIN employees e ON a.employee_id = e.id LEFT JOIN departments d ON e.department_id = d.id ORDER BY a.check_in DESC LIMIT ? """, (limit,))
```

### 481. attendance_api_broken.py:251
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT ar.*, e.name as employee_name, e.employee_id FROM attendance ar JOIN employees e ON ar.employee_id = e.id WHERE ar.id = ? """, (attendance_id,))
```

### 482. attendance_api_broken.py:279
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("UPDATE attendance SET work_date = ? WHERE id = ?", (work_date, attendance_id))
```

### 483. attendance_api_broken.py:284
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, name, start_time, end_time FROM shifts WHERE id = ?", (shift_id,))
```

### 484. attendance_api_broken.py:293
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO schedules (employee_id, shift_date, shift_id, status, updated_at)
```

### 485. attendance_api_broken.py:433
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET shift_id = ?, status = ?, late_minutes = ?, early_leave_minutes = ?, overtime_minutes = ?, work_hours = ?, overtime_hours = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (shift['id'], status, late_minutes, early_leave_minutes, overtime_minutes, work_hours, overtime_minutes / 60.0, attendance['id']))
```

### 486. attendance_api_broken.py:502
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO attendance (employee_id, check_in, status)
```

### 487. attendance_api_broken.py:550
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO attendance (employee_id, check_in, check_out, status, note, work_date)
```

### 488. attendance_api_broken.py:605
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name, employee_id FROM employees WHERE id = ?", (employee_id,))
```

### 489. attendance_api_broken.py:612
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT id, check_in, check_out, status, note FROM attendance WHERE employee_id = ? AND DATE(check_in) = ?
```

### 490. attendance_api_broken.py:684
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT rule_type, rule_value, description FROM schedule_rules WHERE rule_type IN ( 'day_change_time', 'cross_day_attendance', 'first_punch_as_checkin', 'last_punch_as_checkout', 'late_tolerance_minutes', 'early_leave_tolerance_minutes' )
```

### 491. attendance_api_broken.py:724
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE schedule_rules SET rule_value = ? WHERE rule_type = ? """, (str(rule_value), rule_type))
```

### 492. attendance_api_broken.py:732
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO schedule_rules (rule_type, rule_value, description)
```

### 493. attendance_api_broken.py:806
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 494. attendance_api_broken.py:823
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(dept_query, params)
```

### 495. attendance_api_broken.py:963
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 496. attendance_api_broken.py:1045
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 497. attendance_api_broken.py:1166
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 498. attendance_api_broken.py:1304
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" SELECT id FROM attendance WHERE employee_id = ? AND DATE(COALESCE(clock_in_time, clock_out_time, created_at)) = ?
```

### 499. attendance_api_broken.py:1410
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET clock_in_time = ?, clock_out_time = ?, check_in = ?, check_out = ?, status = ?, note = ?, leave_hours = ?, work_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, ( check_in_time, check_out_time, check_in_time, check_out_time, attendance_status, combined_note, leave_hours, target_date, existing_record['id'] ))
```

### 500. attendance_api_broken.py:1429
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance (employee_id, clock_in_time, clock_out_time, check_in, check_out, status, note, leave_hours, work_date, created_at)
```

### 501. attendance_api_broken.py:1564
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(active_employees_query, dept_params)
```

### 502. attendance_api_broken.py:1851
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 503. attendance_api_broken.py:1880
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute(""" DELETE FROM attendance WHERE work_date = ? """, (target_date,))
```

### 504. attendance_api_broken.py:1886
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT e.id as employee_id, p.punch_datetime as punch_time, p.note FROM punch_records p JOIN employees e ON p.employee_id = e.employee_id WHERE DATE(p.punch_datetime) = ?
```

### 505. attendance_api_broken.py:1900
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT lr.employee_id, lr.leave_type as leave_type_name, lr.reason, lr.start_date, lr.end_date, lr.leave_hours FROM leaves lr WHERE lr.status = 'approved' AND ? BETWEEN lr.start_date AND lr.end_date """, (target_date,))
```

### 506. attendance_api_broken.py:1960
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance WHERE employee_id = ? AND work_date = ? """, (employee_id, target_date))
```

### 507. attendance_api_broken.py:2045
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.id, s.name, s.start_time, s.end_time, s.break_duration_minutes FROM schedules sc JOIN shifts s ON sc.shift_id = s.id WHERE sc.employee_id = ? AND sc.shift_date = ? """, (employee_id, target_date))
```

### 508. attendance_api_broken.py:2056
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.id, s.name, s.start_time, s.end_time, s.break_duration_minutes FROM employees e JOIN shifts s ON e.shift_type = s.id WHERE e.id = ? """, (employee_id,))
```

### 509. attendance_api_broken.py:2166
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" INSERT INTO attendance ( employee_id, check_in, check_out, status, note, work_date ) VALUES (?, ?, ?, ?, ?, ?)
```

### 510. attendance_api_broken.py:2262
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET check_in = ?, check_out = ?, status = ?, note = ?, work_date = ? WHERE employee_id = ? AND (work_date = ? OR work_date IS NULL)
```

### 511. attendance_api_broken.py:2416
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 512. attendance_api_broken.py:2476
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 513. attendance_api_broken.py:2589
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 514. attendance_api_broken.py:2609
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_query, (record['attendance_id'],))
```

### 515. attendance_api_broken.py:2652
**風險**: 包含危險關鍵字: DELETE, CREATE, EXEC
```sql
cursor.execute("DELETE FROM attendance WHERE DATE(COALESCE(check_in, check_out, created_at)) = ?", (target_date,))
```

### 516. attendance_api_broken.py:2657
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT e.id as employee_id, p.punch_datetime, p.status_code, p.device_id, p.raw_data, e.name as employee_name, e.employee_id as employee_code FROM punch_records p JOIN employees e ON p.employee_id = e.employee_id WHERE p.punch_date = ? ORDER BY e.id, p.punch_datetime """, (target_date,))
```

### 517. attendance_api_broken.py:2708
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute("SELECT id FROM attendance WHERE employee_id = ? AND DATE(COALESCE(check_in, check_out, created_at)) = ?", (emp_id, target_date))
```

### 518. attendance_api_broken.py:2715
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("UPDATE attendance SET check_in = ?, check_out = ?, status = ?, note = ? WHERE id = ?", (check_in_time, check_out_time, status, note, existing_record[0]))
```

### 519. attendance_api_broken.py:2801
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(daily_stats_query, params)
```

### 520. attendance_api_broken.py:2820
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(total_stats_query, params)
```

### 521. attendance_api_broken.py:2940
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, params)
```

### 522. attendance_edit_api.py:89
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 523. attendance_edit_api.py:109
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT id, name, start_time, end_time, description FROM shifts WHERE is_active = 1 ORDER BY name """)
```

### 524. attendance_edit_api.py:118
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT name, code FROM leave_types WHERE is_active = 1 ORDER BY name """)
```

### 525. attendance_edit_api.py:151
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT l.id, l.leave_type, l.start_date, l.end_date, l.leave_hours, l.reason, l.status, COALESCE(lt.name, l.leave_type) as leave_type_name
```

### 526. attendance_edit_api.py:229
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT * FROM attendance WHERE id = ?", (record_id,))
```

### 527. attendance_edit_api.py:310
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM shifts WHERE id = ?", (shift_id,))
```

### 528. attendance_edit_api.py:346
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" SELECT id FROM leaves WHERE employee_id = ? AND start_date <= ? AND end_date >= ? ORDER BY created_at DESC LIMIT 1 """, (employee_id, work_date, work_date))
```

### 529. attendance_edit_api.py:356
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE leaves SET leave_type = ?, reason = ?, leave_hours = ?, status = 'approved', updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (leave_type, leave_reason, leave_hours, existing_leave[0]))
```

### 530. attendance_edit_api.py:364
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO leaves (employee_id, leave_type, start_date, end_date, leave_hours, reason, status, created_at)
```

### 531. attendance_edit_api.py:377
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(update_query, update_values)
```

### 532. attendance_edit_api.py:415
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT a.id, a.employee_id, a.check_in, a.check_out, a.work_date, a.leave_hours, a.shift_id, s.start_time, s.end_time, s.late_tolerance_minutes, s.early_leave_tolerance_minutes, s.day_start_time FROM attendance a LEFT JOIN shifts s ON a.shift_id = s.id WHERE a.id = ? """, (record_id,))
```

### 533. attendance_edit_api.py:526
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET work_hours = ?, leave_hours = ?, late_minutes = ?, early_leave_minutes = ?, overtime_minutes = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (work_hours, leave_hours, late_minutes, early_leave_minutes, overtime_minutes, status, record_id))
```

### 534. attendance_edit_api.py:649
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 535. attendance_edit_api.py:669
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT id, name, start_time, end_time, description FROM shifts WHERE is_active = 1 ORDER BY name """)
```

### 536. attendance_edit_api.py:678
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT name, code FROM leave_types WHERE is_active = 1 ORDER BY name """)
```

### 537. attendance_edit_api.py:711
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT l.id, l.leave_type, l.start_date, l.end_date, l.leave_hours, l.reason, l.status, COALESCE(lt.name, l.leave_type) as leave_type_name
```

### 538. work_report_api.py:47
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(''' INSERT INTO work_reports (employee_id, employee_name, report_date, report_time, category, sub_category, content, photos)
```

### 539. work_report_api.py:115
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, params)
```

### 540. work_report_api.py:131
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 541. work_report_api.py:203
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, params)
```

### 542. work_report_api.py:219
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 543. work_report_api.py:255
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(''' UPDATE work_reports SET is_read = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ? ''', (report_id,))
```

### 544. work_report_api.py:296
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(''' UPDATE work_reports SET supervisor_feedback = ?, feedback_date = CURRENT_TIMESTAMP, feedback_by = ?, is_read = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ? ''', (feedback, feedback_by, report_id))
```

### 545. work_report_api.py:370
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute('SELECT COUNT(*) as total FROM work_reports')
```

### 546. work_report_api.py:374
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute('SELECT COUNT(*) as unread FROM work_reports WHERE is_read = 0')
```

### 547. work_report_api.py:378
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute('SELECT COUNT(*) as replied FROM work_reports WHERE supervisor_feedback IS NOT NULL')
```

### 548. work_report_api.py:382
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute('SELECT COUNT(*) as today FROM work_reports WHERE report_date = DATE("now")')
```

### 549. work_report_api.py:386
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(''' SELECT category, COUNT(*) as count
```

### 550. work_report_api.py:419
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute('SELECT photos FROM work_reports WHERE id = ?', (report_id,))
```

### 551. work_report_api.py:441
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute('DELETE FROM work_reports WHERE id = ?', (report_id,))
```

### 552. attendance_api.py:46
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM employees WHERE employee_id = ? """, (employee_code,))
```

### 553. attendance_api.py:58
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance WHERE employee_id = ? AND DATE(check_in) = ?
```

### 554. attendance_api.py:69
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance (employee_id, check_in, work_date, status, created_at)
```

### 555. attendance_api.py:98
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM employees WHERE employee_id = ? """, (employee_code,))
```

### 556. attendance_api.py:110
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance WHERE employee_id = ? AND DATE(check_in) = ?
```

### 557. attendance_api.py:121
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET check_out = ? WHERE id = ? """, (now, record['id']))
```

### 558. attendance_api.py:199
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, params)
```

### 559. attendance_api.py:394
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 560. attendance_api.py:437
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT * FROM attendance WHERE id = ?", (record_id,))
```

### 561. attendance_api.py:447
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT * FROM shifts WHERE id = ?", (shift_id,))
```

### 562. attendance_api.py:458
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("UPDATE attendance SET shift_id = ? WHERE id = ?", (shift_id, record_id))
```

### 563. attendance_api.py:461
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT shift_id FROM attendance WHERE id = ?", (record_id,))
```

### 564. attendance_api.py:473
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT shift_id FROM attendance WHERE id = ?", (record_id,))
```

### 565. attendance_api.py:615
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET work_hours = ?, leave_hours = ?, late_minutes = ?, early_leave_minutes = ?, overtime_minutes = ?, status = ? WHERE id = ? """, (work_hours, leave_hours, late_minutes, early_leave_minutes, overtime_minutes, status, attendance['id']))
```

### 566. attendance_api.py:660
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_records, SUM(CASE WHEN check_in IS NOT NULL AND check_out IS NOT NULL THEN 1 ELSE 0 END) as complete_records, SUM(CASE WHEN check_in IS NULL OR check_out IS NULL THEN 1 ELSE 0 END) as incomplete_records
```

### 567. attendance_api.py:714
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (limit,))
```

### 568. attendance_api.py:753
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query)
```

### 569. attendance_api.py:791
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM employees WHERE employee_id = ? """, (employee_code,))
```

### 570. attendance_api.py:814
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (employee_id, today))
```

### 571. attendance_api.py:875
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (employee_id, today))
```

### 572. attendance_api.py:1003
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 573. attendance_api.py:1067
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 574. attendance_api.py:1183
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 575. attendance_api.py:1333
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 576. attendance_api.py:1475
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name FROM employees WHERE id = ?", (int(employee_id),))
```

### 577. attendance_api.py:1477
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name FROM employees WHERE employee_id = ?", (employee_id,))
```

### 578. attendance_api.py:1586
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 579. import_api.py:143
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id, employee_id, card_number FROM employees WHERE status = 'active'")
```

### 580. import_api.py:288
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT MAX(CAST(SUBSTR(employee_id, 2) AS INTEGER)) FROM employees WHERE employee_id LIKE 'E%'")
```

### 581. import_api.py:294
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id FROM departments LIMIT 1")
```

### 582. import_api.py:298
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO departments (name, description, created_at)
```

### 583. import_api.py:307
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT id FROM shifts WHERE is_default = 1 AND is_active = 1 LIMIT 1")
```

### 584. import_api.py:312
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO employees (employee_id, name, card_number, department_id, position, status, shift_type, created_at)
```

### 585. import_api.py:332
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(check_query, (employee_id, record['date']))
```

### 586. import_api.py:356
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET check_in = ?, check_out = ?, status = ?, device_id = ?, note = ? WHERE id = ? """, (record['check_in'], record['check_out'], status, record['device_id'], note, existing[0]))
```

### 587. import_api.py:364
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute("DELETE FROM punch_records WHERE attendance_id = ?", (attendance_id,))
```

### 588. import_api.py:367
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance (employee_id, check_in, check_out, status, device_id, note)
```

### 589. import_api.py:382
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" INSERT INTO punch_records ( device_id, employee_id, punch_date, punch_time, punch_datetime, status_code ) VALUES (?, ?, ?, ?, ?, ?)
```

### 590. attendance_api_backup.py:73
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name FROM employees WHERE id = ?", (employee_id,))
```

### 591. attendance_api_backup.py:126
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status, d.name as department_name FROM attendance a JOIN employees e ON a.employee_id = e.id LEFT JOIN departments d ON e.department_id = d.id ORDER BY a.check_in DESC LIMIT ? """, (limit,))
```

### 592. attendance_api_backup.py:251
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT ar.*, e.name as employee_name, e.employee_id FROM attendance_records ar JOIN employees e ON ar.employee_id = e.id WHERE ar.id = ? """, (attendance_id,))
```

### 593. attendance_api_backup.py:279
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("UPDATE attendance_records SET work_date = ? WHERE id = ?", (work_date, attendance_id))
```

### 594. attendance_api_backup.py:284
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, name, start_time, end_time FROM shifts WHERE id = ?", (shift_id,))
```

### 595. attendance_api_backup.py:293
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO schedules (employee_id, shift_date, shift_id, status, updated_at)
```

### 596. attendance_api_backup.py:433
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance_records SET shift_id = ?, status = ?, late_minutes = ?, early_leave_minutes = ?, overtime_minutes = ?, work_hours = ?, overtime_hours = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (shift['id'], status, late_minutes, early_leave_minutes, overtime_minutes, work_hours, overtime_minutes / 60.0, attendance['id']))
```

### 597. attendance_api_backup.py:502
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO attendance (employee_id, check_in, status)
```

### 598. attendance_api_backup.py:550
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO attendance (employee_id, check_in, check_out, status, note)
```

### 599. attendance_api_backup.py:604
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name, employee_id FROM employees WHERE id = ?", (employee_id,))
```

### 600. attendance_api_backup.py:611
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT id, check_in, check_out, status, note FROM attendance WHERE employee_id = ? AND DATE(check_in) = ?
```

### 601. attendance_api_backup.py:683
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT rule_type, rule_value, description FROM schedule_rules WHERE rule_type IN ( 'day_change_time', 'cross_day_attendance', 'first_punch_as_checkin', 'last_punch_as_checkout', 'late_tolerance_minutes', 'early_leave_tolerance_minutes' )
```

### 602. attendance_api_backup.py:723
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE schedule_rules SET rule_value = ? WHERE rule_type = ? """, (str(rule_value), rule_type))
```

### 603. attendance_api_backup.py:731
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO schedule_rules (rule_type, rule_value, description)
```

### 604. attendance_api_backup.py:805
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 605. attendance_api_backup.py:822
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(dept_query, params)
```

### 606. attendance_api_backup.py:962
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 607. attendance_api_backup.py:1037
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 608. attendance_api_backup.py:1154
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(active_employees_query, dept_params)
```

### 609. attendance_api_backup.py:1271
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" SELECT id FROM attendance_records WHERE employee_id = ? AND DATE(COALESCE(clock_in_time, clock_out_time, created_at)) = ?
```

### 610. attendance_api_backup.py:1377
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance_records SET clock_in_time = ?, clock_out_time = ?, check_in = ?, check_out = ?, status = ?, note = ?, leave_hours = ?, work_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, ( check_in_time, check_out_time, check_in_time, check_out_time, attendance_status, combined_note, leave_hours, target_date, existing_record['id'] ))
```

### 611. attendance_api_backup.py:1396
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance_records (employee_id, clock_in_time, clock_out_time, check_in, check_out, status, note, leave_hours, work_date, created_at)
```

### 612. attendance_api_backup.py:1531
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(active_employees_query, dept_params)
```

### 613. attendance_api_backup.py:1737
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_value FROM system_settings WHERE category = 'attendance_management' AND setting_key = 'last_attendance_process_date' """)
```

### 614. attendance_api_backup.py:1819
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 615. attendance_api_backup.py:1886
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 616. attendance_api_backup.py:1915
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute(""" DELETE FROM attendance_records WHERE work_date = ? """, (target_date,))
```

### 617. attendance_api_backup.py:1921
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT employee_id, punch_time, note FROM ( SELECT employee_id, check_in as punch_time, note FROM attendance WHERE DATE(check_in) = ? AND check_in IS NOT NULL
```

### 618. attendance_api_backup.py:1938
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT lr.employee_id, lr.leave_type as leave_type_name, lr.reason, lr.start_date, lr.end_date, lr.leave_hours FROM leaves lr WHERE lr.status = 'approved' AND ? BETWEEN lr.start_date AND lr.end_date """, (target_date,))
```

### 619. attendance_api_backup.py:1998
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance_records WHERE employee_id = ? AND work_date = ? """, (employee_id, target_date))
```

### 620. attendance_api_backup.py:2106
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" INSERT INTO attendance_records ( employee_id, check_in, check_out, status, note, work_date, clock_in_time, clock_out_time, leave_hours ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 621. attendance_api_backup.py:2188
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance_records SET check_in = ?, check_out = ?, status = ?, note = ?, work_date = ?, clock_in_time = ?, clock_out_time = ?, leave_hours = ?, updated_at = CURRENT_TIMESTAMP WHERE employee_id = ? AND (work_date = ? OR work_date IS NULL)
```

### 622. attendance_api_backup.py:2215
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_value FROM system_settings WHERE category = 'attendance_management' AND setting_key = 'last_attendance_process_date' """)
```

### 623. attendance_api_backup.py:2331
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 624. attendance_api_backup.py:2391
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 625. attendance_api_backup.py:2503
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 626. attendance_api_backup.py:2523
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_query, (record['attendance_id'],))
```

### 627. attendance_api_backup.py:2609
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(active_employees_query, dept_params)
```

### 628. attendance_api_backup.py:2815
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_value FROM system_settings WHERE category = 'attendance_management' AND setting_key = 'last_attendance_process_date' """)
```

### 629. attendance_api_backup.py:2897
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 630. attendance_api_backup.py:2964
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 631. attendance_api_backup.py:2993
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute(""" DELETE FROM attendance_records WHERE work_date = ? """, (target_date,))
```

### 632. attendance_api_backup.py:2999
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT employee_id, punch_time, note FROM ( SELECT employee_id, check_in as punch_time, note FROM attendance WHERE DATE(check_in) = ? AND check_in IS NOT NULL
```

### 633. attendance_api_backup.py:3016
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT lr.employee_id, lr.leave_type as leave_type_name, lr.reason, lr.start_date, lr.end_date, lr.leave_hours FROM leaves lr WHERE lr.status = 'approved' AND ? BETWEEN lr.start_date AND lr.end_date """, (target_date,))
```

### 634. attendance_api_backup.py:3076
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance_records WHERE employee_id = ? AND work_date = ? """, (employee_id, target_date))
```

### 635. attendance_api_backup.py:3184
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" INSERT INTO attendance_records ( employee_id, check_in, check_out, status, note, work_date, clock_in_time, clock_out_time, leave_hours ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 636. attendance_api_backup.py:3266
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance_records SET check_in = ?, check_out = ?, status = ?, note = ?, work_date = ?, clock_in_time = ?, clock_out_time = ?, leave_hours = ?, updated_at = CURRENT_TIMESTAMP WHERE employee_id = ? AND (work_date = ? OR work_date IS NULL)
```

### 637. attendance_api_backup.py:3293
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_value FROM system_settings WHERE category = 'attendance_management' AND setting_key = 'last_attendance_process_date' """)
```

### 638. attendance_api_backup.py:3409
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 639. attendance_api_backup.py:3469
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query, query_params)
```

### 640. attendance_api_backup.py:3581
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 641. attendance_api_backup.py:3601
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(attendance_query, (record['attendance_id'],))
```

### 642. attendance_api_backup.py:3687
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(active_employees_query, dept_params)
```

### 643. attendance_api_backup.py:3893
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_value FROM system_settings WHERE category = 'attendance_management' AND setting_key = 'last_attendance_process_date' """)
```

### 644. attendance_api_backup.py:3975
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
```

### 645. attendance_api_backup.py:4042
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT e.id, e.name, e.employee_id, d.name as department_name FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
```

### 646. attendance_api_backup.py:4071
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute(""" DELETE FROM attendance_records WHERE work_date = ? """, (target_date,))
```

### 647. attendance_api_backup.py:4077
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT employee_id, punch_time, note FROM ( SELECT employee_id, check_in as punch_time, note FROM attendance WHERE DATE(check_in) = ? AND check_in IS NOT NULL
```

### 648. attendance_api_backup.py:4094
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT lr.employee_id, lr.leave_type as leave_type_name, lr.reason, lr.start_date, lr.end_date, lr.leave_hours FROM leaves lr WHERE lr.status = 'approved' AND ? BETWEEN lr.start_date AND lr.end_date """, (target_date,))
```

### 649. attendance_api_backup.py:4154
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance_records WHERE employee_id = ? AND work_date = ? """, (employee_id, target_date))
```

### 650. attendance_api_backup.py:4262
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" INSERT INTO attendance_records ( employee_id, check_in, check_out, status, note, work_date, clock_in_time, clock_out_time, leave_hours ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 651. attendance_processing_api.py:235
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM employees WHERE status = 'active'")
```

### 652. attendance_processing_api.py:300
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 653. attendance_processing_api.py:313
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(existing_query, params_with_dates)
```

### 654. attendance_processing_api.py:431
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT id, employee_id, name FROM employees WHERE status = 'active' ORDER BY employee_id """ )
```

### 655. system_api_backup.py:45
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value, description, category, data_type FROM system_settings ORDER BY category, setting_key """)
```

### 656. system_api_backup.py:90
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE system_settings SET setting_value = ?, updated_at = CURRENT_TIMESTAMP WHERE setting_key = ? """, (value_str, setting_key))
```

### 657. system_api_backup.py:126
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT rule_name, rule_value, description, is_active FROM attendance_rules ORDER BY rule_name """)
```

### 658. system_api_backup.py:149
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance_rules SET rule_value = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE rule_name = ? """, (rule_data.get('value'), rule_data.get('is_active', True), rule_name))
```

### 659. system_api_backup.py:159
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance_rules (rule_name, rule_value, description, is_active)
```

### 660. system_api_backup.py:197
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT notification_type, is_enabled, settings, description FROM notification_settings ORDER BY notification_type """)
```

### 661. system_api_backup.py:229
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE notification_settings SET is_enabled = ?, settings = ?, updated_at = CURRENT_TIMESTAMP WHERE notification_type = ? """, ( notification_data.get('is_enabled', True), settings_json, notification_type ))
```

### 662. system_api_backup.py:269
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT id, role_name, description, permissions, is_active, created_at FROM user_roles ORDER BY role_name """)
```

### 663. system_api_backup.py:305
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO user_roles (role_name, description, permissions)
```

### 664. system_api_backup.py:402
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query)
```

### 665. system_api_backup.py:447
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, values)
```

### 666. system_api_backup.py:537
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (item_id,))
```

### 667. system_api_backup.py:579
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, values)
```

### 668. system_api_backup.py:624
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT status_code, status_name, description, color_code FROM clock_status_types WHERE is_active = 1 ORDER BY status_code """)
```

### 669. system_api_backup.py:687
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT 1")
```

### 670. system_api_backup.py:868
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM employees WHERE is_active = 1")
```

### 671. system_api_backup.py:871
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM attendance WHERE DATE(check_in) = DATE('now')")
```

### 672. system_api_backup.py:874
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM leaves WHERE status = 'pending'")
```

### 673. profile_api.py:55
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT e.employee_id, e.name, e.email, e.phone, d.name as department, e.position, e.photo_url FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE e.employee_id = ? """, (employee_id,))
```

### 674. profile_api.py:128
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id,))
```

### 675. profile_api.py:156
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(sql, update_values)
```

### 676. profile_api.py:213
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT password FROM employees WHERE employee_id = ?", (employee_id,))
```

### 677. profile_api.py:232
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( "UPDATE employees SET password = ? WHERE employee_id = ?", (new_password_hash, employee_id)
```

### 678. profile_api.py:324
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, photo_url FROM employees WHERE employee_id = ?", (employee_id,))
```

### 679. profile_api.py:346
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( "UPDATE employees SET photo_url = ? WHERE employee_id = ?", (avatar_url, employee_id)
```

### 680. profile_api.py:384
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT photo_url FROM employees WHERE employee_id = ?", (employee_id,))
```

### 681. profile_api.py:403
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( "UPDATE employees SET photo_url = NULL WHERE employee_id = ?", (employee_id,)
```

### 682. auth_api.py:82
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT e.id, e.name, e.employee_id, e.password, e.status, e.role_id, e.department_id, d.name as department_name, e.email, e.phone FROM employees e LEFT JOIN departments d ON e.department_id = d.id WHERE (UPPER(e.employee_id) = UPPER(?) OR e.name = ?) AND e.status = 'active'
```

### 683. auth_api.py:113
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("UPDATE employees SET password = ? WHERE id = ?", (new_hashed, employee[0]))
```

### 684. auth_api.py:124
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("UPDATE employees SET password = ? WHERE id = ?", (new_hashed, employee[0]))
```

### 685. auth_api.py:253
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT allow_online_punch, name, employee_id FROM employees WHERE id = ? """, (employee_id,), )
```

### 686. auth_api.py:333
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( "SELECT password, employee_id FROM employees WHERE id = ?", (employee_id,)
```

### 687. auth_api.py:361
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE employees SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (new_hashed_password, employee_id), )
```

### 688. auth_api.py:446
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, params)
```

### 689. auth_api.py:524
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM leaves WHERE status = 'pending'")
```

### 690. auth_api.py:529
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT COUNT(*)
```

### 691. auth_api.py:541
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved, COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected, COUNT(*) as total
```

### 692. auth_api.py:560
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT lr.id, e.name as employee_name, lr.leave_type, lr.status, lr.approved_at, approver.name as approver_name FROM leaves lr JOIN employees e ON lr.employee_id = e.id LEFT JOIN employees approver ON lr.approver_id = approver.id WHERE lr.status IN ('approved', 'rejected')
```

### 693. auth_api.py:648
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT id, employee_id, status, leave_type, start_date, end_date, reason FROM leaves WHERE id = ? """, (leave_id,), )
```

### 694. auth_api.py:668
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE leaves SET status = ?, approver_id = ?, comment = ?, approved_at = ? WHERE id = ? """, (new_status, approver_id, comment, approved_at, leave_id), )
```

### 695. auth_api.py:680
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name FROM employees WHERE id = ?", (approver_id,))
```

### 696. auth_api.py:685
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name FROM employees WHERE id = ?", (leave_record[1],))
```

### 697. auth_api.py:734
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute( """ SELECT id, session_token, created_at, expires_at, ip_address, user_agent, is_active, logged_out_at FROM user_sessions WHERE user_id = ? ORDER BY created_at DESC LIMIT 20 """, (user_id,), )
```

### 698. auth_api.py:792
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT session_token FROM user_sessions WHERE id = ? AND user_id = ? """, (session_id, user_id), )
```

### 699. auth_api.py:805
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE user_sessions SET is_active = 0, logged_out_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ? """, (session_id, user_id), )
```

### 700. leave_api.py:124
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT id, name, code, max_days_per_year, requires_approval, is_paid, description, is_active, created_at FROM leave_types WHERE is_active = 1 ORDER BY name """)
```

### 701. leave_api.py:185
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM leave_types WHERE code = ?", (data["code"],))
```

### 702. leave_api.py:190
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" INSERT INTO leave_types ( name, code, max_days_per_year, requires_approval, is_paid, description ) VALUES (?, ?, ?, ?, ?, ?)
```

### 703. leave_api.py:263
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id_param,))
```

### 704. leave_api.py:302
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 705. leave_api.py:400
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (data["employee_id"],))
```

### 706. leave_api.py:405
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id, max_days_per_year, requires_approval FROM leave_types WHERE code = ? AND is_active = 1 """, (data["leave_type"],))
```

### 707. leave_api.py:421
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COALESCE(SUM(leave_hours), 0) as used_days
```

### 708. leave_api.py:436
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM leaves WHERE employee_id = ? AND status IN ('pending', 'approved')
```

### 709. leave_api.py:458
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" INSERT INTO leaves ( employee_id, leave_type, start_date, end_date, leave_hours, reason, status, substitute_id, emergency_contact, created_at ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
```

### 710. leave_api.py:518
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT lr.id, lr.employee_id, lr.status, e.name as employee_name FROM leaves lr JOIN employees e ON lr.employee_id = e.id WHERE lr.id = ? """, (request_id,))
```

### 711. leave_api.py:533
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, name FROM employees WHERE id = ?", (data["approver_id"],))
```

### 712. leave_api.py:539
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE leaves SET status = 'approved', approved_by = ?, approved_at = datetime('now'), comments = ?, updated_at = datetime('now')
```

### 713. leave_api.py:594
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT lr.id, lr.employee_id, lr.status, e.name as employee_name FROM leaves lr JOIN employees e ON lr.employee_id = e.id WHERE lr.id = ? """, (request_id,))
```

### 714. leave_api.py:609
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, name FROM employees WHERE id = ?", (data["approver_id"],))
```

### 715. leave_api.py:615
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE leaves SET status = 'rejected', approved_by = ?, approved_at = datetime('now'), rejection_reason = ?, updated_at = datetime('now')
```

### 716. leave_api.py:658
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" SELECT lr.id, lr.employee_id, lr.leave_type, lr.start_date, lr.end_date, lr.leave_hours, lr.reason, lr.status, lr.created_at, lr.approved_at, lr.approved_by, lr.rejection_reason, lr.comments, lr.created_at, lr.updated_at, e.name as employee_name, e.employee_id as employee_code, lt.name as leave_type_name, lt.is_paid, lt.max_days_per_year, approver.name as approver_name FROM leaves lr LEFT JOIN employees e ON lr.employee_id = e.id LEFT JOIN leave_types lt ON lr.leave_type_id = lt.id LEFT JOIN employees approver ON lr.approved_by = approver.id WHERE lr.id = ? """, (request_id,))
```

### 717. leave_api.py:721
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id_param,))
```

### 718. leave_api.py:859
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id_param,))
```

### 719. leave_api.py:921
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, query_params)
```

### 720. leave_api.py:978
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (data["employee_id"],))
```

### 721. leave_api.py:983
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT code, name, requires_approval FROM leave_types WHERE code = ? AND is_active = 1 """, (data["leave_type"],))
```

### 722. leave_api.py:1010
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" INSERT INTO leaves ( employee_id, leave_type, start_date, end_date, leave_hours, reason, status, substitute_id, emergency_contact, created_at ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
```

### 723. leave_api.py:1054
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" SELECT lr.id, lr.employee_id, lr.leave_type, lr.start_date, lr.end_date, lr.leave_hours, lr.reason, lr.status, lr.created_at, e.name as employee_name, e.employee_id as employee_code, lt.name as leave_type_name, lt.is_paid FROM leaves lr LEFT JOIN employees e ON lr.employee_id = e.id LEFT JOIN leave_types lt ON lr.leave_type = lt.code WHERE lr.id = ? """, (leave_id,))
```

### 724. leave_api.py:1098
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT status FROM leaves WHERE id = ?", (leave_id,))
```

### 725. leave_api.py:1156
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT status FROM leaves WHERE id = ?", (leave_id,))
```

### 726. leave_api.py:1165
**風險**: 包含危險關鍵字: DELETE, EXEC
```sql
cursor.execute("DELETE FROM leaves WHERE id = ?", (leave_id,))
```

### 727. shift_api.py:74
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT id, name, code, start_time, end_time, break_start_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime, color_code, description, is_active, created_at, updated_at, day_start_time, is_default FROM shifts WHERE is_active = 1 ORDER BY is_default DESC, name """ )
```

### 728. shift_api.py:167
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM shifts WHERE code = ?", (data["code"],))
```

### 729. shift_api.py:174
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("UPDATE shifts SET is_default = 0 WHERE is_active = 1")
```

### 730. shift_api.py:179
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ INSERT INTO shifts ( name, code, start_time, end_time, break_start_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime, color_code, description, day_start_time, is_default ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 731. shift_api.py:236
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute( """ SELECT id, name, code, start_time, end_time, break_start_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime, color_code, description, is_active, created_at, updated_at, day_start_time, is_default FROM shifts WHERE id = ? """, (shift_id,), )
```

### 732. shift_api.py:337
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM shifts WHERE id = ?", (shift_id,))
```

### 733. shift_api.py:342
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( "SELECT id FROM shifts WHERE code = ? AND id != ?", (data["code"], shift_id)
```

### 734. shift_api.py:352
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("UPDATE shifts SET is_default = 0 WHERE id != ? AND is_active = 1", (shift_id,))
```

### 735. shift_api.py:360
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE shifts SET name = ?, code = ?, start_time = ?, end_time = ?, break_start_time = ?, break_duration_minutes = ?, pre_overtime_threshold_minutes = ?, post_overtime_threshold_minutes = ?, enable_pre_overtime = ?, enable_post_overtime = ?, auto_calculate_overtime = ?, color_code = ?, description = ?, day_start_time = ?, is_default = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, ( data["name"], data["code"], data["start_time"], data["end_time"], data.get("break_start_time"), data.get("break_duration_minutes", 60), data.get("pre_overtime_threshold_minutes", 0), data.get("post_overtime_threshold_minutes", 0), data.get("enable_pre_overtime", False), data.get("enable_post_overtime", False), data.get("auto_calculate_overtime", True), data.get("color_code", "#3B82F6"), data.get("description", ""), data.get("day_start_time") or calculate_day_start_time(data.get("start_time", "08:00")), data.get("is_default", False), shift_id ), )
```

### 736. shift_api.py:422
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id, is_active FROM shifts WHERE id = ?", (shift_id,))
```

### 737. shift_api.py:431
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ UPDATE shifts SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (shift_id,), )
```

### 738. shift_api.py:489
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT start_time, end_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime FROM shifts WHERE id = ? AND is_active = 1 """, (data["shift_id"],), )
```

### 739. shift_api.py:621
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT name, start_time, end_time, break_duration_minutes, pre_overtime_threshold_minutes, post_overtime_threshold_minutes, enable_pre_overtime, enable_post_overtime, auto_calculate_overtime FROM shifts WHERE id = ? AND is_active = 1 """, (data["shift_id"],), )
```

### 740. shift_api.py:756
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (schedule["employee_id"],))
```

### 741. shift_api.py:765
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO schedules (employee_id, shift_date, shift_type, start_time, end_time)
```

### 742. shift_api.py:829
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT * FROM schedule_rules ORDER BY rule_type")
```

### 743. shift_api.py:845
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT INTO schedule_rules (rule_type, rule_value, description)
```

### 744. shift_api.py:905
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id_param,))
```

### 745. shift_api.py:937
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, query_params)
```

### 746. shift_api.py:986
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_schedules, COUNT(DISTINCT employee_id) as total_employees
```

### 747. shift_api.py:996
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT sh.name, COUNT(*) as count
```

### 748. shift_api.py:1008
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.name, COUNT(*) as count
```

### 749. shift_api.py:1063
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name FROM employees WHERE id = ?", (employee_id,))
```

### 750. shift_api.py:1069
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT name, start_time, end_time FROM shifts WHERE id = ?", (shift_id,))
```

### 751. shift_api.py:1075
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM schedules WHERE employee_id = ? AND shift_date = ? """, (employee_id, date))
```

### 752. shift_api.py:1083
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE schedules SET shift_id = ?, updated_at = CURRENT_TIMESTAMP WHERE employee_id = ? AND shift_date = ? """, (shift_id, employee_id, date))
```

### 753. shift_api.py:1091
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO schedules (employee_id, shift_date, shift_id, status)
```

### 754. shift_api.py:1132
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT name, start_time, end_time, work_hours FROM shifts WHERE id = ? """, (shift_id,))
```

### 755. shift_api.py:1142
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id, check_in, check_out, clock_in_time, clock_out_time FROM attendance WHERE employee_id = ? AND work_date = ? """, (employee_id, date))
```

### 756. shift_api.py:1205
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET shift_id = ?, status = ?, late_minutes = ?, early_leave_minutes = ?, overtime_minutes = ?, work_hours = ?, overtime_hours = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? """, (shift_id, status, late_minutes, early_leave_minutes, overtime_minutes, work_hours, overtime_minutes / 60.0, attendance['id']))
```

### 757. report_api.py:39
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM employees WHERE is_active = 1")
```

### 758. report_api.py:43
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM departments WHERE is_active = 1")
```

### 759. report_api.py:48
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(DISTINCT employee_id)
```

### 760. report_api.py:59
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*)
```

### 761. report_api.py:69
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*)
```

### 762. report_api.py:78
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COALESCE(0, 0)
```

### 763. report_api.py:118
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT ar.id, ar.employee_id, e.name as employee_name, ar.check_in, ar.check_out, ar.status FROM attendance ar JOIN employees e ON ar.employee_id = e.id ORDER BY ar.check_in DESC LIMIT 10 """)
```

### 764. report_api.py:140
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT lr.id, lr.employee_id, e.name as employee_name, lr.start_date, lr.end_date, lr.status, lt.name as leave_type FROM leaves lr JOIN employees e ON lr.employee_id = e.id JOIN leave_types lt ON lr.leave_type = lt.code ORDER BY lr.created_at DESC LIMIT 10 """)
```

### 765. report_api.py:164
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT ar.id, ar.employee_id, e.name as employee_name, ar.check_in, ar.status FROM attendance ar JOIN employees e ON ar.employee_id = e.id WHERE ar.status IN ('late', 'early_leave', 'absent')
```

### 766. report_api.py:241
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT lr.id, lr.employee_id, e.name as employee_name, lr.start_date, lr.end_date, lr.leave_hours, lt.name as leave_type, lr.status FROM leaves lr JOIN employees e ON lr.employee_id = e.id JOIN leave_types lt ON lr.leave_type = lt.code WHERE lr.start_date BETWEEN ? AND ? ORDER BY lr.start_date """, (start_date, end_date))
```

### 767. report_api.py:254
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT ar.id, ar.employee_id, e.name as employee_name, DATE(ar.check_in) as work_date, 0
```

### 768. report_api.py:332
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id_param,))
```

### 769. report_api.py:471
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT strftime('%H', check_in) as hour, COUNT(*) as count
```

### 770. report_api.py:489
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT strftime('%H', check_out) as hour, COUNT(*) as count
```

### 771. report_api.py:538
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT lt.name as leave_type, COUNT(lr.id) as request_count, SUM(lr.leave_hours) as total_hours, COUNT(CASE WHEN lr.status = 'approved' THEN 1 END) as approved_count, COUNT(CASE WHEN lr.status = 'rejected' THEN 1 END) as rejected_count, COUNT(CASE WHEN lr.status = 'pending' THEN 1 END) as pending_count
```

### 772. report_api.py:568
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT strftime('%m', start_date) as month, COUNT(*) as request_count, SUM(leave_hours) as total_hours
```

### 773. report_api.py:749
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total_requests, SUM(leave_hours) as total_days, COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count, COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count
```

### 774. report_api.py:762
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT d.name as department_name, COUNT(DISTINCT e.id) as employee_count, COUNT(ar.id) as attendance_count, ROUND(COUNT(ar.id) * 100.0 / (COUNT(DISTINCT e.id) * 22), 1) as attendance_rate
```

### 775. overtime_api.py:455
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT id, code, name, rate, description, is_active, created_at FROM overtime_types WHERE is_active = 1 ORDER BY code """)
```

### 776. system_api.py:45
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT setting_key, setting_value, description, category, data_type FROM system_settings ORDER BY category, setting_key """)
```

### 777. system_api.py:90
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE system_settings SET setting_value = ?, updated_at = CURRENT_TIMESTAMP WHERE setting_key = ? """, (value_str, setting_key))
```

### 778. system_api.py:126
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT rule_name, rule_value, description, is_active FROM attendance_rules ORDER BY rule_name """)
```

### 779. system_api.py:149
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance_rules SET rule_value = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE rule_name = ? """, (rule_data.get('value'), rule_data.get('is_active', True), rule_name))
```

### 780. system_api.py:159
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance_rules (rule_name, rule_value, description, is_active)
```

### 781. system_api.py:197
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT notification_type, is_enabled, settings, description FROM notification_settings ORDER BY notification_type """)
```

### 782. system_api.py:229
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE notification_settings SET is_enabled = ?, settings = ?, updated_at = CURRENT_TIMESTAMP WHERE notification_type = ? """, ( notification_data.get('is_enabled', True), settings_json, notification_type ))
```

### 783. system_api.py:269
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT id, role_name, description, permission_level, created_at FROM permissions ORDER BY role_name """)
```

### 784. system_api.py:295
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO permissions (role_name, description, permission_level)
```

### 785. system_api.py:392
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query)
```

### 786. system_api.py:437
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, values)
```

### 787. system_api.py:531
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (item_id,))
```

### 788. system_api.py:574
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, values)
```

### 789. system_api.py:650
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT status_code, status_name, description, color_code FROM clock_status_types WHERE is_active = 1 ORDER BY status_code """)
```

### 790. system_api.py:713
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT 1")
```

### 791. system_api.py:894
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM employees WHERE is_active = 1")
```

### 792. system_api.py:897
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM attendance WHERE DATE(check_in) = DATE('now')")
```

### 793. system_api.py:900
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM leaves WHERE status = 'pending'")
```

### 794. system_api.py:935
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT id, name, description, level_order FROM education_levels ORDER BY level_order """)
```

### 795. system_api.py:971
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT id, name, category, description FROM skills ORDER BY category, name """)
```

### 796. notification_service.py:46
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT * FROM notification_rules WHERE event_type = ? """, (event_type,))
```

### 797. notification_service.py:73
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO notifications (type, recipients, message, status, created_at)
```

### 798. notification_service.py:102
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE notifications SET status = ?, sent_at = ? WHERE id = ? """, (status, sent_at, notification_id))
```

### 799. notification_service.py:108
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE notifications SET status = ? WHERE id = ? """, (status, notification_id))
```

### 800. notification_service.py:208
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT e.id, e.name, e.email FROM employees e JOIN departments d ON e.id = d.manager_id WHERE d.name = ? """, (dept_name,))
```

### 801. notification_service.py:273
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT e.name, e.email, d.name as dept_name, m.id as manager_id, m.name as manager_name, m.email as manager_email FROM employees e JOIN departments d ON e.department_id = d.id LEFT JOIN employees m ON d.manager_id = m.id WHERE e.id = ? """, (employee_id,))
```

### 802. health_monitor.py:43
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM employees")
```

### 803. health_monitor.py:46
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM attendance")
```

### 804. health_monitor.py:49
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM departments")
```

### 805. health_monitor.py:164
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late, SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent
```

### 806. health_monitor.py:178
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT COUNT(*) as total, SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal, SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late
```

### 807. health_monitor.py:190
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(*) FROM leaves WHERE status = 'pending'")
```

### 808. enhanced_attendance_processor.py:54
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT MAX(work_date)
```

### 809. enhanced_attendance_processor.py:337
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT id, name, employee_id FROM employees WHERE status IS NULL OR status IN ('active', 'trial')
```

### 810. enhanced_attendance_processor.py:441
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id, check_in, check_out, status FROM attendance WHERE employee_id = ? AND work_date = ? LIMIT 1 """, (employee_id, target_date))
```

### 811. enhanced_attendance_processor.py:485
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT p.punch_datetime, p.status_code, p.device_id FROM punch_records p JOIN employees e ON p.employee_id = e.employee_id WHERE e.id = ? AND p.punch_datetime BETWEEN ? AND ? ORDER BY p.punch_datetime """, (employee_id, start_datetime, end_datetime))
```

### 812. enhanced_attendance_processor.py:610
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" SELECT SUM(leave_hours)
```

### 813. enhanced_attendance_processor.py:695
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.start_time, s.end_time, s.name FROM schedules sc JOIN shifts s ON sc.shift_id = s.id WHERE sc.employee_id = ? AND sc.shift_date = ? """, (employee_id, target_date))
```

### 814. enhanced_attendance_processor.py:706
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.start_time, s.end_time, s.name FROM employees e JOIN shifts s ON e.shift_type = s.id WHERE e.id = ? """, (employee_id,))
```

### 815. enhanced_attendance_processor.py:754
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.start_time, s.end_time, s.name, s.late_tolerance_minutes, s.early_leave_tolerance_minutes FROM schedules sc JOIN shifts s ON sc.shift_id = s.id WHERE sc.employee_id = ? AND sc.shift_date = ? """, (employee_id, target_date))
```

### 816. enhanced_attendance_processor.py:765
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.start_time, s.end_time, s.name, s.late_tolerance_minutes, s.early_leave_tolerance_minutes FROM employees e JOIN shifts s ON e.shift_type = s.id WHERE e.id = ? """, (employee_id,))
```

### 817. enhanced_attendance_processor.py:873
**風險**: 包含危險關鍵字: CREATE, EXEC
```sql
cursor.execute(""" INSERT INTO attendance ( employee_id, work_date, check_in, check_out, status, work_hours, leave_hours, late_minutes, early_leave_minutes, overtime_minutes, note, created_at ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 818. enhanced_attendance_processor.py:917
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET check_in = ?, check_out = ?, status = ?, work_hours = ?, leave_hours = ?, late_minutes = ?, early_leave_minutes = ?, overtime_minutes = ?, note = ? WHERE id = ? """, ( check_in, check_out, status, work_hours, leave_hours, metrics['late_minutes'], metrics['early_leave_minutes'], metrics['overtime_minutes'], note, record_id ))
```

### 819. scheduler.py:155
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ INSERT OR REPLACE INTO schedules (employee_id, shift_date, shift_type, start_time, end_time, is_overtime)
```

### 820. scheduler.py:200
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT COUNT(*) FROM schedules
```

### 821. scheduler.py:213
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute( """ SELECT COUNT(*) FROM schedules
```

### 822. scheduler.py:229
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT shift_type FROM schedules WHERE employee_id = ? AND shift_date = date(?, '-1 day')
```

### 823. scheduler.py:267
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT s.id, s.employee_id, s.shift_date, s.shift_type, s.start_time, s.end_time, s.is_overtime, e.name as employee_name FROM schedules s JOIN employees e ON s.employee_id = e.id WHERE s.employee_id = ? AND s.shift_date BETWEEN ? AND ? ORDER BY s.shift_date """, (employee_id, start_date, end_date), )
```

### 824. scheduler.py:306
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute( """ SELECT s.*, e.name as employee_name, d.name as department_name FROM schedules s JOIN employees e ON s.employee_id = e.id JOIN departments d ON e.department_id = d.id WHERE s.shift_date = ? ORDER BY s.shift_type, e.name """, (target_date,), )
```

### 825. scheduler.py:353
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT * FROM schedule_rules")
```

### 826. employee_service.py:65
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, params)
```

### 827. employee_service.py:76
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(base_query, params)
```

### 828. employee_service.py:102
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query)
```

### 829. employee_service.py:157
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (employee_id,))
```

### 830. employee_service.py:216
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (data['employee_id'],))
```

### 831. employee_service.py:224
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE email = ?", (data['email'],))
```

### 832. employee_service.py:233
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO employees (employee_id, name, email, phone, department_id, position_id, hire_date, status, created_at)
```

### 833. employee_service.py:289
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query)
```

### 834. attendance_service.py:73
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(count_query, params)
```

### 835. attendance_service.py:84
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, params)
```

### 836. attendance_service.py:112
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(stats_query)
```

### 837. attendance_service.py:123
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(DISTINCT employee_id) FROM attendance")
```

### 838. attendance_service.py:126
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute("SELECT COUNT(DISTINCT DATE(check_in)) FROM attendance")
```

### 839. attendance_service.py:185
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(query, (record_id,))
```

### 840. attendance_service.py:241
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute("SELECT id FROM employees WHERE id = ?", (employee_id,))
```

### 841. attendance_service.py:250
**風險**: 包含危險關鍵字: CREATE, EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance (employee_id, check_in, status, note, created_at)
```

### 842. attendance_service.py:303
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance WHERE employee_id = ? AND DATE(check_in) = ? AND check_out IS NULL
```

### 843. attendance_processor.py:153
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT check_in, check_out, status, note FROM attendance WHERE employee_id = ? AND ((check_in BETWEEN ? AND ?) OR (check_out BETWEEN ? AND ?))
```

### 844. attendance_processor.py:226
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance (employee_id, check_in, status, device_id, note, work_date)
```

### 845. attendance_processor.py:235
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" UPDATE attendance SET check_out = ?, device_id = COALESCE(?, device_id), note = CASE WHEN ? IS NOT NULL THEN COALESCE(note, '') || ' | ' || ? ELSE note END, work_date = COALESCE(work_date, ?)
```

### 846. attendance_processor.py:249
**風險**: 包含危險關鍵字: EXEC, 可能未使用參數化查詢
```sql
cursor.execute(""" INSERT INTO attendance (employee_id, check_out, status, device_id, note, work_date)
```

### 847. attendance_processor.py:257
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT id FROM attendance WHERE employee_id = ? AND check_out = ? ORDER BY id DESC LIMIT 1 """, (employee_id, punch_datetime))
```

### 848. attendance_processor.py:295
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT check_in, check_out FROM attendance WHERE employee_id = ? AND DATE(check_in) = ?
```

### 849. attendance_processor.py:318
**風險**: 包含危險關鍵字: EXEC
```sql
cursor.execute(""" SELECT s.start_time, s.end_time, s.break_duration_minutes, s.pre_overtime_threshold_minutes, s.post_overtime_threshold_minutes, s.enable_pre_overtime, s.enable_post_overtime FROM schedules sc JOIN shifts s ON sc.shift_id = s.id WHERE sc.employee_id = ? AND sc.shift_date = ? """, (employee_id, work_date))
```

## 修復建議
1. 使用參數化查詢 (cursor.execute(sql, params))
2. 避免字串拼接構建SQL
3. 驗證和清理所有用戶輸入
4. 使用白名單驗證動態表名和欄位名
5. 實作最小權限原則