#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL 安全檢查工具
檢查專案中的 SQL 查詢是否存在注入風險
"""

import os
import re
import sys
from typing import List, Dict, Any

def find_python_files(directory: str) -> List[str]:
    """查找所有 Python 檔案"""
    python_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    return python_files

def extract_sql_queries(file_path: str) -> List[Dict[str, Any]]:
    """從 Python 檔案中提取 SQL 查詢"""
    queries = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 查找 cursor.execute 調用
        execute_pattern = r'cursor\.execute\s*\(\s*(["\'].*?["\']|\w+)'
        
        for i, line in enumerate(lines):
            if 'cursor.execute' in line:
                # 提取多行 SQL 查詢
                sql_lines = []
                j = i
                in_sql = False
                
                while j < len(lines):
                    current_line = lines[j].strip()
                    
                    if 'cursor.execute' in current_line:
                        in_sql = True
                    
                    if in_sql:
                        sql_lines.append(current_line)
                        
                        # 檢查是否結束
                        if ')' in current_line and not current_line.endswith(','):
                            break
                    
                    j += 1
                
                sql_text = ' '.join(sql_lines)
                
                queries.append({
                    'file': file_path,
                    'line': i + 1,
                    'sql': sql_text,
                    'context': lines[max(0, i-2):i+3]
                })
    
    except Exception as e:
        print(f"讀取檔案 {file_path} 時發生錯誤: {e}")
    
    return queries

def check_sql_injection_risk(query_info: Dict[str, Any]) -> Dict[str, Any]:
    """檢查 SQL 查詢的注入風險"""
    sql = query_info['sql']
    risks = []
    risk_level = 'low'
    
    # 檢查字串拼接
    if any(pattern in sql for pattern in [' + ', '.format(', 'f"', "f'"]):
        risks.append('使用字串拼接構建SQL查詢')
        risk_level = 'high'
    
    # 檢查 % 格式化
    if '%s' in sql or '%d' in sql:
        risks.append('使用%格式化構建SQL查詢')
        risk_level = 'high'
    
    # 檢查是否有參數化查詢標記
    has_params = '?' in sql or any(param in sql for param in ['%s', ':'])
    
    # 檢查危險關鍵字
    dangerous_keywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'EXEC']
    sql_upper = sql.upper()
    found_dangerous = [kw for kw in dangerous_keywords if kw in sql_upper]
    
    if found_dangerous:
        risks.append(f'包含危險關鍵字: {", ".join(found_dangerous)}')
        if risk_level == 'low':
            risk_level = 'medium'
    
    # 檢查動態表名或欄位名
    if 'f"' in sql or "f'" in sql:
        risks.append('可能包含動態表名或欄位名')
        risk_level = 'high'
    
    # 檢查是否使用參數化查詢
    if not has_params and any(quote in sql for quote in ['"', "'"]):
        if 'SELECT' in sql_upper or 'INSERT' in sql_upper or 'UPDATE' in sql_upper:
            risks.append('可能未使用參數化查詢')
            if risk_level == 'low':
                risk_level = 'medium'
    
    return {
        'file': query_info['file'],
        'line': query_info['line'],
        'sql': sql,
        'risk_level': risk_level,
        'risks': risks,
        'context': query_info['context']
    }

def generate_report(results: List[Dict[str, Any]]) -> str:
    """生成檢查報告"""
    high_risk = [r for r in results if r['risk_level'] == 'high']
    medium_risk = [r for r in results if r['risk_level'] == 'medium']
    low_risk = [r for r in results if r['risk_level'] == 'low']
    
    report = []
    report.append("# SQL 安全檢查報告")
    report.append(f"檢查時間: {__import__('datetime').datetime.now()}")
    report.append("")
    report.append("## 摘要")
    report.append(f"- 總查詢數: {len(results)}")
    report.append(f"- 高風險: {len(high_risk)}")
    report.append(f"- 中風險: {len(medium_risk)}")
    report.append(f"- 低風險: {len(low_risk)}")
    report.append("")
    
    if high_risk:
        report.append("## 🔴 高風險查詢 (需要立即修復)")
        for i, risk in enumerate(high_risk, 1):
            report.append(f"### {i}. {os.path.basename(risk['file'])}:{risk['line']}")
            report.append(f"**風險**: {', '.join(risk['risks'])}")
            report.append("```sql")
            report.append(risk['sql'])
            report.append("```")
            report.append("")
    
    if medium_risk:
        report.append("## 🟡 中風險查詢 (建議修復)")
        for i, risk in enumerate(medium_risk, 1):
            report.append(f"### {i}. {os.path.basename(risk['file'])}:{risk['line']}")
            report.append(f"**風險**: {', '.join(risk['risks'])}")
            report.append("```sql")
            report.append(risk['sql'])
            report.append("```")
            report.append("")
    
    report.append("## 修復建議")
    report.append("1. 使用參數化查詢 (cursor.execute(sql, params))")
    report.append("2. 避免字串拼接構建SQL")
    report.append("3. 驗證和清理所有用戶輸入")
    report.append("4. 使用白名單驗證動態表名和欄位名")
    report.append("5. 實作最小權限原則")
    
    return '\n'.join(report)

def main():
    """主函數"""
    print("🔍 SQL 安全檢查工具")
    print("=" * 50)
    
    # 檢查 backend 目錄
    backend_dir = 'backend' if os.path.exists('backend') else '.'
    
    print(f"掃描目錄: {backend_dir}")
    
    # 查找所有 Python 檔案
    python_files = find_python_files(backend_dir)
    print(f"找到 {len(python_files)} 個 Python 檔案")
    
    # 提取和檢查 SQL 查詢
    all_results = []
    
    for file_path in python_files:
        queries = extract_sql_queries(file_path)
        for query in queries:
            result = check_sql_injection_risk(query)
            all_results.append(result)
    
    print(f"找到 {len(all_results)} 個 SQL 查詢")
    
    # 生成報告
    report = generate_report(all_results)
    
    # 儲存報告
    report_file = 'SQL_Security_Check_Report.md'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"報告已儲存至: {report_file}")
    
    # 顯示摘要
    high_risk_count = len([r for r in all_results if r['risk_level'] == 'high'])
    medium_risk_count = len([r for r in all_results if r['risk_level'] == 'medium'])
    
    if high_risk_count > 0:
        print(f"⚠️  發現 {high_risk_count} 個高風險查詢，需要立即修復！")
    if medium_risk_count > 0:
        print(f"⚠️  發現 {medium_risk_count} 個中風險查詢，建議修復")
    
    if high_risk_count == 0 and medium_risk_count == 0:
        print("✅ 未發現明顯的 SQL 注入風險")
    
    return high_risk_count == 0 and medium_risk_count == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
