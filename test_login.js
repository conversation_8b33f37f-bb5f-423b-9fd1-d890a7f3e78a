// 測試登錄功能的腳本
const puppeteer = require('puppeteer');

async function testLogin() {
    const browser = await puppeteer.launch({ 
        headless: false,
        devtools: true 
    });
    
    const page = await browser.newPage();
    
    // 監聽控制台消息
    page.on('console', msg => {
        console.log('CONSOLE:', msg.type(), msg.text());
    });
    
    // 監聽網絡請求
    page.on('request', request => {
        if (request.url().includes('/api/login')) {
            console.log('LOGIN REQUEST:', request.url(), request.postData());
        }
    });
    
    // 監聽響應
    page.on('response', response => {
        if (response.url().includes('/api/login')) {
            console.log('LOGIN RESPONSE:', response.status(), response.url());
        }
    });
    
    try {
        // 訪問登錄頁面
        console.log('訪問登錄頁面...');
        await page.goto('http://localhost:7075/admin/login', { 
            waitUntil: 'networkidle2' 
        });
        
        // 等待頁面載入
        await page.waitForSelector('input[type="text"]', { timeout: 10000 });
        
        // 填寫登錄信息
        console.log('填寫登錄信息...');
        await page.type('input[type="text"]', 'admin');
        await page.type('input[type="password"]', 'admin123');
        
        // 點擊登錄按鈕
        console.log('點擊登錄按鈕...');
        await page.click('button[type="submit"]');
        
        // 等待響應
        await page.waitForTimeout(3000);
        
        // 檢查是否跳轉到管理後台
        const currentUrl = page.url();
        console.log('當前URL:', currentUrl);
        
        if (currentUrl.includes('/admin') && !currentUrl.includes('/login')) {
            console.log('✅ 登錄成功！');
        } else {
            console.log('❌ 登錄失敗，仍在登錄頁面');
        }
        
    } catch (error) {
        console.error('測試過程中發生錯誤:', error);
    }
    
    // 保持瀏覽器開啟以便檢查
    console.log('瀏覽器將保持開啟，請手動檢查...');
    // await browser.close();
}

testLogin().catch(console.error);
