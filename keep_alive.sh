#!/bin/bash

# Han AttendanceOS 服務監控腳本
# 確保後端和前端服務永遠運行

echo "🔄 啟動服務監控..."

while true; do
    # 檢查後端服務
    if ! curl -s http://localhost:7072/api/health > /dev/null 2>&1; then
        echo "❌ 後端服務停止，正在重啟..."
        pkill -f "python app.py" 2>/dev/null
        cd /Users/<USER>/2024newdev/attend_next/backend
        nohup python app.py > backend.log 2>&1 &
        echo "✅ 後端服務已重啟"
        sleep 10
    fi
    
    # 檢查前端服務
    if ! curl -s http://localhost:7075 > /dev/null 2>&1; then
        echo "❌ 前端服務停止，正在重啟..."
        pkill -f "next dev" 2>/dev/null
        cd /Users/<USER>/2024newdev/attend_next/frontend
        nohup npm run dev > frontend.log 2>&1 &
        echo "✅ 前端服務已重啟"
        sleep 15
    fi
    
    # 每30秒檢查一次
    sleep 30
done
